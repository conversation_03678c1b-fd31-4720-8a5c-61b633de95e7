/**
 * Notification Controller
 * Handles HTTP requests for notification management
 */

const NotificationService = require('./service');
const { successResponse, errorResponse } = require('../../utils/response');
const logger = require('../../utils/logger');

class NotificationController {
  /**
   * Create a new notification
   * POST /api/notifications
   */
  static async createNotification(req, res, next) {
    try {
      const notificationData = req.body;
      const userId = req.user.id;

      const notification = await NotificationService.createNotification(notificationData, userId);

      logger.info('Notification created successfully', {
        notificationId: notification.id,
        userId: req.user.id
      });

      return successResponse(res, notification, 'Notification created successfully', 201);
    } catch (error) {
      logger.error('Create notification failed', {
        error: error.message,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Get all notifications with pagination and filtering
   * GET /api/notifications
   */
  static async getAllNotifications(req, res, next) {
    try {
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        recipientId: req.query.recipientId,
        type: req.query.type,
        status: req.query.status,
        channel: req.query.channel,
        priority: req.query.priority,
        category: req.query.category,
        isRead: req.query.isRead === 'true' ? true : req.query.isRead === 'false' ? false : undefined,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        search: req.query.search
      };

      const result = await NotificationService.getAllNotifications(options);

      logger.info('Notifications retrieved successfully', {
        userId: req.user.id,
        count: result.notifications.length,
        page: options.page
      });

      return successResponse(res, result.notifications, 'Notifications retrieved successfully', 200, {
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Get all notifications failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get notification by ID
   * GET /api/notifications/:id
   */
  static async getNotificationById(req, res, next) {
    try {
      const { id } = req.params;
      const notification = await NotificationService.getNotificationById(parseInt(id));

      logger.info('Notification retrieved successfully', {
        notificationId: id,
        userId: req.user.id
      });

      return successResponse(res, notification, 'Notification retrieved successfully');
    } catch (error) {
      logger.error('Get notification by ID failed', {
        error: error.message,
        notificationId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Get notifications for current user
   * GET /api/notifications/my
   */
  static async getMyNotifications(req, res, next) {
    try {
      const userId = req.user.id;
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        type: req.query.type,
        status: req.query.status,
        isRead: req.query.isRead === 'true' ? true : req.query.isRead === 'false' ? false : undefined,
        priority: req.query.priority
      };

      const result = await NotificationService.getUserNotifications(userId, options);

      logger.info('User notifications retrieved successfully', {
        userId: req.user.id,
        count: result.notifications.length,
        page: options.page
      });

      return successResponse(res, result.notifications, 'User notifications retrieved successfully', 200, {
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Get user notifications failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Mark notification as read
   * PATCH /api/notifications/:id/read
   */
  static async markAsRead(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const notification = await NotificationService.markAsRead(parseInt(id), userId);

      logger.info('Notification marked as read successfully', {
        notificationId: id,
        userId: req.user.id
      });

      return successResponse(res, notification, 'Notification marked as read successfully');
    } catch (error) {
      logger.error('Mark notification as read failed', {
        error: error.message,
        notificationId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Mark all notifications as read for current user
   * PATCH /api/notifications/mark-all-read
   */
  static async markAllAsRead(req, res, next) {
    try {
      const userId = req.user.id;

      const result = await NotificationService.markAllAsRead(userId);

      logger.info('All notifications marked as read successfully', {
        userId: req.user.id,
        updatedCount: result.updatedCount
      });

      return successResponse(res, result, 'All notifications marked as read successfully');
    } catch (error) {
      logger.error('Mark all notifications as read failed', {
        error: error.message,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Update notification
   * PUT /api/notifications/:id
   */
  static async updateNotification(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      const notification = await NotificationService.updateNotification(parseInt(id), updateData, userId);

      logger.info('Notification updated successfully', {
        notificationId: id,
        userId: req.user.id
      });

      return successResponse(res, notification, 'Notification updated successfully');
    } catch (error) {
      logger.error('Update notification failed', {
        error: error.message,
        notificationId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Send notification
   * PATCH /api/notifications/:id/send
   */
  static async sendNotification(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const notification = await NotificationService.sendNotification(parseInt(id), userId);

      logger.info('Notification sent successfully', {
        notificationId: id,
        userId: req.user.id
      });

      return successResponse(res, notification, 'Notification sent successfully');
    } catch (error) {
      logger.error('Send notification failed', {
        error: error.message,
        notificationId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Get notification statistics
   * GET /api/notifications/stats
   */
  static async getNotificationStats(req, res, next) {
    try {
      const options = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        recipientId: req.query.recipientId
      };

      const stats = await NotificationService.getNotificationStats(options);

      logger.info('Notification statistics retrieved successfully', {
        userId: req.user.id,
        options
      });

      return successResponse(res, stats, 'Notification statistics retrieved successfully');
    } catch (error) {
      logger.error('Get notification stats failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Delete notification
   * DELETE /api/notifications/:id
   */
  static async deleteNotification(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const result = await NotificationService.deleteNotification(parseInt(id), userId);

      logger.info('Notification deleted successfully', {
        notificationId: id,
        userId: req.user.id
      });

      return successResponse(res, result, 'Notification deleted successfully');
    } catch (error) {
      logger.error('Delete notification failed', {
        error: error.message,
        notificationId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }
}

module.exports = NotificationController;
