/**
 * Test API Fix - Verify API URL Configuration
 * This script tests the API endpoint configuration fix
 */

const axios = require('axios');

// Load environment variables
require('dotenv').config();

// Test configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
const REGISTER_ENDPOINT = '/auth/register';
const FULL_URL = API_BASE_URL + REGISTER_ENDPOINT;

console.log('🔧 Testing API Configuration Fix');
console.log('================================');
console.log(`Environment API URL: ${process.env.NEXT_PUBLIC_API_URL}`);
console.log(`Computed Base URL: ${API_BASE_URL}`);
console.log(`Register Endpoint: ${REGISTER_ENDPOINT}`);
console.log(`Full Register URL: ${FULL_URL}`);
console.log('');

// Test 1: Check if API base endpoint is accessible
async function testApiBase() {
  try {
    console.log('📡 Test 1: Checking API base endpoint...');
    const response = await axios.get('http://localhost:3000/api');
    console.log('✅ API base endpoint accessible');
    console.log(`   Response: ${response.data.name} v${response.data.version}`);
    return true;
  } catch (error) {
    console.log('❌ API base endpoint not accessible');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 2: Check register endpoint with minimal data
async function testRegisterEndpoint() {
  try {
    console.log('📡 Test 2: Testing register endpoint...');
    
    // This should fail with validation error (not 404)
    const response = await axios.post(FULL_URL, {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      phone: '+84123456789', // Add required phone field
      role: 'customer'
    });
    
    console.log('✅ Register endpoint accessible (unexpected success)');
    console.log(`   Response: ${response.data.message}`);
    return true;
  } catch (error) {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        console.log('❌ Register endpoint returns 404 - URL is wrong');
        console.log(`   Status: ${status}`);
        console.log(`   URL: ${FULL_URL}`);
        return false;
      } else if (status === 400 || status === 422) {
        console.log('✅ Register endpoint accessible (validation error expected)');
        console.log(`   Status: ${status}`);
        console.log(`   Error: ${data.error?.message || data.message}`);
        return true;
      } else {
        console.log(`⚠️  Register endpoint returns status ${status}`);
        console.log(`   Error: ${data.error?.message || data.message}`);
        return true;
      }
    } else {
      console.log('❌ Network error accessing register endpoint');
      console.log(`   Error: ${error.message}`);
      return false;
    }
  }
}

// Test 3: Test the old wrong URL to confirm it fails
async function testOldWrongUrl() {
  try {
    console.log('📡 Test 3: Testing old wrong URL (should fail)...');
    const wrongUrl = 'http://localhost:3000/auth/register';
    
    await axios.post(wrongUrl, {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      phone: '+84123456789',
      role: 'customer'
    });
    
    console.log('❌ Old wrong URL unexpectedly works');
    return false;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Old wrong URL correctly returns 404');
      console.log(`   URL: http://localhost:3000/auth/register`);
      return true;
    } else {
      console.log('⚠️  Old wrong URL returns unexpected error');
      console.log(`   Status: ${error.response?.status}`);
      return false;
    }
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting API Configuration Tests...\n');
  
  const test1 = await testApiBase();
  console.log('');
  
  const test2 = await testRegisterEndpoint();
  console.log('');
  
  const test3 = await testOldWrongUrl();
  console.log('');
  
  console.log('📊 Test Results Summary');
  console.log('======================');
  console.log(`API Base Accessible: ${test1 ? '✅' : '❌'}`);
  console.log(`Register Endpoint: ${test2 ? '✅' : '❌'}`);
  console.log(`Old URL Blocked: ${test3 ? '✅' : '❌'}`);
  
  const allPassed = test1 && test2 && test3;
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ API Fix Successful' : '❌ API Fix Failed'}`);
  
  if (allPassed) {
    console.log('\n🎉 The API URL configuration has been fixed!');
    console.log('   Frontend will now correctly call: http://localhost:3000/api/auth/register');
    console.log('   Instead of the wrong URL: http://localhost:3000/auth/register');
  } else {
    console.log('\n🔧 API configuration still needs attention.');
  }
}

// Execute tests
runTests().catch(console.error);
