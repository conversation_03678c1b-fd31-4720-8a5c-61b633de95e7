/**
 * Pagination Utilities
 * Helper functions for database pagination
 */

const { Op } = require('sequelize');

/**
 * Paginate Sequelize query results
 * @param {Object} model - Sequelize model
 * @param {Object} whereClause - Where conditions
 * @param {Object} options - Pagination and query options
 * @returns {Promise<Object>} Paginated results
 */
const paginate = async (model, whereClause = {}, options = {}) => {
  const {
    page = 1,
    limit = 10,
    include = [],
    attributes,
    order = [['createdAt', 'DESC']],
    distinct = false,
    group = null
  } = options;

  // Validate pagination parameters
  const validatedPage = Math.max(1, parseInt(page));
  const validatedLimit = Math.min(100, Math.max(1, parseInt(limit)));
  
  // Calculate offset
  const offset = (validatedPage - 1) * validatedLimit;

  // Build query options
  const queryOptions = {
    where: whereClause,
    limit: validatedLimit,
    offset,
    distinct,
    order
  };

  // Add includes if provided
  if (include.length > 0) {
    queryOptions.include = include;
  }

  // Add attributes if provided
  if (attributes) {
    queryOptions.attributes = attributes;
  }

  // Add group if provided
  if (group) {
    queryOptions.group = group;
  }

  try {
    // Execute query with count
    const { count, rows } = await model.findAndCountAll(queryOptions);
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(count / validatedLimit);
    
    return {
      data: rows,
      pagination: {
        page: validatedPage,
        limit: validatedLimit,
        total: count,
        totalPages,
        hasNext: validatedPage < totalPages,
        hasPrev: validatedPage > 1,
        nextPage: validatedPage < totalPages ? validatedPage + 1 : null,
        prevPage: validatedPage > 1 ? validatedPage - 1 : null
      }
    };
  } catch (error) {
    throw new Error(`Pagination error: ${error.message}`);
  }
};

/**
 * Build pagination parameters from request query
 * @param {Object} query - Request query object
 * @returns {Object} Pagination parameters
 */
const getPaginationParams = (query) => {
  const page = parseInt(query.page) || 1;
  const limit = parseInt(query.limit) || 10;
  
  return {
    page: Math.max(1, page),
    limit: Math.min(100, Math.max(1, limit))
  };
};

/**
 * Build sort order from request query
 * @param {string} sortQuery - Sort query string (e.g., "name:asc,createdAt:desc")
 * @param {Array} allowedFields - Allowed fields for sorting
 * @param {Array} defaultOrder - Default order if no sort provided
 * @returns {Array} Sequelize order array
 */
const buildSortOrder = (sortQuery, allowedFields = [], defaultOrder = [['createdAt', 'DESC']]) => {
  if (!sortQuery) {
    return defaultOrder;
  }

  const sortPairs = sortQuery.split(',');
  const order = [];

  for (const pair of sortPairs) {
    const [field, direction = 'ASC'] = pair.split(':');
    
    // Validate field
    if (allowedFields.length > 0 && !allowedFields.includes(field)) {
      continue;
    }
    
    // Validate direction
    const validDirection = ['ASC', 'DESC'].includes(direction.toUpperCase()) 
      ? direction.toUpperCase() 
      : 'ASC';
    
    order.push([field, validDirection]);
  }

  return order.length > 0 ? order : defaultOrder;
};

/**
 * Build search conditions for multiple fields
 * @param {string} searchTerm - Search term
 * @param {Array} searchFields - Fields to search in
 * @returns {Object} Sequelize where clause
 */
const buildSearchConditions = (searchTerm, searchFields = []) => {
  if (!searchTerm || searchFields.length === 0) {
    return {};
  }

  const searchConditions = searchFields.map(field => ({
    [field]: {
      [Op.like]: `%${searchTerm}%`
    }
  }));

  return {
    [Op.or]: searchConditions
  };
};

/**
 * Build date range filter
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @param {string} field - Date field name
 * @returns {Object} Sequelize where clause
 */
const buildDateRangeFilter = (startDate, endDate, field = 'createdAt') => {
  const conditions = {};

  if (startDate && endDate) {
    conditions[field] = {
      [Op.between]: [
        new Date(startDate + ' 00:00:00'),
        new Date(endDate + ' 23:59:59')
      ]
    };
  } else if (startDate) {
    conditions[field] = {
      [Op.gte]: new Date(startDate + ' 00:00:00')
    };
  } else if (endDate) {
    conditions[field] = {
      [Op.lte]: new Date(endDate + ' 23:59:59')
    };
  }

  return conditions;
};

/**
 * Build filter conditions from query parameters
 * @param {Object} query - Request query object
 * @param {Object} filterConfig - Filter configuration
 * @returns {Object} Sequelize where clause
 */
const buildFilterConditions = (query, filterConfig = {}) => {
  const conditions = {};
  
  Object.keys(filterConfig).forEach(key => {
    const value = query[key];
    if (value === undefined || value === null || value === '') {
      return;
    }

    const config = filterConfig[key];
    const field = config.field || key;
    const operator = config.operator || 'eq';

    switch (operator) {
      case 'eq':
        conditions[field] = value;
        break;
      case 'ne':
        conditions[field] = { [Op.ne]: value };
        break;
      case 'gt':
        conditions[field] = { [Op.gt]: value };
        break;
      case 'gte':
        conditions[field] = { [Op.gte]: value };
        break;
      case 'lt':
        conditions[field] = { [Op.lt]: value };
        break;
      case 'lte':
        conditions[field] = { [Op.lte]: value };
        break;
      case 'like':
        conditions[field] = { [Op.like]: `%${value}%` };
        break;
      case 'ilike':
        conditions[field] = { [Op.iLike]: `%${value}%` };
        break;
      case 'in':
        const values = Array.isArray(value) ? value : value.split(',');
        conditions[field] = { [Op.in]: values };
        break;
      case 'notIn':
        const notValues = Array.isArray(value) ? value : value.split(',');
        conditions[field] = { [Op.notIn]: notValues };
        break;
      case 'between':
        if (Array.isArray(value) && value.length === 2) {
          conditions[field] = { [Op.between]: value };
        }
        break;
      default:
        conditions[field] = value;
    }
  });

  return conditions;
};

/**
 * Create cursor-based pagination (for large datasets)
 * @param {Object} model - Sequelize model
 * @param {Object} options - Pagination options
 * @returns {Promise<Object>} Cursor paginated results
 */
const cursorPaginate = async (model, options = {}) => {
  const {
    cursor,
    limit = 10,
    cursorField = 'id',
    direction = 'forward', // 'forward' or 'backward'
    where = {},
    include = [],
    attributes,
    order = [[cursorField, 'ASC']]
  } = options;

  const validatedLimit = Math.min(100, Math.max(1, parseInt(limit)));
  
  // Build where clause with cursor
  const whereClause = { ...where };
  
  if (cursor) {
    const operator = direction === 'forward' ? Op.gt : Op.lt;
    whereClause[cursorField] = { [operator]: cursor };
  }

  // Build query options
  const queryOptions = {
    where: whereClause,
    limit: validatedLimit + 1, // Get one extra to check if there's more
    order,
    include,
    attributes
  };

  try {
    const results = await model.findAll(queryOptions);
    const hasMore = results.length > validatedLimit;
    
    // Remove the extra record if it exists
    if (hasMore) {
      results.pop();
    }

    // Get next cursor
    const nextCursor = results.length > 0 
      ? results[results.length - 1][cursorField] 
      : null;

    return {
      data: results,
      pagination: {
        hasMore,
        nextCursor,
        limit: validatedLimit
      }
    };
  } catch (error) {
    throw new Error(`Cursor pagination error: ${error.message}`);
  }
};

module.exports = {
  paginate,
  getPaginationParams,
  buildSortOrder,
  buildSearchConditions,
  buildDateRangeFilter,
  buildFilterConditions,
  cursorPaginate
};
