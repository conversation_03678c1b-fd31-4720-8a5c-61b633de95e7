'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import servicesService from '@/services/servicesService';
import branchesService from '@/services/branchesService';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Plus, 
  Edit, 
  Settings, 
  Star,
  Clock,
  DollarSign,
  X,
  Check,
  Loader2,
  AlertCircle,
  Trash2,
  Tags,
  Users,
  CheckCircle,
  XCircle,
  StarOff,
  MapPin,
  Image,
  Info
} from 'lucide-react';

export default function ServicesPage() {
  // Auth context
  const { user } = useAuth();

  // State management
  const [services, setServices] = useState([]);
  const [branches, setBranches] = useState([]);
  const [selectedBranchId, setSelectedBranchId] = useState(''); // '' means all branches
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalClosing, setIsModalClosing] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [notification, setNotification] = useState(null);
  
  // Service categories from backend
  const serviceCategories = [
    { key: 'massage', label: 'Massage' },
    { key: 'facial', label: 'Facial' },
    { key: 'body_treatment', label: 'Body Treatment' },
    { key: 'nail_care', label: 'Nail Care' },
    { key: 'hair_care', label: 'Hair Care' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'package', label: 'Package' }
  ];

  // Gender restrictions
  const genderRestrictions = [
    { key: 'male', label: 'Male Only' },
    { key: 'female', label: 'Female Only' }
  ];

  // Form data matching backend API structure
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'massage',
    duration: 60,
    price: 0,
    discountPrice: '',
    branchId: '',
    minAge: '',
    maxAge: '',
    genderRestriction: '',
    bookingAdvanceDays: 30,
    cancellationDeadlineHours: 24,
    maxBookingsPerDay: '',
    sortOrder: 0,
    benefits: [],
    requirements: [],
    images: [],
    preparationInstructions: '',
    aftercareInstructions: ''
  });

  // Load services and branches on component mount
  useEffect(() => {
    loadServices();
    loadBranches();
  }, []);

  // Reload services when branch filter changes
  useEffect(() => {
    if (branches.length > 0) { // Only reload after branches are loaded
      loadServices();
    }
  }, [selectedBranchId]);

  // Show notification function
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type, isVisible: false });
    
    // Trigger animation after a brief delay
    setTimeout(() => {
      setNotification(prev => prev ? { ...prev, isVisible: true } : null);
    }, 100);
    
    // Auto hide after 6 seconds (increased from 4 seconds)
    setTimeout(() => {
      setNotification(prev => prev ? { ...prev, isVisible: false } : null);
    }, 6000);
    
    // Remove notification after fade out
    setTimeout(() => {
      setNotification(null);
    }, 6500);
  };

  // Load services from API
  const loadServices = async () => {
    try {
      setIsLoading(true);
      const params = {
        page: 1,
        limit: 50,
        sort: 'sortOrder,name'
      };
      
      // Add branch filter if selected
      if (selectedBranchId) {
        params.branchId = selectedBranchId;
      }
      
      const response = await servicesService.getServices(params);
      console.log('Services response:', response);
      setServices(response.data || []);
      setErrors({});
    } catch (error) {
      console.error('Failed to load services:', error);
      showNotification(error.message || 'Failed to load services. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };
  // Handle branch filter change
  const handleBranchChange = (e) => {
    setSelectedBranchId(e.target.value);
  };

  // Load branches for dropdown
  const loadBranches = async () => {
    try {
      const response = await branchesService.getActiveBranches(100);
      setBranches(response.data || []);
    } catch (error) {
      console.error('Failed to load branches:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? '' : parseFloat(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear field-specific errors
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle array input changes (benefits, requirements)
  const handleArrayInputChange = (fieldName, index, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].map((item, i) => i === index ? value : item)
    }));
  };

  // Add array item
  const addArrayItem = (fieldName) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: [...prev[fieldName], '']
    }));
  };

  // Remove array item
  const removeArrayItem = (fieldName, index) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].filter((_, i) => i !== index)
    }));
  };

  // Open modal for create/edit
  const openModal = (service = null) => {
    if (service) {
      setEditingService(service);
      setFormData({
        name: service.name || '',
        description: service.description || '',
        category: service.category || 'massage',
        duration: service.duration || 60,
        price: service.price || 0,
        discountPrice: service.discountPrice || '',
        branchId: service.branchId || '',
        minAge: service.minAge || '',
        maxAge: service.maxAge || '',
        genderRestriction: service.genderRestriction || '',
        bookingAdvanceDays: service.bookingAdvanceDays || 30,
        cancellationDeadlineHours: service.cancellationDeadlineHours || 24,
        maxBookingsPerDay: service.maxBookingsPerDay || '',
        sortOrder: service.sortOrder || 0,
        benefits: Array.isArray(service.benefits) ? service.benefits : [],
        requirements: Array.isArray(service.requirements) ? service.requirements : [],
        images: Array.isArray(service.images) ? service.images : [],
        preparationInstructions: service.preparationInstructions || '',
        aftercareInstructions: service.aftercareInstructions || ''
      });
    } else {
      setEditingService(null);
      setFormData({
        name: '',
        description: '',
        category: 'massage',
        duration: 60,
        price: 0,
        discountPrice: '',
        branchId: '',
        minAge: '',
        maxAge: '',
        genderRestriction: '',
        bookingAdvanceDays: 30,
        cancellationDeadlineHours: 24,
        maxBookingsPerDay: '',
        sortOrder: 0,
        benefits: [],
        requirements: [],
        images: [],
        preparationInstructions: '',
        aftercareInstructions: ''
      });
    }
    setErrors({});
    setNotification(null);
    setIsModalOpen(true);
    setIsModalClosing(false);
  };

  // Close modal with smooth animation
  const closeModal = () => {
    setIsModalClosing(true);
    
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsModalOpen(false);
      setIsModalClosing(false);
      setEditingService(null);
      setErrors({});
      setNotification(null);
    }, 300); // Match the animation duration
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    }

    if (!formData.category) {
      newErrors.category = 'Service category is required';
    }

    if (!formData.duration || formData.duration < 15 || formData.duration > 480) {
      newErrors.duration = 'Duration must be between 15 and 480 minutes';
    }

    if (!formData.price || formData.price < 0) {
      newErrors.price = 'Price must be a positive number';
    }

    if (formData.discountPrice && (formData.discountPrice < 0 || formData.discountPrice >= formData.price)) {
      newErrors.discountPrice = 'Discount price must be less than regular price';
    }

    if (formData.minAge && (formData.minAge < 0 || formData.minAge > 100)) {
      newErrors.minAge = 'Minimum age must be between 0 and 100';
    }

    if (formData.maxAge && (formData.maxAge < 0 || formData.maxAge > 150)) {
      newErrors.maxAge = 'Maximum age must be between 0 and 150';
    }

    if (formData.minAge && formData.maxAge && formData.minAge > formData.maxAge) {
      newErrors.maxAge = 'Maximum age must be greater than minimum age';
    }

    if (formData.bookingAdvanceDays && (formData.bookingAdvanceDays < 0 || formData.bookingAdvanceDays > 365)) {
      newErrors.bookingAdvanceDays = 'Booking advance days must be between 0 and 365';
    }

    if (formData.cancellationDeadlineHours && (formData.cancellationDeadlineHours < 0 || formData.cancellationDeadlineHours > 168)) {
      newErrors.cancellationDeadlineHours = 'Cancellation deadline must be between 0 and 168 hours';
    }

    if (formData.maxBookingsPerDay && (formData.maxBookingsPerDay < 1 || formData.maxBookingsPerDay > 100)) {
      newErrors.maxBookingsPerDay = 'Max bookings per day must be between 1 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Clean form data before sending to API
  const cleanFormData = (data) => {
    const cleaned = { ...data };
    
    // Handle optional text fields - remove if empty
    const optionalTextFields = ['description', 'genderRestriction', 'preparationInstructions', 'aftercareInstructions'];
    optionalTextFields.forEach(field => {
      if (cleaned[field] === '' || cleaned[field] === null || cleaned[field] === undefined) {
        delete cleaned[field];
      }
    });

    // Handle optional numeric fields - parse and validate, or remove if empty
    const numericFields = [
      { name: 'discountPrice', parser: parseFloat },
      { name: 'branchId', parser: parseInt },
      { name: 'minAge', parser: parseInt },
      { name: 'maxAge', parser: parseInt },
      { name: 'maxBookingsPerDay', parser: parseInt }
    ];

    numericFields.forEach(({ name, parser }) => {
      if (cleaned[name] === '' || cleaned[name] === null || cleaned[name] === undefined) {
        // Remove empty optional fields instead of sending null
        delete cleaned[name];
      } else if (cleaned[name] !== '') {
        const parsed = parser(cleaned[name]);
        if (isNaN(parsed)) {
          // Remove invalid numbers
          delete cleaned[name];
        } else {
          cleaned[name] = parsed;
        }
      }
    });

    // Ensure required numeric fields are properly formatted
    if (cleaned.duration !== undefined && cleaned.duration !== null) {
      cleaned.duration = parseInt(cleaned.duration);
    }
    if (cleaned.price !== undefined && cleaned.price !== null) {
      cleaned.price = parseFloat(cleaned.price);
    }
    if (cleaned.bookingAdvanceDays !== undefined && cleaned.bookingAdvanceDays !== null) {
      cleaned.bookingAdvanceDays = parseInt(cleaned.bookingAdvanceDays);
    }
    if (cleaned.cancellationDeadlineHours !== undefined && cleaned.cancellationDeadlineHours !== null) {
      cleaned.cancellationDeadlineHours = parseInt(cleaned.cancellationDeadlineHours);
    }
    if (cleaned.sortOrder !== undefined && cleaned.sortOrder !== null) {
      cleaned.sortOrder = parseInt(cleaned.sortOrder);
    }

    // Ensure arrays are properly formatted and filter out empty strings
    cleaned.benefits = Array.isArray(cleaned.benefits) ? cleaned.benefits.filter(benefit => benefit.trim() !== '') : [];
    cleaned.requirements = Array.isArray(cleaned.requirements) ? cleaned.requirements.filter(req => req.trim() !== '') : [];
    cleaned.images = Array.isArray(cleaned.images) ? cleaned.images.filter(img => img.trim() !== '') : [];

    // Debug log to see what's being sent
    console.log('Cleaned form data:', cleaned);

    return cleaned;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Clean form data before sending
      const cleanedData = cleanFormData(formData);
      let response;
      
      if (editingService) {
        // Update existing service
        console.log('Updating service with data:', cleanedData);
        response = await servicesService.updateService(editingService.id, cleanedData);
        
        // Update local state
        setServices(prev => prev.map(service => 
          service.id === editingService.id 
            ? { ...service, ...response.data }
            : service
        ));
        
        // Close modal first
        closeModal();
        
        // Then show notification after modal closes
        setTimeout(() => {
          showNotification(`Service "${formData.name}" updated successfully!`, 'success');
        }, 300);
      } else {
        // Create new service
        response = await servicesService.createService(cleanedData);
        
        // Add to local state
        setServices(prev => [...prev, response.data]);
        
        // Close modal first
        closeModal();
        
        // Then show notification after modal closes
        setTimeout(() => {
          showNotification(`Service "${formData.name}" created successfully!`, 'success');
        }, 300);
      }

    } catch (error) {
      console.error('Submit error:', error);
      console.error('Error response:', error.response?.data);

      // Handle backend validation errors
      if (error.response?.data?.error?.details) {
        const newErrors = {};
        error.response.data.error.details.forEach(detail => {
          if (detail.field) {
            newErrors[detail.field] = detail.message;
          }
        });
        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors);
        } else {
          setErrors({
            submit: error.response.data.error.message || 'Validation failed. Please check your input.'
          });
        }
      } else if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        // Handle express-validator errors
        const newErrors = {};
        error.response.data.errors.forEach(err => {
          if (err.path || err.param) {
            const field = err.path || err.param;
            newErrors[field] = err.msg || err.message;
          }
        });
        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors);
        } else {
          setErrors({
            submit: 'Validation failed. Please check your input.'
          });
        }
      } else if (error.errors && error.errors.length > 0) {
        // Handle validation errors from service (old format)
        const newErrors = {};
        error.errors.forEach(err => {
          if (err.path) {
            newErrors[err.path] = err.msg;
          }
        });
        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors);
        } else {
          setErrors({
            submit: error.message || 'Validation failed. Please check your input.'
          });
        }
      } else {
        // General error
        setErrors({
          submit: error.message || error.response?.data?.message || 'Failed to save service. Please try again.'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete service
  const handleDelete = async (serviceId) => {
    if (!confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
      return;
    }

    try {
      // Get service name before deletion
      const serviceName = services.find(s => s.id === serviceId)?.name || 'Service';
      
      await servicesService.deleteService(serviceId);
      
      // Remove from local state
      setServices(prev => prev.filter(service => service.id !== serviceId));
      
      showNotification(`"${serviceName}" deleted successfully!`, 'success');
    } catch (error) {
      console.error('Delete error:', error);
      showNotification(error.message || 'Failed to delete service. Please try again.', 'error');
    }
  };

  // Toggle service status
  const handleToggleStatus = async (serviceId, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await servicesService.toggleServiceStatus(serviceId, newStatus);
      
      // Update local state
      setServices(prev => prev.map(service => 
        service.id === serviceId 
          ? { ...service, isActive: newStatus }
          : service
      ));
      
      // Get service name for notification
      const serviceName = services.find(s => s.id === serviceId)?.name || 'Service';
      
      showNotification(`"${serviceName}" ${newStatus ? 'activated' : 'deactivated'} successfully!`, 'success');
    } catch (error) {
      console.error('Toggle status error:', error);
      showNotification(error.message || 'Failed to update service status. Please try again.', 'error');
    }
  };

  // Toggle service popularity
  const handleTogglePopularity = async (serviceId, currentPopularity) => {
    try {
      const newPopularity = !currentPopularity;
      await servicesService.toggleServicePopularity(serviceId, newPopularity);
      
      // Update local state
      setServices(prev => prev.map(service => 
        service.id === serviceId 
          ? { ...service, isPopular: newPopularity }
          : service
      ));
      
      // Get service name for notification
      const serviceName = services.find(s => s.id === serviceId)?.name || 'Service';
      
      showNotification(`"${serviceName}" ${newPopularity ? 'marked as popular' : 'unmarked as popular'} successfully!`, 'success');
    } catch (error) {
      console.error('Toggle popularity error:', error);
      showNotification(error.message || 'Failed to update service popularity. Please try again.', 'error');
    }
  };

  // Format price for display
  const formatPrice = (price) => {
    if (!price) return '0';
    return new Intl.NumberFormat('vi-VN').format(price);
  };

  // Get category label
  const getCategoryLabel = (category) => {
    const categoryObj = serviceCategories.find(cat => cat.key === category);
    return categoryObj ? categoryObj.label : category;
  };

  // Get status badge class
  const getStatusBadge = (isActive) => {
    return isActive ? 'badge-success' : 'badge-neutral';
  };

  // Check if user can manage services (admin and staff)
  const canManageServices = () => {
    return user?.role === 'admin' || user?.role === 'staff';
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-text">Your Services</h1>
              <p className="mt-2 text-neutral-600">
                Manage your spa services, pricing, and availability
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
              {/* Branch Filter */}
              <div className="min-w-[200px]">
                <select
                  value={selectedBranchId}
                  onChange={handleBranchChange}
                  className="form-input text-sm"
                  disabled={isLoading}
                >
                  <option value="">All Branches</option>
                  {branches.map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Add Service Button */}
              <button
                onClick={() => openModal()}
                className="btn-primary inline-flex items-center"
                disabled={isLoading}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Service
              </button>
            </div>
          </div>

          {/* Loading State */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
              <span className="ml-2 text-neutral-600">Loading services...</span>
            </div>
          ) : (
            <>
              {/* Services List */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {services.map((service) => (
                  <div key={service.id} className="card-hover">
                    <div className="card-body">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-text mb-1">
                            {service.name}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <span className={`badge ${getStatusBadge(service.isActive)}`}>
                              {service.isActive ? 'Active' : 'Inactive'}
                            </span>
                            {service.isPopular && (
                              <span className="badge badge-warning">
                                <Star className="h-3 w-3 mr-1" />
                                Popular
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => openModal(service)}
                            className="btn-ghost btn-sm p-2"
                            title="Edit service"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleToggleStatus(service.id, service.isActive)}
                            className="btn-ghost btn-sm p-2"
                            title={service.isActive ? 'Deactivate service' : 'Activate service'}
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleTogglePopularity(service.id, service.isPopular)}
                            className="btn-ghost btn-sm p-2"
                            title={service.isPopular ? 'Unmark as popular' : 'Mark as popular'}
                          >
                            {service.isPopular ? <StarOff className="h-4 w-4" /> : <Star className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => handleDelete(service.id)}
                            className="btn-ghost btn-sm p-2 text-red-600 hover:text-red-700"
                            title="Delete service"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div className="space-y-3 text-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-neutral-600">
                            <Tags className="h-4 w-4 mr-2" />
                            <span>{getCategoryLabel(service.category)}</span>
                          </div>
                          <div className="flex items-center text-neutral-600">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>{service.duration} min</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-neutral-600">
                            <DollarSign className="h-4 w-4 mr-2" />
                            <span className="font-medium">
                              {service.discountPrice ? (
                                <>
                                  <span className="line-through text-neutral-400 mr-2">
                                    {formatPrice(service.price)} VND
                                  </span>
                                  <span className="text-red-600">
                                    {formatPrice(service.discountPrice)} VND
                                  </span>
                                </>
                              ) : (
                                `${formatPrice(service.price)} VND`
                              )}
                            </span>
                          </div>
                          {service.branch && (
                            <div className="flex items-center text-neutral-600">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span className="text-xs">{service.branch.name}</span>
                            </div>
                          )}
                        </div>

                        {(service.minAge || service.maxAge || service.genderRestriction) && (
                          <div className="flex items-center text-neutral-600">
                            <Users className="h-4 w-4 mr-2" />
                            <span className="text-xs">
                              {service.genderRestriction && `${service.genderRestriction} only`}
                              {service.genderRestriction && (service.minAge || service.maxAge) && ', '}
                              {service.minAge && `${service.minAge}+`}
                              {service.minAge && service.maxAge && '-'}
                              {service.maxAge && service.minAge !== service.maxAge && `${service.maxAge} years`}
                            </span>
                          </div>
                        )}
                      </div>

                      {service.description && (
                        <div className="mt-4 pt-4 border-t border-neutral-200">
                          <p className="text-sm text-neutral-600 line-clamp-2">{service.description}</p>
                        </div>
                      )}

                      {Array.isArray(service.benefits) && service.benefits.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-neutral-200">
                          <p className="text-xs font-medium text-neutral-700 mb-1">Benefits:</p>
                          <div className="flex flex-wrap gap-1">
                            {service.benefits.slice(0, 3).map((benefit, index) => (
                              <span key={index} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                {benefit}
                              </span>
                            ))}
                            {service.benefits.length > 3 && (
                              <span className="text-xs text-neutral-500">
                                +{service.benefits.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Add Service Card */}
                {!isLoading && (
                  <div
                    onClick={() => openModal()}
                    className="card-hover cursor-pointer border-2 border-dashed border-neutral-300 hover:border-primary-300"
                  >
                    <div className="card-body text-center">
                      <div className="w-12 h-12 bg-neutral-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <Plus className="h-6 w-6 text-neutral-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-neutral-600 mb-2">
                        Add New Service
                      </h3>
                      <p className="text-sm text-neutral-500">
                        Create a new spa service offering
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Empty State */}
              {!isLoading && services.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-neutral-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Tags className="h-8 w-8 text-neutral-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-600 mb-2">
                    No services yet
                  </h3>
                  <p className="text-sm text-neutral-500 mb-6">
                    Get started by adding your first service offering
                  </p>
                  <button
                    onClick={() => openModal()}
                    className="btn-primary inline-flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Service
                  </button>
                </div>
              )}
            </>
          )}

          {/* Toast Notification */}
          {notification && (
            <div className="fixed top-6 right-6 z-50">
              <div className={`min-w-[420px] max-w-lg w-full shadow-xl rounded-xl pointer-events-auto backdrop-blur-sm overflow-hidden transform transition-all duration-700 ease-out hover:scale-105 ${
                notification.isVisible 
                  ? 'translate-x-0 opacity-100 scale-100' 
                  : 'translate-x-full opacity-0 scale-95'
              } ${
                notification.type === 'success' 
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200/60 shadow-green-100/50' 
                  : 'bg-gradient-to-r from-red-50 to-rose-50 border-2 border-red-200/60 shadow-red-100/50'
              }`}>
                {/* Progress bar */}
                <div className={`h-1 w-full ${
                  notification.type === 'success' ? 'bg-green-200' : 'bg-red-200'
                } relative overflow-hidden`}>
                  <div className={`h-full ${
                    notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'
                  } animate-pulse`}></div>
                </div>
                
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className={`p-2 rounded-full ${
                        notification.type === 'success' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        {notification.type === 'success' ? (
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        ) : (
                          <XCircle className="h-6 w-6 text-red-600" />
                        )}
                      </div>
                    </div>
                    <div className="ml-4 w-0 flex-1">
                      <p className={`text-base font-semibold ${
                        notification.type === 'success' ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {notification.type === 'success' ? '🎉 Success!' : '⚠️ Error!'}
                      </p>
                      <p className={`mt-2 text-sm leading-relaxed ${
                        notification.type === 'success' ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {notification.message}
                      </p>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                      <button
                        className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                          notification.type === 'success' 
                            ? 'text-green-400 hover:text-green-600 hover:bg-green-100/50 focus:ring-green-500' 
                            : 'text-red-400 hover:text-red-600 hover:bg-red-100/50 focus:ring-red-500'
                        } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                        onClick={() => setNotification(null)}
                      >
                        <span className="sr-only">Close</span>
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Service Modal */}
          {isModalOpen && (
            <div className={`modal-overlay flex items-center justify-center p-4 ${isModalClosing ? 'modal-closing' : ''}`}>
              <div className={`modal-form max-w-4xl w-full mx-4 ${isModalClosing ? 'modal-closing' : ''}`}>
                {/* Modal Header - Fixed */}
                <div className="flex items-center justify-between p-6 border-b border-neutral-200 bg-white rounded-t-xl">
                  <h2 className="text-xl font-semibold text-text">
                    {editingService ? 'Edit Service' : 'Add New Service'}
                  </h2>
                  <button
                    onClick={closeModal}
                    className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
                    disabled={isSubmitting}
                  >
                    <X className="h-5 w-5 text-neutral-500" />
                  </button>
                </div>

                {/* Modal Body - Scrollable */}
                <div className="modal-form-body modal-scroll">
                  <form onSubmit={handleSubmit} className="p-6 space-y-6">

                    {/* General Error */}
                    {errors.submit && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                          <span className="text-red-800">{errors.submit}</span>
                        </div>
                      </div>
                    )}

                    {/* Basic Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Basic Information</h3>

                      <div>
                        <label className="form-label">Service Name *</label>
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`form-input ${errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="e.g., Thai Massage"
                          required
                        />
                        {errors.name && (
                          <p className="form-error">{errors.name}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Description</label>
                        <textarea
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          className={`form-input ${errors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          rows="3"
                          placeholder="Describe your service offering..."
                        />
                        {errors.description && (
                          <p className="form-error">{errors.description}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Category *</label>
                          <select
                            name="category"
                            value={formData.category}
                            onChange={handleInputChange}
                            className={`form-input ${errors.category ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            required
                          >
                            {serviceCategories.map((category) => (
                              <option key={category.key} value={category.key}>
                                {category.label}
                              </option>
                            ))}
                          </select>
                          {errors.category && (
                            <p className="form-error">{errors.category}</p>
                          )}
                        </div>
                        <div>
                          <label className="form-label">Branch (Optional)</label>
                          <select
                            name="branchId"
                            value={formData.branchId}
                            onChange={handleInputChange}
                            className="form-input"
                          >
                            <option value="">All Branches</option>
                            {branches.map((branch) => (
                              <option key={branch.id} value={branch.id}>
                                {branch.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Duration (minutes) *</label>
                          <input
                            type="number"
                            name="duration"
                            value={formData.duration}
                            onChange={handleInputChange}
                            className={`form-input ${errors.duration ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="15"
                            max="480"
                            required
                          />
                          {errors.duration && (
                            <p className="form-error">{errors.duration}</p>
                          )}
                          <p className="form-help">Between 15 and 480 minutes</p>
                        </div>
                        <div>
                          <label className="form-label">Sort Order</label>
                          <input
                            type="number"
                            name="sortOrder"
                            value={formData.sortOrder}
                            onChange={handleInputChange}
                            className="form-input"
                            min="0"
                          />
                          <p className="form-help">Lower numbers appear first</p>
                        </div>
                      </div>
                    </div>

                    {/* Pricing */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Pricing</h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Regular Price (VND) *</label>
                          <input
                            type="number"
                            name="price"
                            value={formData.price}
                            onChange={handleInputChange}
                            className={`form-input ${errors.price ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            step="1000"
                            required
                          />
                          {errors.price && (
                            <p className="form-error">{errors.price}</p>
                          )}
                        </div>
                        <div>
                          <label className="form-label">Discount Price (VND)</label>
                          <input
                            type="number"
                            name="discountPrice"
                            value={formData.discountPrice}
                            onChange={handleInputChange}
                            className={`form-input ${errors.discountPrice ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            step="1000"
                          />
                          {errors.discountPrice && (
                            <p className="form-error">{errors.discountPrice}</p>
                          )}
                          <p className="form-help">Must be less than regular price</p>
                        </div>
                      </div>
                    </div>

                    {/* Restrictions */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Restrictions (Optional)</h3>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="form-label">Minimum Age</label>
                          <input
                            type="number"
                            name="minAge"
                            value={formData.minAge}
                            onChange={handleInputChange}
                            className={`form-input ${errors.minAge ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            max="100"
                          />
                          {errors.minAge && (
                            <p className="form-error">{errors.minAge}</p>
                          )}
                        </div>
                        <div>
                          <label className="form-label">Maximum Age</label>
                          <input
                            type="number"
                            name="maxAge"
                            value={formData.maxAge}
                            onChange={handleInputChange}
                            className={`form-input ${errors.maxAge ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            max="150"
                          />
                          {errors.maxAge && (
                            <p className="form-error">{errors.maxAge}</p>
                          )}
                        </div>
                        <div>
                          <label className="form-label">Gender Restriction</label>
                          <select
                            name="genderRestriction"
                            value={formData.genderRestriction}
                            onChange={handleInputChange}
                            className="form-input"
                          >
                            <option value="">No Restriction</option>
                            {genderRestrictions.map((gender) => (
                              <option key={gender.key} value={gender.key}>
                                {gender.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Booking Settings */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Booking Settings</h3>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="form-label">Booking Advance Days</label>
                          <input
                            type="number"
                            name="bookingAdvanceDays"
                            value={formData.bookingAdvanceDays}
                            onChange={handleInputChange}
                            className={`form-input ${errors.bookingAdvanceDays ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            max="365"
                          />
                          {errors.bookingAdvanceDays && (
                            <p className="form-error">{errors.bookingAdvanceDays}</p>
                          )}
                          <p className="form-help">How far in advance can customers book</p>
                        </div>
                        <div>
                          <label className="form-label">Cancellation Deadline (hours)</label>
                          <input
                            type="number"
                            name="cancellationDeadlineHours"
                            value={formData.cancellationDeadlineHours}
                            onChange={handleInputChange}
                            className={`form-input ${errors.cancellationDeadlineHours ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="0"
                            max="168"
                          />
                          {errors.cancellationDeadlineHours && (
                            <p className="form-error">{errors.cancellationDeadlineHours}</p>
                          )}
                          <p className="form-help">Hours before service to allow cancellation</p>
                        </div>
                        <div>
                          <label className="form-label">Max Bookings Per Day</label>
                          <input
                            type="number"
                            name="maxBookingsPerDay"
                            value={formData.maxBookingsPerDay}
                            onChange={handleInputChange}
                            className={`form-input ${errors.maxBookingsPerDay ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            min="1"
                            max="100"
                          />
                          {errors.maxBookingsPerDay && (
                            <p className="form-error">{errors.maxBookingsPerDay}</p>
                          )}
                          <p className="form-help">Leave empty for unlimited</p>
                        </div>
                      </div>
                    </div>

                    {/* Benefits */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Benefits (Optional)</h3>
                      
                      {Array.isArray(formData.benefits) && formData.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={benefit}
                            onChange={(e) => handleArrayInputChange('benefits', index, e.target.value)}
                            className="form-input flex-1"
                            placeholder="e.g., Stress relief, Pain reduction"
                          />
                          <button
                            type="button"
                            onClick={() => removeArrayItem('benefits', index)}
                            className="btn-ghost btn-sm p-2 text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                      
                      <button
                        type="button"
                        onClick={() => addArrayItem('benefits')}
                        className="btn-outline btn-sm inline-flex items-center"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Benefit
                      </button>
                    </div>

                    {/* Requirements */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Requirements/Contraindications (Optional)</h3>
                      
                      {Array.isArray(formData.requirements) && formData.requirements.map((requirement, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={requirement}
                            onChange={(e) => handleArrayInputChange('requirements', index, e.target.value)}
                            className="form-input flex-1"
                            placeholder="e.g., No recent injuries, No pregnancy"
                          />
                          <button
                            type="button"
                            onClick={() => removeArrayItem('requirements', index)}
                            className="btn-ghost btn-sm p-2 text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                      
                      <button
                        type="button"
                        onClick={() => addArrayItem('requirements')}
                        className="btn-outline btn-sm inline-flex items-center"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Requirement
                      </button>
                    </div>

                    {/* Instructions */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Instructions (Optional)</h3>

                      <div>
                        <label className="form-label">Preparation Instructions</label>
                        <textarea
                          name="preparationInstructions"
                          value={formData.preparationInstructions}
                          onChange={handleInputChange}
                          className="form-input"
                          rows="3"
                          placeholder="What should customers do before the service..."
                        />
                      </div>

                      <div>
                        <label className="form-label">Aftercare Instructions</label>
                        <textarea
                          name="aftercareInstructions"
                          value={formData.aftercareInstructions}
                          onChange={handleInputChange}
                          className="form-input"
                          rows="3"
                          placeholder="What should customers do after the service..."
                        />
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-3 pt-4 border-t border-neutral-200 bg-neutral-50 -mx-6 px-6 py-4 rounded-b-xl">
                      <button
                        type="button"
                        onClick={closeModal}
                        className="btn-outline flex-1"
                        disabled={isSubmitting}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="btn-primary flex-1 inline-flex items-center justify-center"
                        disabled={isSubmitting || !formData.name || !formData.category || !formData.duration || !formData.price}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            {editingService ? 'Updating...' : 'Creating...'}
                          </>
                        ) : (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            {editingService ? 'Save Changes' : 'Create Service'}
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
