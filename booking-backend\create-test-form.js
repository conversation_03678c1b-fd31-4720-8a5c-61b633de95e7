/**
 * Create a test form for public booking testing
 */

const axios = require('axios');

async function createTestForm() {
  console.log('🧪 Creating test form for public booking...');
  
  try {
    // First, login to get auth token
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });

    const token = loginResponse.data.data.token;
    console.log('✅ Logged in successfully');

    // Get services and branches to use in form
    const servicesResponse = await axios.get('http://localhost:3000/api/services', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const branchesResponse = await axios.get('http://localhost:3000/api/branches', {
      headers: { Authorization: `Bearer ${token}` }
    });

    const service = servicesResponse.data.data.data[0]; // Take first service
    const branch = branchesResponse.data.data.data[0]; // Take first branch

    console.log('📋 Using service:', service?.name);
    console.log('🏢 Using branch:', branch?.name);

    if (!service || !branch) {
      console.error('❌ No services or branches found. Please create them first.');
      return;
    }

    // Create form
    const formData = {
      name: 'Test Public Form',
      description: 'Test form for public booking API testing',
      service_id: service.id,
      branch_id: branch.id,
      is_active: true,
      customization: {
        theme: 'default',
        primaryColor: '#007bff',
        logo: null
      }
    };

    const formResponse = await axios.post('http://localhost:3000/api/forms', formData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const form = formResponse.data.data;
    console.log('✅ Form created successfully!');
    console.log('Form slug:', form.slug);
    console.log('Public URL:', form.publicUrl);

    return form;

  } catch (error) {
    console.error('❌ Error occurred:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run test
createTestForm();
