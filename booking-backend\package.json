{"name": "spa-booking-backend", "version": "1.0.0", "description": "Spa Booking System Backend API", "main": "src/server.js", "scripts": {"start": "node start-server.js", "start:direct": "node test-app-direct.js", "start:prod": "node start-production.js", "dev": "nodemon start-server.js", "dev:direct": "node start-server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:sync": "node scripts/db-alter.js sync-all", "db:sync:model": "node scripts/db-alter.js sync-model", "db:check": "node scripts/db-alter.js check-table", "db:alter": "node scripts/db-alter.js alter-column", "db:add": "node scripts/db-alter.js add-column", "db:remove": "node scripts/db-alter.js remove-column", "test:app": "node scripts/test-app.js", "test:api": "node test-api.js", "test:quick": "node quick-test.js", "check:server": "node check-server.js", "debug:login": "node debug-login.js", "test:login-fix": "node test-login-fix.js", "test:db": "node test-db-connection.js", "fix:ip": "node fix-ip-addresses.js", "test:fix": "node test-fix.js", "test:final": "node final-test.js", "test:ultimate": "node ultimate-test.js", "test:http": "node http-test.js", "test:forms": "node test-forms-simple.js", "accounts:check": "node scripts/account-manager.js check", "accounts:unlock": "node scripts/account-manager.js unlock", "accounts:locked": "node scripts/account-manager.js locked", "accounts:reset-admin": "node scripts/account-manager.js reset-password <EMAIL> admin123456"}, "keywords": ["spa", "booking", "api", "express", "sequelize"], "author": "Your Name", "license": "MIT", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.6.0", "morgan": "^1.10.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^9.28.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "supertest": "^7.1.1"}}