/**
 * Services Swagger Documentation
 * API documentation for service management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Service:
 *       type: object
 *       required:
 *         - name
 *         - category
 *         - duration
 *         - price
 *       properties:
 *         id:
 *           type: integer
 *           description: Service ID
 *         name:
 *           type: string
 *           description: Service name
 *           example: "Swedish Massage"
 *         description:
 *           type: string
 *           description: Service description
 *           example: "Relaxing full-body massage using Swedish techniques"
 *         category:
 *           type: string
 *           enum: [massage, facial, body_treatment, nail_care, hair_care, wellness, package]
 *           description: Service category
 *           example: "massage"
 *         duration:
 *           type: integer
 *           description: Service duration in minutes
 *           example: 60
 *         price:
 *           type: number
 *           format: decimal
 *           description: Service price in VND
 *           example: 500000
 *         discountPrice:
 *           type: number
 *           format: decimal
 *           description: Discounted price in VND
 *           example: 450000
 *         isActive:
 *           type: boolean
 *           description: Service status
 *           example: true
 *         isPopular:
 *           type: boolean
 *           description: Popular service flag
 *           example: true
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           description: Service images URLs
 *           example: ["https://example.com/massage1.jpg", "https://example.com/massage2.jpg"]
 *         benefits:
 *           type: array
 *           items:
 *             type: string
 *           description: Service benefits list
 *           example: ["Stress relief", "Muscle relaxation", "Improved circulation"]
 *         requirements:
 *           type: array
 *           items:
 *             type: string
 *           description: Service requirements/contraindications
 *           example: ["No recent injuries", "Not suitable for pregnant women"]
 *         preparationInstructions:
 *           type: string
 *           description: Pre-service preparation instructions
 *           example: "Arrive 15 minutes early, avoid heavy meals 2 hours before"
 *         aftercareInstructions:
 *           type: string
 *           description: Post-service care instructions
 *           example: "Drink plenty of water, avoid strenuous activity for 24 hours"
 *         branchId:
 *           type: integer
 *           description: Branch ID (null for all branches)
 *           example: 1
 *         minAge:
 *           type: integer
 *           description: Minimum age requirement
 *           example: 18
 *         maxAge:
 *           type: integer
 *           description: Maximum age requirement
 *           example: 65
 *         genderRestriction:
 *           type: string
 *           enum: [male, female]
 *           description: Gender restriction if any
 *           example: "female"
 *         bookingAdvanceDays:
 *           type: integer
 *           description: How many days in advance can be booked
 *           example: 30
 *         cancellationDeadlineHours:
 *           type: integer
 *           description: Cancellation deadline in hours
 *           example: 24
 *         maxBookingsPerDay:
 *           type: integer
 *           description: Maximum bookings per day
 *           example: 10
 *         sortOrder:
 *           type: integer
 *           description: Display sort order
 *           example: 1
 *         branch:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             city:
 *               type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     ServiceCreate:
 *       type: object
 *       required:
 *         - name
 *         - category
 *         - duration
 *         - price
 *       properties:
 *         name:
 *           type: string
 *           example: "Swedish Massage"
 *         description:
 *           type: string
 *           example: "Relaxing full-body massage using Swedish techniques"
 *         category:
 *           type: string
 *           enum: [massage, facial, body_treatment, nail_care, hair_care, wellness, package]
 *           example: "massage"
 *         duration:
 *           type: integer
 *           minimum: 15
 *           maximum: 480
 *           example: 60
 *         price:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           example: 500000
 *         discountPrice:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           example: 450000
 *         branchId:
 *           type: integer
 *           example: 1
 *         minAge:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *           example: 18
 *         maxAge:
 *           type: integer
 *           minimum: 0
 *           maximum: 150
 *           example: 65
 *         genderRestriction:
 *           type: string
 *           enum: [male, female]
 *           example: "female"
 *         bookingAdvanceDays:
 *           type: integer
 *           minimum: 0
 *           maximum: 365
 *           example: 30
 *         cancellationDeadlineHours:
 *           type: integer
 *           minimum: 0
 *           maximum: 168
 *           example: 24
 *         maxBookingsPerDay:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           example: 10
 *         sortOrder:
 *           type: integer
 *           minimum: 0
 *           example: 1
 *         benefits:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Stress relief", "Muscle relaxation"]
 *         requirements:
 *           type: array
 *           items:
 *             type: string
 *           example: ["No recent injuries"]
 *         preparationInstructions:
 *           type: string
 *           example: "Arrive 15 minutes early"
 *         aftercareInstructions:
 *           type: string
 *           example: "Drink plenty of water"
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           example: ["https://example.com/massage1.jpg"]
 *     
 *     ServiceUpdate:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           example: "Deep Tissue Massage"
 *         description:
 *           type: string
 *           example: "Therapeutic massage targeting deep muscle layers"
 *         category:
 *           type: string
 *           enum: [massage, facial, body_treatment, nail_care, hair_care, wellness, package]
 *           example: "massage"
 *         duration:
 *           type: integer
 *           minimum: 15
 *           maximum: 480
 *           example: 90
 *         price:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           example: 700000
 *         discountPrice:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           example: 630000
 *         isActive:
 *           type: boolean
 *           example: true
 *         isPopular:
 *           type: boolean
 *           example: true
 *         branchId:
 *           type: integer
 *           example: 2
 *         minAge:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *           example: 21
 *         maxAge:
 *           type: integer
 *           minimum: 0
 *           maximum: 150
 *           example: 60
 *         genderRestriction:
 *           type: string
 *           enum: [male, female]
 *           example: null
 *         bookingAdvanceDays:
 *           type: integer
 *           minimum: 0
 *           maximum: 365
 *           example: 14
 *         cancellationDeadlineHours:
 *           type: integer
 *           minimum: 0
 *           maximum: 168
 *           example: 48
 *         maxBookingsPerDay:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           example: 5
 *         sortOrder:
 *           type: integer
 *           minimum: 0
 *           example: 2
 *         benefits:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Pain relief", "Improved mobility"]
 *         requirements:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Medical clearance required"]
 *         preparationInstructions:
 *           type: string
 *           example: "Consult with therapist before treatment"
 *         aftercareInstructions:
 *           type: string
 *           example: "Apply ice if soreness occurs"
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           example: ["https://example.com/deep-tissue1.jpg"]
 *     
 *     ServiceStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of active services
 *           example: 45
 *         popular:
 *           type: integer
 *           description: Number of popular services
 *           example: 12
 *         newThisMonth:
 *           type: integer
 *           description: New services this month
 *           example: 3
 *         averagePrice:
 *           type: number
 *           format: decimal
 *           description: Average service price
 *           example: 650000
 *         priceRange:
 *           type: object
 *           properties:
 *             min:
 *               type: number
 *               format: decimal
 *               example: 200000
 *             max:
 *               type: number
 *               format: decimal
 *               example: 1500000
 *         byCategory:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "massage": 15
 *             "facial": 10
 *             "body_treatment": 8
 *             "nail_care": 6
 *             "hair_care": 4
 *             "wellness": 2
 */

/**
 * @swagger
 * /api/services:
 *   get:
 *     summary: Get all services with pagination and filters
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [massage, facial, body_treatment, nail_care, hair_care, wellness, package]
 *         description: Filter by service category
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Filter by active status
 *       - in: query
 *         name: isPopular
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Filter by popular status
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in name, description, category
 *     responses:
 *       200:
 *         description: Services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Service'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Services retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create new service
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceCreate'
 *     responses:
 *       201:
 *         description: Service created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *                 message:
 *                   type: string
 *                   example: "Service created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       409:
 *         description: Service name already exists in branch
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/services/{id}:
 *   get:
 *     summary: Get service by ID
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *                 message:
 *                   type: string
 *                   example: "Service retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update service
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceUpdate'
 *     responses:
 *       200:
 *         description: Service updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *                 message:
 *                   type: string
 *                   example: "Service updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       409:
 *         description: Service name already exists in branch
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete service (soft delete)
 *     tags: [Services]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
