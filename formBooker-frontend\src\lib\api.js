/**
 * API Configuration and Axios Setup
 * Handles authentication, interceptors, and API communication
 */

import axios from 'axios';

// API Configuration
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Create axios instance
const api = axios.create(API_CONFIG);

// Token management utilities
export const tokenManager = {
  getToken: () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken');
    }
    return null;
  },

  setToken: (token) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', token);
      // Also set as httpOnly cookie for middleware (optional)
      document.cookie = `accessToken=${token}; path=/; secure; samesite=strict`;
    }
  },

  getRefreshToken: () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refreshToken');
    }
    return null;
  },

  setRefreshToken: (token) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('refreshToken', token);
    }
  },

  removeTokens: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      // Also remove cookies
      document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }
  },

  getUser: () => {
    if (typeof window !== 'undefined') {
      const user = localStorage.getItem('user');
      return user ? JSON.parse(user) : null;
    }
    return null;
  },

  setUser: (user) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(user));
    }
  },

  // Check if token exists and is valid format
  isTokenValid: () => {
    const token = tokenManager.getToken();
    if (!token) {
      console.log('🔍 Token validation: No token found');
      return false;
    }

    try {
      // Basic JWT format check (header.payload.signature)
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.log('🔍 Token validation: Invalid JWT format, parts:', parts.length);
        return false;
      }

      // Decode payload to check expiry
      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      console.log('🔍 Token validation:', {
        hasExp: !!payload.exp,
        exp: payload.exp,
        currentTime: currentTime,
        timeUntilExpiry: payload.exp ? (payload.exp - currentTime) : 'N/A',
        isValid: payload.exp && payload.exp > (currentTime + 30)
      });

      // Check if token is expired (with 30 second buffer)
      return payload.exp && payload.exp > (currentTime + 30);
    } catch (error) {
      console.error('🔍 Token validation error:', error);
      return false;
    }
  },

  // Get token expiry time
  getTokenExpiry: () => {
    const token = tokenManager.getToken();
    if (!token) return null;

    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      const payload = JSON.parse(atob(parts[1]));
      return payload.exp ? new Date(payload.exp * 1000) : null;
    } catch (error) {
      console.error('Token expiry check error:', error);
      return null;
    }
  }
};

// Request interceptor - Add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Track refresh attempts to prevent infinite loops
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Response interceptor - Handle token refresh and errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (token expired)
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = tokenManager.getRefreshToken();
      if (refreshToken) {
        try {
          // Attempt to refresh token
          const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh`, {
            refreshToken,
          });

          // Handle different response formats from backend
          const responseData = response.data.data || response.data;
          const { accessToken, refreshToken: newRefreshToken, tokens } = responseData;

          // Backend might return tokens in different formats
          const finalAccessToken = accessToken || tokens?.accessToken;
          const finalRefreshToken = newRefreshToken || tokens?.refreshToken;

          // Update stored tokens
          tokenManager.setToken(finalAccessToken);
          if (finalRefreshToken) {
            tokenManager.setRefreshToken(finalRefreshToken);
          }

          // Process queued requests
          processQueue(null, finalAccessToken);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${finalAccessToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect
          processQueue(refreshError, null);
          tokenManager.removeTokens();

          if (typeof window !== 'undefined') {
            // Only redirect if not already on login/register pages
            const currentPath = window.location.pathname;
            if (currentPath !== '/login' && currentPath !== '/register' && currentPath !== '/') {
              window.location.href = '/login';
            }
          }
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // No refresh token, clear tokens and redirect
        isRefreshing = false;
        tokenManager.removeTokens();

        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname;
          if (currentPath !== '/login' && currentPath !== '/register' && currentPath !== '/') {
            window.location.href = '/login';
          }
        }
      }
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const endpoints = {
  auth: {
    register: '/auth/register',
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    profile: '/auth/profile',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
  },
  users: {
    list: '/users',
    create: '/users',
    update: (id) => `/users/${id}`,
    delete: (id) => `/users/${id}`,
    profile: '/users/profile',
  },
  forms: {
    list: '/forms',
    create: '/forms',
    update: (id) => `/forms/${id}`,
    delete: (id) => `/forms/${id}`,
    public: (id) => `/forms/public/${id}`,
  },
  bookings: {
    list: '/bookings',
    create: '/bookings',
    update: (id) => `/bookings/${id}`,
    delete: (id) => `/bookings/${id}`,
  },
  services: {
    list: '/services',
    create: '/services',
    update: (id) => `/services/${id}`,
    delete: (id) => `/services/${id}`,
  },
  branches: {
    list: '/branches',
    create: '/branches',
    update: (id) => `/branches/${id}`,
    delete: (id) => `/branches/${id}`,
  },
};

export default api;
