/**
 * Database Sync Service
 * Handles database synchronization with alter table functionality
 */

const { sequelize, config } = require('./connection');
const logger = require('../utils/logger');

class DatabaseSyncService {
  constructor() {
    this.config = config;
  }

  /**
   * Sync all models to database
   * @param {Object} options - Sync options
   * @returns {Promise<void>}
   */
  async syncDatabase(options = {}) {
    try {
      logger.info('Starting database synchronization...');
      
      const syncOptions = {
        ...this.config.sync,
        ...options
      };

      if (syncOptions.force) {
        logger.warn('FORCE SYNC: This will drop all tables!');
        if (process.env.NODE_ENV === 'production') {
          throw new Error('Force sync is not allowed in production');
        }
        await sequelize.sync({ force: true });
        logger.info('Database force synced successfully');
      } else if (syncOptions.alter && this.config.alter.enabled) {
        logger.info('ALTER SYNC: Updating table structures...');
        await sequelize.sync({ alter: this.config.alter });
        logger.info('Database alter synced successfully');
      } else {
        logger.info('SAFE SYNC: Creating missing tables only...');
        await sequelize.sync();
        logger.info('Database synced successfully');
      }
      
    } catch (error) {
      logger.error('Database sync failed:', error);
      throw error;
    }
  }

  /**
   * Sync a specific model
   * @param {string} modelName - Name of the model to sync
   * @param {Object} options - Sync options
   * @returns {Promise<void>}
   */
  async syncModel(modelName, options = {}) {
    try {
      const model = sequelize.models[modelName];
      if (!model) {
        throw new Error(`Model ${modelName} not found`);
      }

      const syncOptions = {
        ...this.config.alter,
        ...options
      };

      if (syncOptions.enabled) {
        logger.info(`Syncing model: ${modelName}`);
        await model.sync({ alter: syncOptions });
        logger.info(`Model ${modelName} synced successfully`);
      } else {
        logger.warn(`Alter table disabled for model: ${modelName}`);
        await model.sync();
        logger.info(`Model ${modelName} synced safely (no alter)`);
      }
      
    } catch (error) {
      logger.error(`Failed to sync model ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * Check table structure for a model
   * @param {string} modelName - Name of the model
   * @returns {Promise<Object>}
   */
  async checkTableStructure(modelName) {
    try {
      const model = sequelize.models[modelName];
      if (!model) {
        throw new Error(`Model ${modelName} not found`);
      }

      const tableName = model.tableName || model.getTableName();
      const tableInfo = await sequelize.getQueryInterface().describeTable(tableName);
      
      logger.info(`Table structure for ${modelName}:`, tableInfo);
      return tableInfo;
      
    } catch (error) {
      logger.error(`Failed to check table structure for ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * Alter a table column
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column
   * @param {Object} columnDefinition - New column definition
   * @returns {Promise<void>}
   */
  async alterTableColumn(tableName, columnName, columnDefinition) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();
      
      logger.info(`Altering column ${columnName} in table ${tableName}`);
      await queryInterface.changeColumn(tableName, columnName, columnDefinition);
      logger.info(`Column ${columnName} altered successfully`);
      
    } catch (error) {
      logger.error(`Failed to alter column ${columnName}:`, error);
      throw error;
    }
  }

  /**
   * Add a column to a table
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column
   * @param {Object} columnDefinition - Column definition
   * @returns {Promise<void>}
   */
  async addTableColumn(tableName, columnName, columnDefinition) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();
      
      logger.info(`Adding column ${columnName} to table ${tableName}`);
      await queryInterface.addColumn(tableName, columnName, columnDefinition);
      logger.info(`Column ${columnName} added successfully`);
      
    } catch (error) {
      logger.error(`Failed to add column ${columnName}:`, error);
      throw error;
    }
  }

  /**
   * Remove a column from a table
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column
   * @returns {Promise<void>}
   */
  async removeTableColumn(tableName, columnName) {
    try {
      if (!this.config.alter.enabled || !this.config.alter.drop) {
        throw new Error('Drop column is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();
      
      logger.warn(`Removing column ${columnName} from table ${tableName}`);
      await queryInterface.removeColumn(tableName, columnName);
      logger.info(`Column ${columnName} removed successfully`);
      
    } catch (error) {
      logger.error(`Failed to remove column ${columnName}:`, error);
      throw error;
    }
  }

  /**
   * Add an index to a table
   * @param {string} tableName - Name of the table
   * @param {Array|string} fields - Fields to index
   * @param {Object} options - Index options
   * @returns {Promise<void>}
   */
  async addIndex(tableName, fields, options = {}) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();
      
      logger.info(`Adding index to table ${tableName} on fields:`, fields);
      await queryInterface.addIndex(tableName, fields, options);
      logger.info(`Index added successfully`);
      
    } catch (error) {
      logger.error(`Failed to add index:`, error);
      throw error;
    }
  }

  /**
   * Remove an index from a table
   * @param {string} tableName - Name of the table
   * @param {string} indexName - Name of the index
   * @returns {Promise<void>}
   */
  async removeIndex(tableName, indexName) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();
      
      logger.info(`Removing index ${indexName} from table ${tableName}`);
      await queryInterface.removeIndex(tableName, indexName);
      logger.info(`Index ${indexName} removed successfully`);
      
    } catch (error) {
      logger.error(`Failed to remove index ${indexName}:`, error);
      throw error;
    }
  }

  /**
   * Get all tables in the database
   * @returns {Promise<Array>}
   */
  async getAllTables() {
    try {
      const queryInterface = sequelize.getQueryInterface();
      const tables = await queryInterface.showAllTables();
      return tables;
    } catch (error) {
      logger.error('Failed to get all tables:', error);
      throw error;
    }
  }

  /**
   * Check if alter table is enabled
   * @returns {boolean}
   */
  isAlterEnabled() {
    return this.config.alter.enabled;
  }

  /**
   * Check if drop column is enabled
   * @returns {boolean}
   */
  isDropEnabled() {
    return this.config.alter.enabled && this.config.alter.drop;
  }

  /**
   * Get sync configuration
   * @returns {Object}
   */
  getSyncConfig() {
    return {
      alter: this.config.alter,
      sync: this.config.sync
    };
  }
}

module.exports = new DatabaseSyncService();
