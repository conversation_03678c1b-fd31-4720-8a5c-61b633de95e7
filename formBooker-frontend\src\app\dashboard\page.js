'use client';

import Link from 'next/link';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Plus, 
  Calendar, 
  Settings, 
  FileText,
  CalendarDays,
  Building2,
  TrendingUp,
  Clock,
  Users
} from 'lucide-react';

/**
 * Dashboard Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function DashboardPage() {
  // Mock data - TODO: Replace with real API data
  const stats = {
    forms: 5,
    bookings: 23,
    services: 12,
    conversionRate: 89
  };

  const recentActivity = [
    {
      id: 1,
      type: 'booking',
      message: 'New booking: Hair Cut',
      time: '2h ago',
      icon: Calendar,
      color: 'text-secondary-600 bg-secondary-100'
    },
    {
      id: 2,
      type: 'form',
      message: 'Form created: Massage',
      time: '1d ago',
      icon: FileText,
      color: 'text-primary-600 bg-primary-100'
    },
    {
      id: 3,
      type: 'service',
      message: 'Service added: Facial',
      time: '2d ago',
      icon: Building2,
      color: 'text-accent-600 bg-accent-100'
    }
  ];

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-text">
              Welcome back, Demo Business!
            </h1>
            <p className="mt-2 text-neutral-600">
              Here's what's happening with your booking forms today.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-text mb-4">Quick Stats:</h2>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Forms Stat */}
              <div className="stat-card">
                <div className="stat-value text-primary-600">{stats.forms}</div>
                <div className="stat-label">Forms</div>
              </div>

              {/* Bookings Stat */}
              <div className="stat-card">
                <div className="stat-value text-secondary-600">{stats.bookings}</div>
                <div className="stat-label">Book.</div>
              </div>

              {/* Services Stat */}
              <div className="stat-card">
                <div className="stat-value text-accent-600">{stats.services}</div>
                <div className="stat-label">Serv.</div>
              </div>

              {/* Conversion Rate Stat */}
              <div className="stat-card">
                <div className="stat-value text-purple-600">{stats.conversionRate}%</div>
                <div className="stat-label">Rate</div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-text mb-4">Quick Actions:</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Create New Form */}
              <Link 
                href="/forms/new"
                className="card-hover p-6 group cursor-pointer"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center group-hover:bg-primary-200 transition-colors duration-200">
                    <Plus className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-text group-hover:text-primary-600 transition-colors duration-200">
                      Create New Form
                    </h3>
                    <p className="text-sm text-neutral-600">
                      Build a new booking form in minutes
                    </p>
                  </div>
                </div>
              </Link>

              {/* View All Bookings */}
              <Link 
                href="/bookings"
                className="card-hover p-6 group cursor-pointer"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-200">
                    <CalendarDays className="h-6 w-6 text-secondary-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-text group-hover:text-secondary-600 transition-colors duration-200">
                      📋 View All Bookings
                    </h3>
                    <p className="text-sm text-neutral-600">
                      Manage your appointments
                    </p>
                  </div>
                </div>
              </Link>

              {/* Manage Services */}
              <Link 
                href="/services"
                className="card-hover p-6 group cursor-pointer"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center group-hover:bg-accent-200 transition-colors duration-200">
                    <Settings className="h-6 w-6 text-accent-600" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-text group-hover:text-accent-600 transition-colors duration-200">
                      ⚙️ Manage Services
                    </h3>
                    <p className="text-sm text-neutral-600">
                      Add or edit your services
                    </p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-text mb-4">Recent Activity:</h2>
            <div className="card">
              <div className="card-body">
                {recentActivity.length > 0 ? (
                  <div className="space-y-4">
                    {recentActivity.map((activity) => {
                      const Icon = activity.icon;
                      return (
                        <div key={activity.id} className="flex items-center">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${activity.color}`}>
                            <Icon className="h-5 w-5" />
                          </div>
                          <div className="ml-4 flex-1">
                            <p className="text-sm font-medium text-text">
                              • {activity.message}
                            </p>
                            <p className="text-xs text-neutral-500">
                              {activity.time}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
                    <p className="text-neutral-500">No recent activity</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Additional Stats Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* This Week Summary */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-text">This Week</h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <CalendarDays className="h-5 w-5 text-secondary-600 mr-2" />
                      <span className="text-sm text-neutral-600">New Bookings</span>
                    </div>
                    <span className="text-lg font-semibold text-text">12</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-primary-600 mr-2" />
                      <span className="text-sm text-neutral-600">Forms Created</span>
                    </div>
                    <span className="text-lg font-semibold text-text">2</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <TrendingUp className="h-5 w-5 text-accent-600 mr-2" />
                      <span className="text-sm text-neutral-600">Conversion Rate</span>
                    </div>
                    <span className="text-lg font-semibold text-text">92%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Tips */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-text">Quick Tips</h3>
              </div>
              <div className="card-body">
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-sm text-neutral-600">
                      Share your booking forms on social media to reach more customers
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-secondary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-sm text-neutral-600">
                      Add multiple services to give customers more options
                    </p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-sm text-neutral-600">
                      Embed forms directly on your website for seamless booking
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
