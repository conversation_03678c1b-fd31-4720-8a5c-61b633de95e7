/**
 * Services Service
 * Handles all services-related API calls
 */

import api, { endpoints } from '@/lib/api';

class ServicesService {
  /**
   * Get all services with pagination and filters
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.category - Filter by category
   * @param {number} params.branchId - Filter by branch ID
   * @param {boolean} params.isActive - Filter by active status
   * @param {boolean} params.isPopular - Filter by popular status
   * @param {string} params.search - Search term
   * @param {number} params.minPrice - Minimum price filter
   * @param {number} params.maxPrice - Maximum price filter
   * @param {string} params.sort - Sort order
   * @returns {Promise<Object>} Services response
   */
  async getServices(params = {}) {
    try {
      const endpoint = '/services';
      const requestParams = { ...params };

      const response = await api.get(endpoint, { params: requestParams });

      return {
        success: true,
        data: response.data.data,
        pagination: response.data.pagination,
        message: response.data.message || 'Services retrieved successfully',
      };
    } catch (error) {
      console.error('Get services error:', error);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error?.message ||
                          'Failed to fetch services. Please try again.';

      const validationErrors = error.response?.data?.errors || [];

      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Get service by ID
   * @param {number} id - Service ID
   * @returns {Promise<Object>} Service response
   */
  async getServiceById(id) {
    try {
      const response = await api.get(`/services/${id}`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Service retrieved successfully',
      };
    } catch (error) {
      console.error('Get service by ID error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to fetch service. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Create a new service
   * @param {Object} serviceData - Service data
   * @param {string} serviceData.name - Service name (required)
   * @param {string} serviceData.description - Service description (optional)
   * @param {string} serviceData.category - Service category (required)
   * @param {number} serviceData.duration - Duration in minutes (required)
   * @param {number} serviceData.price - Service price (required)
   * @param {number} serviceData.discountPrice - Discount price (optional)
   * @param {number} serviceData.branchId - Branch ID (optional)
   * @param {number} serviceData.minAge - Minimum age (optional)
   * @param {number} serviceData.maxAge - Maximum age (optional)
   * @param {string} serviceData.genderRestriction - Gender restriction (optional)
   * @param {number} serviceData.bookingAdvanceDays - Booking advance days (optional)
   * @param {number} serviceData.cancellationDeadlineHours - Cancellation deadline (optional)
   * @param {number} serviceData.maxBookingsPerDay - Max bookings per day (optional)
   * @param {number} serviceData.sortOrder - Sort order (optional)
   * @param {Array} serviceData.benefits - Benefits list (optional)
   * @param {Array} serviceData.requirements - Requirements list (optional)
   * @param {Array} serviceData.images - Image URLs (optional)
   * @param {string} serviceData.preparationInstructions - Preparation instructions (optional)
   * @param {string} serviceData.aftercareInstructions - Aftercare instructions (optional)
   * @returns {Promise<Object>} Create response
   */
  async createService(serviceData) {
    try {
      const response = await api.post('/services', serviceData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Service created successfully',
      };
    } catch (error) {
      console.error('Create service error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to create service. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Update service
   * @param {number} id - Service ID
   * @param {Object} serviceData - Updated service data
   * @returns {Promise<Object>} Update response
   */
  async updateService(id, serviceData) {
    try {
      const response = await api.put(`/services/${id}`, serviceData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Service updated successfully',
      };
    } catch (error) {
      console.error('Update service error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update service. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Delete service (soft delete)
   * @param {number} id - Service ID
   * @returns {Promise<Object>} Delete response
   */
  async deleteService(id) {
    try {
      const response = await api.delete(`/services/${id}`);
      
      return {
        success: true,
        message: response.data.message || 'Service deleted successfully',
      };
    } catch (error) {
      console.error('Delete service error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to delete service. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Toggle service status (active/inactive)
   * @param {number} id - Service ID
   * @param {boolean} isActive - New active status
   * @returns {Promise<Object>} Toggle response
   */
  async toggleServiceStatus(id, isActive) {
    try {
      const response = await api.patch(`/services/${id}/status`, {
        isActive
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || `Service ${isActive ? 'activated' : 'deactivated'} successfully`,
      };
    } catch (error) {
      console.error('Toggle service status error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update service status. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Toggle service popularity
   * @param {number} id - Service ID
   * @param {boolean} isPopular - New popularity status
   * @returns {Promise<Object>} Toggle response
   */
  async toggleServicePopularity(id, isPopular) {
    try {
      const response = await api.patch(`/services/${id}/popularity`, {
        isPopular
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || `Service ${isPopular ? 'marked as popular' : 'unmarked as popular'} successfully`,
      };
    } catch (error) {
      console.error('Toggle service popularity error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update service popularity. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Get active services (simple list)
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Active services response
   */
  async getActiveServices(limit = 100) {
    try {
      const response = await api.get('/services/active', { 
        params: { limit } 
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Active services retrieved successfully',
      };
    } catch (error) {
      console.error('Get active services error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch active services. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get popular services
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Popular services response
   */
  async getPopularServices(limit = 20) {
    try {
      const response = await api.get('/services/popular', {
        params: { limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Popular services retrieved successfully',
      };
    } catch (error) {
      console.error('Get popular services error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch popular services. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Search services
   * @param {string} searchTerm - Search term
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Search results response
   */
  async searchServices(searchTerm, limit = 20) {
    try {
      const response = await api.get('/services/search', {
        params: { q: searchTerm, limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Search completed successfully',
      };
    } catch (error) {
      console.error('Search services error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to search services. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get services by category
   * @param {string} category - Service category
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Services by category response
   */
  async getServicesByCategory(category, limit = 50) {
    try {
      const response = await api.get(`/services/category/${encodeURIComponent(category)}`, {
        params: { limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Services retrieved successfully',
      };
    } catch (error) {
      console.error('Get services by category error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch services by category. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get services by branch
   * @param {number} branchId - Branch ID
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Services by branch response
   */
  async getServicesByBranch(branchId, limit = 100) {
    try {
      const response = await api.get(`/services/branch/${branchId}`, {
        params: { limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Branch services retrieved successfully',
      };
    } catch (error) {
      console.error('Get services by branch error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch services by branch. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get services by price range
   * @param {number} minPrice - Minimum price
   * @param {number} maxPrice - Maximum price
   * @param {number} limit - Maximum number of services to return
   * @returns {Promise<Object>} Services by price range response
   */
  async getServicesByPriceRange(minPrice, maxPrice, limit = 50) {
    try {
      const response = await api.get('/services/price-range', {
        params: { minPrice, maxPrice, limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Services in price range retrieved successfully',
      };
    } catch (error) {
      console.error('Get services by price range error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch services by price range. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get service statistics
   * @returns {Promise<Object>} Service statistics response
   */
  async getServiceStats() {
    try {
      const response = await api.get('/services/stats');
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Service statistics retrieved successfully',
      };
    } catch (error) {
      console.error('Get service stats error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch service statistics. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Update service images
   * @param {number} id - Service ID
   * @param {Array} imageUrls - Array of image URLs
   * @returns {Promise<Object>} Update images response
   */
  async updateServiceImages(id, imageUrls) {
    try {
      const response = await api.post(`/services/${id}/images`, {
        images: imageUrls
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Service images updated successfully',
      };
    } catch (error) {
      console.error('Update service images error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update service images. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }
}

// Export singleton instance
export default new ServicesService();
