/**
 * Services Routes
 * API routes for service management
 */

const express = require('express');
const router = express.Router();
const ServicesController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validatePagination, validateIdParam } = require('../../middleware/validation');
const { adminLimiter, generalLimiter } = require('../../middleware/rateLimiter');
const { body, query, param } = require('express-validator');

// Validation rules
const createServiceValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Service name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('category')
    .isIn(['massage', 'facial', 'body_treatment', 'nail_care', 'hair_care', 'wellness', 'package'])
    .withMessage('Invalid service category'),
  body('duration')
    .isInt({ min: 15, max: 480 })
    .withMessage('Duration must be between 15 and 480 minutes'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('discountPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount price must be a positive number'),
  body('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('minAge')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Minimum age must be between 0 and 100'),
  body('maxAge')
    .optional()
    .isInt({ min: 0, max: 150 })
    .withMessage('Maximum age must be between 0 and 150'),
  body('genderRestriction')
    .optional()
    .isIn(['male', 'female'])
    .withMessage('Gender restriction must be male or female'),
  body('bookingAdvanceDays')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Booking advance days must be between 0 and 365'),
  body('cancellationDeadlineHours')
    .optional()
    .isInt({ min: 0, max: 168 })
    .withMessage('Cancellation deadline must be between 0 and 168 hours'),
  body('maxBookingsPerDay')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Max bookings per day must be between 1 and 100'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  body('benefits')
    .optional()
    .isArray()
    .withMessage('Benefits must be an array'),
  body('requirements')
    .optional()
    .isArray()
    .withMessage('Requirements must be an array'),
  body('images')
    .optional()
    .isArray()
    .withMessage('Images must be an array')
];

const updateServiceValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Service name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('category')
    .optional()
    .isIn(['massage', 'facial', 'body_treatment', 'nail_care', 'hair_care', 'wellness', 'package'])
    .withMessage('Invalid service category'),
  body('duration')
    .optional()
    .isInt({ min: 15, max: 480 })
    .withMessage('Duration must be between 15 and 480 minutes'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('discountPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount price must be a positive number'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('isPopular')
    .optional()
    .isBoolean()
    .withMessage('isPopular must be a boolean'),
  body('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('minAge')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Minimum age must be between 0 and 100'),
  body('maxAge')
    .optional()
    .isInt({ min: 0, max: 150 })
    .withMessage('Maximum age must be between 0 and 150'),
  body('genderRestriction')
    .optional()
    .isIn(['male', 'female'])
    .withMessage('Gender restriction must be male or female'),
  body('bookingAdvanceDays')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Booking advance days must be between 0 and 365'),
  body('cancellationDeadlineHours')
    .optional()
    .isInt({ min: 0, max: 168 })
    .withMessage('Cancellation deadline must be between 0 and 168 hours'),
  body('maxBookingsPerDay')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Max bookings per day must be between 1 and 100'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  body('benefits')
    .optional()
    .isArray()
    .withMessage('Benefits must be an array'),
  body('requirements')
    .optional()
    .isArray()
    .withMessage('Requirements must be an array'),
  body('images')
    .optional()
    .isArray()
    .withMessage('Images must be an array')
];

const toggleStatusValidation = [
  body('isActive')
    .isBoolean()
    .withMessage('isActive is required and must be a boolean')
];

const togglePopularityValidation = [
  body('isPopular')
    .isBoolean()
    .withMessage('isPopular is required and must be a boolean')
];

const searchValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

const priceRangeValidation = [
  query('minPrice')
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  query('maxPrice')
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number')
];

const updateImagesValidation = [
  body('images')
    .isArray()
    .withMessage('Images must be an array'),
  body('images.*')
    .isURL()
    .withMessage('Each image must be a valid URL')
];

const getServicesValidation = [
  query('category')
    .optional()
    .isIn(['massage', 'facial', 'body_treatment', 'nail_care', 'hair_care', 'wellness', 'package'])
    .withMessage('Invalid service category'),
  query('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  query('isActive')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isActive must be true or false'),
  query('isPopular')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isPopular must be true or false'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/services
 * @desc    Get all services with pagination and filters
 * @access  Private (All authenticated users)
 */
router.get('/', 
  getServicesValidation,
  validatePagination,
  validateRequest,
  ServicesController.getServices
);

/**
 * @route   GET /api/services/stats
 * @desc    Get service statistics
 * @access  Private (Admin, Staff)
 */
router.get('/stats', 
  authorize(['admin', 'staff']),
  ServicesController.getServiceStats
);

/**
 * @route   GET /api/services/active
 * @desc    Get active services (simple list)
 * @access  Private (All authenticated users)
 */
router.get('/active', 
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  ServicesController.getActiveServices
);

/**
 * @route   GET /api/services/popular
 * @desc    Get popular services
 * @access  Private (All authenticated users)
 */
router.get('/popular', 
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
  ServicesController.getPopularServices
);

/**
 * @route   GET /api/services/search
 * @desc    Search services
 * @access  Private (All authenticated users)
 */
router.get('/search', 
  searchValidation,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
  ServicesController.searchServices
);

/**
 * @route   GET /api/services/price-range
 * @desc    Get services by price range
 * @access  Private (All authenticated users)
 */
router.get('/price-range', 
  priceRangeValidation,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  ServicesController.getServicesByPriceRange
);

/**
 * @route   GET /api/services/category/:category
 * @desc    Get services by category
 * @access  Private (All authenticated users)
 */
router.get('/category/:category', 
  param('category')
    .isIn(['massage', 'facial', 'body_treatment', 'nail_care', 'hair_care', 'wellness', 'package'])
    .withMessage('Invalid service category'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  ServicesController.getServicesByCategory
);

/**
 * @route   GET /api/services/branch/:branchId
 * @desc    Get services by branch
 * @access  Private (All authenticated users)
 */
router.get('/branch/:branchId', 
  param('branchId')
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  ServicesController.getServicesByBranch
);

/**
 * @route   GET /api/services/:id
 * @desc    Get service by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', 
  validateIdParam(),
  validateRequest,
  ServicesController.getServiceById
);

/**
 * @route   POST /api/services
 * @desc    Create new service
 * @access  Private (Admin, Customer)
 */
router.post('/', 
  authorize(['admin', 'customer']),
  adminLimiter,
  createServiceValidation,
  validateRequest,
  ServicesController.createService
);

/**
 * @route   PUT /api/services/:id
 * @desc    Update service
 * @access  Private (Admin, Customer)
 */
router.put('/:id', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  updateServiceValidation,
  validateRequest,
  ServicesController.updateService
);

/**
 * @route   DELETE /api/services/:id
 * @desc    Delete service (soft delete)
 * @access  Private (Admin, Customer)
 */
router.delete('/:id', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  ServicesController.deleteService
);

/**
 * @route   PATCH /api/services/:id/status
 * @desc    Toggle service status
 * @access  Private (Admin, Customer)
 */
router.patch('/:id/status', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  toggleStatusValidation,
  validateRequest,
  ServicesController.toggleServiceStatus
);

/**
 * @route   PATCH /api/services/:id/popularity
 * @desc    Toggle service popularity
 * @access  Private (Admin, Customer)
 */
router.patch('/:id/popularity', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  togglePopularityValidation,
  validateRequest,
  ServicesController.toggleServicePopularity
);

/**
 * @route   POST /api/services/:id/images
 * @desc    Update service images
 * @access  Private (Admin, Customer)
 */
router.post('/:id/images', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  updateImagesValidation,
  validateRequest,
  ServicesController.updateServiceImages
);

module.exports = router;
