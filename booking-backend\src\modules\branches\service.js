/**
 * Branches Service
 * Business logic for branch management
 */

const Branch = require('./model');
const User = require('../users/model');
const { AppError } = require('../../middleware/errorHandler');
const { paginate } = require('../../utils/pagination');
const logger = require('../../utils/logger');
const { WORKING_DAYS } = require('../../utils/constants');

class BranchesService {
  /**
   * Get all branches with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {Object} pagination - Pagination options
   * @param {Object} user - Current user for filtering
   * @returns {Promise<Object>} Paginated branches
   */
  async getBranches(filters = {}, pagination = {}, user = null) {
    try {
      const { city, district, isActive, search, hasLocation } = filters;
      const { Op } = require('sequelize');
      const whereClause = {};

      // Apply role-based filtering
      if (user && user.role === 'customer') {
        // Customer can only see branches they manage
        whereClause.managerId = user.id;
      }

      // Apply filters
      if (city) {
        whereClause.city = { [Op.like]: `%${city}%` };
      }

      if (district) {
        whereClause.district = { [Op.like]: `%${district}%` };
      }

      if (isActive !== undefined) {
        whereClause.isActive = isActive === 'true';
      }

      if (hasLocation === 'true') {
        whereClause.latitude = { [Op.ne]: null };
        whereClause.longitude = { [Op.ne]: null };
      }

      // Search functionality
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { address: { [Op.like]: `%${search}%` } },
          { city: { [Op.like]: `%${search}%` } },
          { district: { [Op.like]: `%${search}%` } }
        ];
      }

      const options = {
        ...pagination,
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'name', 'email', 'phone'],
            required: false
          }
        ]
      };

      return await paginate(Branch, whereClause, options);

    } catch (error) {
      logger.error('Get branches failed:', error);
      throw error;
    }
  }

  /**
   * Get branch by ID
   * @param {number} branchId - Branch ID
   * @returns {Promise<Object>} Branch data
   */
  async getBranchById(branchId) {
    try {
      const branch = await Branch.findByPk(branchId, {
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: User,
            as: 'employees',
            attributes: ['id', 'name', 'email', 'phone', 'role'],
            where: { isActive: true },
            required: false
          }
        ]
      });

      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      return branch;

    } catch (error) {
      logger.error('Get branch by ID failed:', error);
      throw error;
    }
  }

  /**
   * Create new branch
   * @param {Object} branchData - Branch data
   * @param {Object} user - Current user
   * @returns {Promise<Object>} Created branch
   */
  async createBranch(branchData, user = null) {
    try {
      const { name, phone, email, managerId } = branchData;

      // Check if branch name already exists in the same city
      const existingBranch = await Branch.findOne({
        where: {
          name,
          city: branchData.city,
          isActive: true
        }
      });

      if (existingBranch) {
        throw new AppError('Branch with this name already exists in the city', 409, 'BRANCH_NAME_EXISTS');
      }

      // Check if phone already exists
      const existingPhone = await Branch.findOne({
        where: { phone, isActive: true }
      });

      if (existingPhone) {
        throw new AppError('Phone number already exists', 409, 'PHONE_EXISTS');
      }

      // Check if email already exists (if provided)
      if (email) {
        const existingEmail = await Branch.findOne({
          where: { email, isActive: true }
        });

        if (existingEmail) {
          throw new AppError('Email already exists', 409, 'EMAIL_EXISTS');
        }
      }

      // Validate manager if provided
      if (managerId) {
        const manager = await User.findByPk(managerId);
        if (!manager || !manager.isActive) {
          throw new AppError('Manager not found or inactive', 404, 'MANAGER_NOT_FOUND');
        }

        if (!['admin', 'staff', 'customer'].includes(manager.role)) {
          throw new AppError('Manager must be admin, staff, or customer', 400, 'INVALID_MANAGER_ROLE');
        }
      }

      // Validate working days
      if (branchData.workingDays) {
        const validDays = Object.values(WORKING_DAYS);
        const invalidDays = branchData.workingDays.filter(day => !validDays.includes(day));
        if (invalidDays.length > 0) {
          throw new AppError(`Invalid working days: ${invalidDays.join(', ')}`, 400, 'INVALID_WORKING_DAYS');
        }
      }

      // Create branch
      const branch = await Branch.create(branchData);

      logger.business('branch_created', {
        branchId: branch.id,
        branchName: branch.name,
        city: branch.city,
        managerId: branch.managerId
      });

      return await this.getBranchById(branch.id);

    } catch (error) {
      logger.error('Create branch failed:', error);
      throw error;
    }
  }

  /**
   * Update branch
   * @param {number} branchId - Branch ID
   * @param {Object} updateData - Update data
   * @param {Object} user - Current user
   * @returns {Promise<Object>} Updated branch
   */
  async updateBranch(branchId, updateData, user = null) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      // Check if customer can only update their own branches
      if (user && user.role === 'customer' && branch.managerId !== user.id) {
        throw new AppError('You can only update branches you manage', 403, 'ACCESS_DENIED');
      }

      // Check name uniqueness if updating name or city
      if ((updateData.name && updateData.name !== branch.name) || 
          (updateData.city && updateData.city !== branch.city)) {
        const existingBranch = await Branch.findOne({
          where: {
            name: updateData.name || branch.name,
            city: updateData.city || branch.city,
            isActive: true,
            id: { [require('sequelize').Op.ne]: branchId }
          }
        });

        if (existingBranch) {
          throw new AppError('Branch with this name already exists in the city', 409, 'BRANCH_NAME_EXISTS');
        }
      }

      // Check phone uniqueness if updating phone
      if (updateData.phone && updateData.phone !== branch.phone) {
        const existingPhone = await Branch.findOne({
          where: {
            phone: updateData.phone,
            isActive: true,
            id: { [require('sequelize').Op.ne]: branchId }
          }
        });

        if (existingPhone) {
          throw new AppError('Phone number already exists', 409, 'PHONE_EXISTS');
        }
      }

      // Check email uniqueness if updating email
      if (updateData.email && updateData.email !== branch.email) {
        const existingEmail = await Branch.findOne({
          where: {
            email: updateData.email,
            isActive: true,
            id: { [require('sequelize').Op.ne]: branchId }
          }
        });

        if (existingEmail) {
          throw new AppError('Email already exists', 409, 'EMAIL_EXISTS');
        }
      }

      // Validate manager if updating
      if (updateData.managerId && updateData.managerId !== branch.managerId) {
        const manager = await User.findByPk(updateData.managerId);
        if (!manager || !manager.isActive) {
          throw new AppError('Manager not found or inactive', 404, 'MANAGER_NOT_FOUND');
        }

        if (!['admin', 'staff', 'customer'].includes(manager.role)) {
          throw new AppError('Manager must be admin, staff, or customer', 400, 'INVALID_MANAGER_ROLE');
        }
      }

      // Validate working days if updating
      if (updateData.workingDays) {
        const validDays = Object.values(WORKING_DAYS);
        const invalidDays = updateData.workingDays.filter(day => !validDays.includes(day));
        if (invalidDays.length > 0) {
          throw new AppError(`Invalid working days: ${invalidDays.join(', ')}`, 400, 'INVALID_WORKING_DAYS');
        }
      }

      await branch.update(updateData);

      logger.business('branch_updated', {
        branchId: branch.id,
        branchName: branch.name,
        updatedFields: Object.keys(updateData)
      });

      return await this.getBranchById(branch.id);

    } catch (error) {
      logger.error('Update branch failed:', error);
      throw error;
    }
  }

  /**
   * Delete branch (soft delete)
   * @param {number} branchId - Branch ID
   * @param {Object} user - Current user
   * @returns {Promise<Object>} Success message
   */
  async deleteBranch(branchId, user = null) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      // Check if customer can only delete their own branches
      if (user && user.role === 'customer' && branch.managerId !== user.id) {
        throw new AppError('You can only delete branches you manage', 403, 'ACCESS_DENIED');
      }

      // Check if branch has active employees
      const activeEmployees = await User.count({
        where: {
          branchId,
          isActive: true
        }
      });

      if (activeEmployees > 0) {
        throw new AppError('Cannot delete branch with active employees', 400, 'BRANCH_HAS_ACTIVE_EMPLOYEES');
      }

      // Soft delete by setting isActive to false
      await branch.update({ isActive: false });

      logger.business('branch_deleted', {
        branchId: branch.id,
        branchName: branch.name
      });

      return { message: 'Branch deleted successfully' };

    } catch (error) {
      logger.error('Delete branch failed:', error);
      throw error;
    }
  }

  /**
   * Toggle branch status
   * @param {number} branchId - Branch ID
   * @param {boolean} isActive - Active status
   * @param {Object} user - Current user
   * @returns {Promise<Object>} Updated branch
   */
  async toggleBranchStatus(branchId, isActive, user = null) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      // Check if customer can only toggle status of their own branches
      if (user && user.role === 'customer' && branch.managerId !== user.id) {
        throw new AppError('You can only manage branches you own', 403, 'ACCESS_DENIED');
      }

      await branch.update({ isActive });

      logger.business('branch_status_changed', {
        branchId: branch.id,
        branchName: branch.name,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return await this.getBranchById(branch.id);

    } catch (error) {
      logger.error('Toggle branch status failed:', error);
      throw error;
    }
  }

  /**
   * Get branches by city
   * @param {string} city - City name
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Branches in the city
   */
  async getBranchesByCity(city, options = {}) {
    try {
      return await Branch.findByCity(city, {
        ...options,
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'name', 'email', 'phone'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get branches by city failed:', error);
      throw error;
    }
  }

  /**
   * Find nearby branches
   * @param {number} latitude - Latitude
   * @param {number} longitude - Longitude
   * @param {number} radiusKm - Search radius in kilometers
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Nearby branches
   */
  async findNearbyBranches(latitude, longitude, radiusKm = 10, options = {}) {
    try {
      if (!latitude || !longitude) {
        throw new AppError('Latitude and longitude are required', 400, 'MISSING_COORDINATES');
      }

      const branches = await Branch.findNearby(latitude, longitude, radiusKm, {
        ...options,
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'name', 'email', 'phone'],
            required: false
          }
        ]
      });

      // Calculate actual distances and sort by distance
      const branchesWithDistance = branches.map(branch => {
        const distance = this.calculateDistance(
          latitude, longitude,
          parseFloat(branch.latitude), parseFloat(branch.longitude)
        );
        
        return {
          ...branch.toJSON(),
          distance: Math.round(distance * 100) / 100 // Round to 2 decimal places
        };
      }).sort((a, b) => a.distance - b.distance);

      return branchesWithDistance;

    } catch (error) {
      logger.error('Find nearby branches failed:', error);
      throw error;
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   * @param {number} lat1 - Latitude 1
   * @param {number} lon1 - Longitude 1
   * @param {number} lat2 - Latitude 2
   * @param {number} lon2 - Longitude 2
   * @returns {number} Distance in kilometers
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   * @param {number} degrees - Degrees
   * @returns {number} Radians
   */
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get branch statistics
   * @returns {Promise<Object>} Branch statistics
   */
  async getBranchStats() {
    try {
      const { Op } = require('sequelize');
      const sequelize = require('../../database/connection').sequelize;

      const totalBranches = await Branch.count({ where: { isActive: true } });

      const branchesByCity = await Branch.findAll({
        attributes: [
          'city',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: { isActive: true },
        group: ['city'],
        raw: true
      });

      const newBranchesThisMonth = await Branch.count({
        where: {
          isActive: true,
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      });

      const branchesWithLocation = await Branch.count({
        where: {
          isActive: true,
          latitude: { [Op.ne]: null },
          longitude: { [Op.ne]: null }
        }
      });

      return {
        total: totalBranches,
        newThisMonth: newBranchesThisMonth,
        withLocation: branchesWithLocation,
        byCity: branchesByCity.reduce((acc, branch) => {
          acc[branch.city] = parseInt(branch.count);
          return acc;
        }, {})
      };

    } catch (error) {
      logger.error('Get branch stats failed:', error);
      throw error;
    }
  }

  /**
   * Get active branches (simple list)
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Active branches
   */
  async getActiveBranches(options = {}) {
    try {
      return await Branch.getActiveBranches({
        ...options,
        attributes: ['id', 'name', 'address', 'city', 'phone'],
        order: [['name', 'ASC']]
      });

    } catch (error) {
      logger.error('Get active branches failed:', error);
      throw error;
    }
  }

  /**
   * Update branch images
   * @param {number} branchId - Branch ID
   * @param {Array} imageUrls - Array of image URLs
   * @param {Object} user - Current user
   * @returns {Promise<Object>} Updated branch
   */
  async updateBranchImages(branchId, imageUrls, user = null) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      // Check if customer can only update images of their own branches
      if (user && user.role === 'customer' && branch.managerId !== user.id) {
        throw new AppError('You can only update images of branches you manage', 403, 'ACCESS_DENIED');
      }

      await branch.update({ images: imageUrls });

      logger.business('branch_images_updated', {
        branchId: branch.id,
        branchName: branch.name,
        imageCount: imageUrls.length
      });

      return await this.getBranchById(branch.id);

    } catch (error) {
      logger.error('Update branch images failed:', error);
      throw error;
    }
  }

  /**
   * Check if branch is open at specific time
   * @param {number} branchId - Branch ID
   * @param {Date} dateTime - Date and time to check
   * @returns {Promise<Object>} Open status
   */
  async checkBranchOpenStatus(branchId, dateTime = new Date()) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      const isOpen = branch.isOpen(dateTime);
      const workingHours = branch.getWorkingHours();

      return {
        isOpen,
        workingHours,
        currentTime: dateTime.toISOString(),
        message: isOpen ? 'Branch is currently open' : 'Branch is currently closed'
      };

    } catch (error) {
      logger.error('Check branch open status failed:', error);
      throw error;
    }
  }
}

module.exports = new BranchesService();
