/**
 * Booking Swagger Documentation
 * API documentation for booking management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Booking:
 *       type: object
 *       required:
 *         - customerId
 *         - serviceId
 *         - branchId
 *         - bookingDate
 *         - startTime
 *       properties:
 *         id:
 *           type: integer
 *           description: Unique booking identifier
 *         bookingCode:
 *           type: string
 *           description: Unique booking code for customer reference
 *         customerId:
 *           type: integer
 *           description: Customer who made the booking
 *         serviceId:
 *           type: integer
 *           description: Service being booked
 *         branchId:
 *           type: integer
 *           description: Branch where service will be provided
 *         employeeId:
 *           type: integer
 *           description: Assigned employee (optional)
 *         bookingDate:
 *           type: string
 *           format: date
 *           description: Date of the booking
 *         startTime:
 *           type: string
 *           format: time
 *           description: Start time of the booking
 *         endTime:
 *           type: string
 *           format: time
 *           description: End time of the booking
 *         duration:
 *           type: integer
 *           description: Duration in minutes
 *         status:
 *           type: string
 *           enum: [pending, confirmed, in_progress, completed, cancelled, no_show]
 *           description: Current booking status
 *         totalAmount:
 *           type: number
 *           format: decimal
 *           description: Total amount for the booking
 *         discountAmount:
 *           type: number
 *           format: decimal
 *           description: Discount applied to the booking
 *         finalAmount:
 *           type: number
 *           format: decimal
 *           description: Final amount after discount
 *         paymentStatus:
 *           type: string
 *           enum: [pending, partial, paid, refunded]
 *           description: Payment status of the booking
 *         notes:
 *           type: string
 *           description: Additional notes for the booking
 *         customerNotes:
 *           type: string
 *           description: Notes from customer
 *         internalNotes:
 *           type: string
 *           description: Internal notes for staff
 *         cancellationReason:
 *           type: string
 *           description: Reason for cancellation
 *         cancelledBy:
 *           type: integer
 *           description: User who cancelled the booking
 *         cancelledAt:
 *           type: string
 *           format: date-time
 *           description: When the booking was cancelled
 *         confirmedBy:
 *           type: integer
 *           description: Staff who confirmed the booking
 *         confirmedAt:
 *           type: string
 *           format: date-time
 *           description: When the booking was confirmed
 *         completedAt:
 *           type: string
 *           format: date-time
 *           description: When the service was completed
 *         reminderSent:
 *           type: boolean
 *           description: Whether reminder notification was sent
 *         rating:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: Customer rating (1-5 stars)
 *         review:
 *           type: string
 *           description: Customer review
 *         isActive:
 *           type: boolean
 *           description: Whether the booking is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         customer:
 *           $ref: '#/components/schemas/Customer'
 *         service:
 *           $ref: '#/components/schemas/Service'
 *         branch:
 *           $ref: '#/components/schemas/Branch'
 *         employee:
 *           $ref: '#/components/schemas/Employee'
 *
 *     CreateBookingRequest:
 *       type: object
 *       required:
 *         - customerId
 *         - serviceId
 *         - branchId
 *         - bookingDate
 *         - startTime
 *       properties:
 *         customerId:
 *           type: integer
 *           description: Customer ID
 *         serviceId:
 *           type: integer
 *           description: Service ID
 *         branchId:
 *           type: integer
 *           description: Branch ID
 *         employeeId:
 *           type: integer
 *           description: Employee ID (optional)
 *         bookingDate:
 *           type: string
 *           format: date
 *           description: Booking date (YYYY-MM-DD)
 *         startTime:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Start time (HH:MM)
 *         discountAmount:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           description: Discount amount
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: Additional notes
 *         customerNotes:
 *           type: string
 *           maxLength: 500
 *           description: Customer notes
 *         internalNotes:
 *           type: string
 *           maxLength: 500
 *           description: Internal notes
 *
 *     UpdateBookingRequest:
 *       type: object
 *       properties:
 *         employeeId:
 *           type: integer
 *           description: Employee ID
 *         startTime:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Start time (HH:MM)
 *         discountAmount:
 *           type: number
 *           format: decimal
 *           minimum: 0
 *           description: Discount amount
 *         notes:
 *           type: string
 *           maxLength: 1000
 *           description: Additional notes
 *         customerNotes:
 *           type: string
 *           maxLength: 500
 *           description: Customer notes
 *         internalNotes:
 *           type: string
 *           maxLength: 500
 *           description: Internal notes
 *
 *     CancelBookingRequest:
 *       type: object
 *       required:
 *         - reason
 *       properties:
 *         reason:
 *           type: string
 *           minLength: 5
 *           maxLength: 500
 *           description: Cancellation reason
 *
 *     CompleteBookingRequest:
 *       type: object
 *       properties:
 *         rating:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: Customer rating
 *         review:
 *           type: string
 *           maxLength: 1000
 *           description: Customer review
 *
 *     BookingStats:
 *       type: object
 *       properties:
 *         totalBookings:
 *           type: integer
 *           description: Total number of bookings
 *         pendingBookings:
 *           type: integer
 *           description: Number of pending bookings
 *         confirmedBookings:
 *           type: integer
 *           description: Number of confirmed bookings
 *         completedBookings:
 *           type: integer
 *           description: Number of completed bookings
 *         cancelledBookings:
 *           type: integer
 *           description: Number of cancelled bookings
 *         totalRevenue:
 *           type: number
 *           format: decimal
 *           description: Total revenue from completed bookings
 *         averageRating:
 *           type: number
 *           format: decimal
 *           description: Average customer rating
 *         completionRate:
 *           type: string
 *           description: Completion rate percentage
 *         cancellationRate:
 *           type: string
 *           description: Cancellation rate percentage
 *
 *     TimeSlot:
 *       type: object
 *       properties:
 *         startTime:
 *           type: string
 *           format: time
 *           description: Slot start time
 *         endTime:
 *           type: string
 *           format: time
 *           description: Slot end time
 *         available:
 *           type: boolean
 *           description: Whether the slot is available
 *
 *   parameters:
 *     BookingId:
 *       name: id
 *       in: path
 *       required: true
 *       schema:
 *         type: integer
 *       description: Booking ID
 *
 *     BookingCode:
 *       name: code
 *       in: path
 *       required: true
 *       schema:
 *         type: string
 *       description: Booking code
 *
 *     BookingFilters:
 *       - name: page
 *         in: query
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - name: limit
 *         in: query
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Items per page
 *       - name: status
 *         in: query
 *         schema:
 *           type: string
 *           enum: [pending, confirmed, in_progress, completed, cancelled, no_show]
 *         description: Filter by booking status
 *       - name: customerId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *       - name: serviceId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by service ID
 *       - name: branchId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - name: employeeId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by employee ID
 *       - name: bookingDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by specific booking date
 *       - name: startDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date range
 *       - name: endDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date range
 *       - name: paymentStatus
 *         in: query
 *         schema:
 *           type: string
 *           enum: [pending, partial, paid, refunded]
 *         description: Filter by payment status
 *       - name: search
 *         in: query
 *         schema:
 *           type: string
 *         description: Search in booking code, notes, and customer notes
 *
 *     AvailableSlotParams:
 *       - name: branchId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *       - name: serviceId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service ID
 *       - name: employeeId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Employee ID (optional)
 *       - name: date
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date to check availability
 */

/**
 * @swagger
 * /api/bookings:
 *   get:
 *     summary: Get all bookings with pagination and filtering
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - name: status
 *         in: query
 *         schema:
 *           type: string
 *           enum: [pending, confirmed, in_progress, completed, cancelled, no_show]
 *         description: Filter by booking status
 *       - name: customerId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *       - name: serviceId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by service ID
 *       - name: branchId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - name: employeeId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by employee ID
 *       - name: bookingDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by booking date
 *       - name: startDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings from this date
 *       - name: endDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter bookings until this date
 *       - name: paymentStatus
 *         in: query
 *         schema:
 *           type: string
 *           enum: [pending, partial, paid, refunded]
 *         description: Filter by payment status
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Booking'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Bookings retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create a new booking
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BookingCreate'
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Booking'
 *                 message:
 *                   type: string
 *                   example: "Booking created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/bookings/stats:
 *   get:
 *     summary: Get booking statistics
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Booking statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/BookingStats'
 *                 message:
 *                   type: string
 *                   example: "Booking statistics retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/bookings/available-slots:
 *   get:
 *     summary: Get available time slots
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: branchId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *       - name: serviceId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Service ID
 *       - name: employeeId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Employee ID (optional)
 *       - name: date
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date to check availability
 *     responses:
 *       200:
 *         description: Available time slots retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       time:
 *                         type: string
 *                         example: "09:00"
 *                       available:
 *                         type: boolean
 *                         example: true
 *                       employeeId:
 *                         type: integer
 *                         example: 5
 *                 message:
 *                   type: string
 *                   example: "Available time slots retrieved successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
