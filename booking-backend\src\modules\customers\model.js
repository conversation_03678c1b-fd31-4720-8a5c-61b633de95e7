/**
 * Customer Model
 * Customer management (extends User model for customer members)
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Reference to User table'
  },
  customerCode: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'customer_code',
    validate: {
      len: [3, 20],
      isAlphanumeric: true
    },
    comment: 'Unique customer code'
  },
  membershipLevel: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'bronze',
    validate: {
      isIn: [['bronze', 'silver', 'gold', 'platinum', 'diamond']]
    },
    field: 'membership_level',
    comment: 'Customer membership level'
  },
  membershipStartDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'membership_start_date',
    comment: 'Membership start date'
  },
  membershipExpiryDate: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'membership_expiry_date',
    comment: 'Membership expiry date'
  },
  loyaltyPoints: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    },
    field: 'loyalty_points',
    comment: 'Accumulated loyalty points'
  },
  totalSpent: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    },
    field: 'total_spent',
    comment: 'Total amount spent in VND'
  },
  totalBookings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'total_bookings',
    comment: 'Total number of bookings'
  },
  completedBookings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'completed_bookings',
    comment: 'Number of completed bookings'
  },
  cancelledBookings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'cancelled_bookings',
    comment: 'Number of cancelled bookings'
  },
  noShowBookings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'no_show_bookings',
    comment: 'Number of no-show bookings'
  },
  preferredBranchId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'preferred_branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Preferred branch ID'
  },
  preferredServices: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'preferred_services',
    comment: 'Array of preferred service IDs'
  },
  preferredEmployees: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'preferred_employees',
    comment: 'Array of preferred employee IDs'
  },
  allergies: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of known allergies'
  },
  medicalConditions: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'medical_conditions',
    comment: 'Array of medical conditions'
  },
  skinType: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      isIn: [['normal', 'dry', 'oily', 'combination', 'sensitive']]
    },
    field: 'skin_type',
    comment: 'Customer skin type'
  },
  preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Customer preferences (music, temperature, etc.)'
  },
  emergencyContact: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'emergency_contact',
    comment: 'Emergency contact information'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes about customer'
  },
  referralCode: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true,
    field: 'referral_code',
    comment: 'Customer referral code'
  },
  referredBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'referred_by',
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'Referred by customer ID'
  },
  referralCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'referral_count',
    comment: 'Number of successful referrals'
  },
  lastVisitDate: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'last_visit_date',
    comment: 'Last visit date'
  },
  averageRating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 5
    },
    field: 'average_rating',
    comment: 'Average rating given by customer'
  },
  isVip: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_vip',
    comment: 'VIP customer status'
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'active',
    validate: {
      isIn: [['active', 'inactive', 'suspended', 'blacklisted']]
    },
    comment: 'Customer status'
  }
}, {
  tableName: 'customers',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete
  
  // Indexes
  indexes: [
    { fields: ['user_id'] },
    { fields: ['customer_code'] },
    { fields: ['membership_level'] },
    { fields: ['preferred_branch_id'] },
    { fields: ['status'] },
    { fields: ['is_vip'] },
    { fields: ['loyalty_points'] },
    { fields: ['total_spent'] },
    { fields: ['referral_code'] },
    { fields: ['referred_by'] },
    { fields: ['last_visit_date'] },
    { fields: ['created_at'] }
  ],
  
  // Scopes
  scopes: {
    active: {
      where: { status: 'active' }
    },
    vip: {
      where: { status: 'active', isVip: true }
    },
    byMembership: (level) => ({
      where: { membershipLevel: level, status: 'active' }
    }),
    byBranch: (branchId) => ({
      where: { preferredBranchId: branchId, status: 'active' }
    }),
    highValue: {
      where: {
        status: 'active',
        totalSpent: { [sequelize.Sequelize.Op.gte]: 5000000 } // 5M VND
      },
      order: [['totalSpent', 'DESC']]
    },
    frequent: {
      where: {
        status: 'active',
        completedBookings: { [sequelize.Sequelize.Op.gte]: 10 }
      },
      order: [['completedBookings', 'DESC']]
    }
  }
});

// Instance methods
Customer.prototype.getFullInfo = async function() {
  const User = require('../auth/model');
  const user = await User.findByPk(this.userId, {
    attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
  });
  
  return {
    ...this.toJSON(),
    user: user ? user.toJSON() : null
  };
};

Customer.prototype.calculateMembershipLevel = function() {
  const totalSpent = parseFloat(this.totalSpent);
  
  if (totalSpent >= 50000000) return 'diamond'; // 50M VND
  if (totalSpent >= 20000000) return 'platinum'; // 20M VND
  if (totalSpent >= 10000000) return 'gold'; // 10M VND
  if (totalSpent >= 5000000) return 'silver'; // 5M VND
  return 'bronze';
};

Customer.prototype.addLoyaltyPoints = async function(points) {
  const newPoints = this.loyaltyPoints + points;
  await this.update({ loyaltyPoints: newPoints });
  return newPoints;
};

Customer.prototype.redeemLoyaltyPoints = async function(points) {
  if (this.loyaltyPoints < points) {
    throw new Error('Insufficient loyalty points');
  }
  
  const newPoints = this.loyaltyPoints - points;
  await this.update({ loyaltyPoints: newPoints });
  return newPoints;
};

Customer.prototype.updateBookingStats = async function(type) {
  const updates = { totalBookings: this.totalBookings + 1 };
  
  switch (type) {
    case 'completed':
      updates.completedBookings = this.completedBookings + 1;
      updates.lastVisitDate = new Date();
      break;
    case 'cancelled':
      updates.cancelledBookings = this.cancelledBookings + 1;
      break;
    case 'no_show':
      updates.noShowBookings = this.noShowBookings + 1;
      break;
  }
  
  await this.update(updates);
};

Customer.prototype.updateSpending = async function(amount) {
  const newTotal = parseFloat(this.totalSpent) + parseFloat(amount);
  const newMembershipLevel = this.calculateMembershipLevel.call({ totalSpent: newTotal });
  
  await this.update({
    totalSpent: newTotal,
    membershipLevel: newMembershipLevel
  });
  
  return newTotal;
};

// Class methods
Customer.getActiveCustomers = function(options = {}) {
  return this.scope('active').findAll(options);
};

Customer.getVipCustomers = function(options = {}) {
  return this.scope('vip').findAll(options);
};

Customer.getCustomersByMembership = function(level, options = {}) {
  return this.scope({ method: ['byMembership', level] }).findAll(options);
};

Customer.getCustomersByBranch = function(branchId, options = {}) {
  return this.scope({ method: ['byBranch', branchId] }).findAll(options);
};

Customer.getHighValueCustomers = function(options = {}) {
  return this.scope('highValue').findAll(options);
};

Customer.getFrequentCustomers = function(options = {}) {
  return this.scope('frequent').findAll(options);
};

Customer.findByCustomerCode = function(customerCode) {
  return this.findOne({ where: { customerCode } });
};

Customer.findByUserId = function(userId) {
  return this.findOne({ where: { userId } });
};

Customer.findByReferralCode = function(referralCode) {
  return this.findOne({ where: { referralCode } });
};

// Define associations
Customer.associate = (models) => {
  // Customer belongs to User
  Customer.belongsTo(models.users, {
    foreignKey: 'user_id',
    as: 'user'
  });

  // Customer belongs to Branch (preferred branch)
  Customer.belongsTo(models.branches, {
    foreignKey: 'preferred_branch_id',
    as: 'preferredBranch',
    allowNull: true
  });

  // Customer belongs to Customer (referrer)
  Customer.belongsTo(models.customers, {
    foreignKey: 'referred_by',
    as: 'referrer',
    allowNull: true
  });

  // Customer has many Customers (referrals)
  Customer.hasMany(models.customers, {
    foreignKey: 'referred_by',
    as: 'referrals'
  });

  // Customer has many Bookings
  Customer.hasMany(models.bookings, {
    foreignKey: 'customer_id',
    as: 'bookings'
  });
};

module.exports = Customer;
