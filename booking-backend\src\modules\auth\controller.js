/**
 * Auth Controller
 * HTTP request handlers for authentication
 */

const AuthService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const logger = require('../../utils/logger');

class AuthController {
  /**
   * Register a new user
   * POST /api/auth/register
   */
  async register(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const userData = req.body;
      const result = await AuthService.register(userData);

      logger.info('User registered successfully', {
        userId: result.user.id,
        email: result.user.email,
        ip: req.ip
      });

      return createdResponse(res, result, 'User registered successfully');

    } catch (error) {
      logger.error('Registration controller error:', error);
      next(error);
    }
  }

  /**
   * Login user
   * POST /api/auth/login
   */
  async login(req, res, next) {
    try {
      // Validate req and res parameters
      if (!req || !res || typeof res.status !== 'function') {
        const error = new Error('req or res is not defined in login controller');
        console.error('Login controller error: req or res is undefined');
        if (next) next(error);
        return;
      }

      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const credentials = req.body;
      const result = await AuthService.login(credentials);

      logger.info('User logged in successfully', {
        userId: result.user.id,
        email: result.user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Format response to match expected structure
      const responseData = {
        token: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn,
        user: {
          id: result.user.id,
          name: result.user.name,
          email: result.user.email,
          role: result.user.role,
          isActive: result.user.isActive
        }
      };

      return successResponse(res, responseData, 'Login successful');

    } catch (error) {
      logger.error('Login controller error:', error);
      next(error);
    }
  }

  /**
   * Refresh access token
   * POST /api/auth/refresh
   */
  async refreshToken(req, res, next) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return errorResponse(res, 'Refresh token is required', 400, 'MISSING_REFRESH_TOKEN');
      }

      const result = await AuthService.refreshToken(refreshToken);

      logger.info('Token refreshed successfully', {
        ip: req.ip
      });

      return successResponse(res, result, 'Token refreshed successfully');

    } catch (error) {
      logger.error('Token refresh controller error:', error);
      next(error);
    }
  }

  /**
   * Logout user
   * POST /api/auth/logout
   */
  async logout(req, res, next) {
    try {
      const userId = req.user.id;
      const result = await AuthService.logout(userId);

      logger.info('User logged out successfully', {
        userId,
        ip: req.ip
      });

      return successResponse(res, result, 'Logged out successfully');

    } catch (error) {
      logger.error('Logout controller error:', error);
      next(error);
    }
  }

  /**
   * Verify email address
   * GET /api/auth/verify-email/:token
   */
  async verifyEmail(req, res, next) {
    try {
      const { token } = req.params;

      if (!token) {
        return errorResponse(res, 'Verification token is required', 400, 'MISSING_TOKEN');
      }

      const result = await AuthService.verifyEmail(token);

      logger.info('Email verified successfully', {
        token: token.substring(0, 8) + '...',
        ip: req.ip
      });

      return successResponse(res, result, 'Email verified successfully');

    } catch (error) {
      logger.error('Email verification controller error:', error);
      next(error);
    }
  }

  /**
   * Request password reset
   * POST /api/auth/forgot-password
   */
  async forgotPassword(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { email } = req.body;
      const result = await AuthService.requestPasswordReset(email);

      logger.info('Password reset requested', {
        email,
        ip: req.ip
      });

      return successResponse(res, result, 'Password reset email sent');

    } catch (error) {
      logger.error('Forgot password controller error:', error);
      next(error);
    }
  }

  /**
   * Reset password
   * POST /api/auth/reset-password
   */
  async resetPassword(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { token, password } = req.body;
      const result = await AuthService.resetPassword(token, password);

      logger.info('Password reset successfully', {
        token: token.substring(0, 8) + '...',
        ip: req.ip
      });

      return successResponse(res, result, 'Password reset successfully');

    } catch (error) {
      logger.error('Reset password controller error:', error);
      next(error);
    }
  }

  /**
   * Change password
   * POST /api/auth/change-password
   */
  async changePassword(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;
      
      const result = await AuthService.changePassword(userId, currentPassword, newPassword);

      logger.info('Password changed successfully', {
        userId,
        ip: req.ip
      });

      return successResponse(res, result, 'Password changed successfully');

    } catch (error) {
      logger.error('Change password controller error:', error);
      next(error);
    }
  }

  /**
   * Get user profile
   * GET /api/auth/profile
   */
  async getProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const profile = await AuthService.getProfile(userId);

      return successResponse(res, profile, 'Profile retrieved successfully');

    } catch (error) {
      logger.error('Get profile controller error:', error);
      next(error);
    }
  }

  /**
   * Update user profile
   * PUT /api/auth/profile
   */
  async updateProfile(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const userId = req.user.id;
      const updateData = req.body;
      
      const updatedProfile = await AuthService.updateProfile(userId, updateData);

      logger.info('Profile updated successfully', {
        userId,
        updatedFields: Object.keys(updateData),
        ip: req.ip
      });

      return successResponse(res, updatedProfile, 'Profile updated successfully');

    } catch (error) {
      logger.error('Update profile controller error:', error);
      next(error);
    }
  }

  /**
   * Get current user info (from token)
   * GET /api/auth/me
   */
  async getCurrentUser(req, res, next) {
    try {
      const user = req.user;

      // Remove sensitive information
      const safeUser = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      };

      return successResponse(res, safeUser, 'Current user retrieved successfully');

    } catch (error) {
      logger.error('Get current user controller error:', error);
      next(error);
    }
  }

  /**
   * Check if email exists
   * POST /api/auth/check-email
   */
  async checkEmail(req, res, next) {
    try {
      const { email } = req.body;

      if (!email) {
        return errorResponse(res, 'Email is required', 400, 'MISSING_EMAIL');
      }

      const User = require('./model');
      const existingUser = await User.findByEmail(email);

      return successResponse(res, {
        exists: !!existingUser,
        email
      }, 'Email check completed');

    } catch (error) {
      logger.error('Check email controller error:', error);
      next(error);
    }
  }

  /**
   * Resend email verification
   * POST /api/auth/resend-verification
   */
  async resendVerification(req, res, next) {
    try {
      const { email } = req.body;

      if (!email) {
        return errorResponse(res, 'Email is required', 400, 'MISSING_EMAIL');
      }

      // TODO: Implement resend verification logic
      // For now, just return success message
      
      logger.info('Verification email resent', {
        email,
        ip: req.ip
      });

      return successResponse(res, {
        message: 'Verification email sent'
      }, 'Verification email sent successfully');

    } catch (error) {
      logger.error('Resend verification controller error:', error);
      next(error);
    }
  }
}

module.exports = new AuthController();
