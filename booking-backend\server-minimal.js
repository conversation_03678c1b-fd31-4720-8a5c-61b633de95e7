// Simple server start without complex database initialization
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');

console.log('🚀 Starting server with minimal database...');

const app = express();
const PORT = process.env.PORT || 3001;

// Basic middleware
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001'
  ],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Mock database for forms
const mockForms = [
  {
    id: 1,
    slug: 'test-public-form',
    title: 'Test Public Form',
    description: 'Test form for public booking',
    isPublic: true,
    isActive: true,
    service: { name: 'Test Service' },
    branch: { name: 'Test Branch' },
    publicUrl: `http://localhost:3000/book/test-public-form`,
    embedCode: `<iframe src="http://localhost:3000/book/test-public-form" width="100%" height="600"></iframe>`,
    qrCodeUrl: `http://localhost:3001/api/forms/1/qr`,
    socialUrls: {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=http://localhost:3000/book/test-public-form`,
      twitter: `https://twitter.com/intent/tweet?url=http://localhost:3000/book/test-public-form`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=http://localhost:3000/book/test-public-form`
    }
  }
];

const mockBookings = [];

// Public endpoints
app.get('/api/public/forms/:slug', (req, res) => {
  const { slug } = req.params;
  console.log('📋 Public form request for slug:', slug);
  
  const form = mockForms.find(f => f.slug === slug);
  if (form) {
    res.json({
      success: true,
      data: form
    });
  } else {
    res.status(404).json({
      success: false,
      error: {
        code: 'FORM_NOT_FOUND',
        message: 'Form not found'
      }
    });
  }
});

app.post('/api/public/bookings', async (req, res) => {
  try {
    console.log('📝 Public booking request:', req.body);
    
    const { formSlug, customerName, phoneNumber, emailAddress, preferredDate, preferredTime, specialRequests } = req.body;
    
    // Basic validation
    if (!formSlug || !customerName || !phoneNumber || !emailAddress) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields'
        }
      });
    }
    
    // Check if form exists
    const form = mockForms.find(f => f.slug === formSlug);
    if (!form) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'FORM_NOT_FOUND',
          message: 'Form not found'
        }
      });
    }
    
    // Create mock user with hashed password (simulating the fixed logic)
    const randomPassword = Math.random().toString(36).substring(2, 15);
    const hashedPassword = await bcrypt.hash(randomPassword, 10);
    
    console.log('✅ Password hashed successfully for user creation');
    
    // Create booking
    const booking = {
      id: mockBookings.length + 1,
      formSlug,
      customerName,
      phoneNumber,
      emailAddress,
      preferredDate,
      preferredTime,
      specialRequests: specialRequests || '',
      status: 'pending',
      createdAt: new Date().toISOString(),
      // Mock user creation (this would be real database in production)
      user: {
        id: mockBookings.length + 100,
        email: emailAddress,
        name: customerName,
        phone: phoneNumber,
        role: 'customer',
        hashedPassword: '***hashed***'
      }
    };
    
    mockBookings.push(booking);
    
    console.log('✅ Booking created successfully:', {
      id: booking.id,
      customerName: booking.customerName,
      status: booking.status,
      formSlug: booking.formSlug
    });
    
    res.json({
      success: true,
      data: {
        id: booking.id,
        status: booking.status,
        customerName: booking.customerName,
        preferredDate: booking.preferredDate,
        preferredTime: booking.preferredTime,
        message: 'Booking submitted successfully! You will receive a confirmation email shortly.'
      }
    });
    
  } catch (error) {
    console.error('❌ Booking creation error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'BOOKING_ERROR',
        message: 'Failed to create booking. Please try again.'
      }
    });
  }
});

// Forms API for frontend
app.get('/api/forms', (req, res) => {
  console.log('📋 Forms list request');
  
  const formsWithStats = mockForms.map(form => ({
    ...form,
    bookingsThisMonth: mockBookings.filter(b => b.formSlug === form.slug).length,
    lastBooking: mockBookings.filter(b => b.formSlug === form.slug).pop()?.createdAt || null,
    createdAt: '2025-01-01T00:00:00Z'
  }));
  
  res.json({
    success: true,
    data: formsWithStats,
    pagination: {
      page: 1,
      limit: 10,
      total: formsWithStats.length,
      pages: 1
    }
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    bookingsCount: mockBookings.length
  });
});

// Test endpoints
app.get('/api/test/bookings', (req, res) => {
  res.json({
    success: true,
    data: mockBookings,
    count: mockBookings.length
  });
});

app.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📋 Forms API: http://localhost:${PORT}/api/forms`);
  console.log(`📝 Public booking: http://localhost:${PORT}/api/public/bookings`);
  console.log(`📊 Test bookings: http://localhost:${PORT}/api/test/bookings`);
  console.log('');
  console.log('🎯 Ready to test with frontend!');
});
