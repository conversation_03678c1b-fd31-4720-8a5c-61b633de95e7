/**
 * Notification Service
 * Business logic for notification management
 */

const { Op, sequelize } = require('sequelize');
const Notification = require('./model');
const User = require('../users/model');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class NotificationService {
  /**
   * Create a new notification
   */
  static async createNotification(notificationData, userId) {
    try {
      // Validate recipient exists
      if (notificationData.recipientId) {
        const recipient = await User.findByPk(notificationData.recipientId);
        if (!recipient) {
          throw new AppError('Recipient not found', 404, 'RECIPIENT_NOT_FOUND');
        }
      }

      // Create notification
      const notification = await Notification.create({
        recipientId: notificationData.recipientId,
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        data: notificationData.data,
        channel: notificationData.channel || 'in_app',
        priority: notificationData.priority || 'medium',
        category: notificationData.category,
        templateId: notificationData.templateId,
        scheduledAt: notificationData.scheduledAt,
        expiresAt: notificationData.expiresAt,
        createdBy: userId
      });

      logger.info('Notification created successfully', {
        notificationId: notification.id,
        recipientId: notificationData.recipientId,
        type: notificationData.type,
        createdBy: userId
      });

      return await this.getNotificationById(notification.id);
    } catch (error) {
      logger.error('Create notification failed', {
        error: error.message,
        notificationData,
        userId
      });
      throw error;
    }
  }

  /**
   * Get all notifications with pagination and filtering
   */
  static async getAllNotifications(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        recipientId,
        type,
        status,
        channel,
        priority,
        category,
        isRead,
        startDate,
        endDate,
        search
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (recipientId) whereClause.recipientId = recipientId;
      if (type) whereClause.type = type;
      if (status) whereClause.status = status;
      if (channel) whereClause.channel = channel;
      if (priority) whereClause.priority = priority;
      if (category) whereClause.category = category;
      if (typeof isRead === 'boolean') whereClause.isRead = isRead;

      if (startDate && endDate) {
        whereClause.createdAt = {
          [Op.between]: [startDate, endDate]
        };
      }

      if (search) {
        whereClause[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { message: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const { count, rows } = await Notification.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'recipient',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: User,
            as: 'createdByUser',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        notifications: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          nextPage: page < totalPages ? parseInt(page) + 1 : null,
          prevPage: page > 1 ? parseInt(page) - 1 : null
        }
      };
    } catch (error) {
      logger.error('Get all notifications failed', { error: error.message, options });
      throw error;
    }
  }

  /**
   * Get notification by ID
   */
  static async getNotificationById(id) {
    try {
      const notification = await Notification.findByPk(id, {
        include: [
          {
            model: User,
            as: 'recipient',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: User,
            as: 'createdByUser',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!notification) {
        throw new AppError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND');
      }

      return notification;
    } catch (error) {
      logger.error('Get notification by ID failed', { error: error.message, notificationId: id });
      throw error;
    }
  }

  /**
   * Get notifications for a specific user
   */
  static async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        type,
        status,
        isRead,
        priority
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = { recipientId: userId };

      if (type) whereClause.type = type;
      if (status) whereClause.status = status;
      if (typeof isRead === 'boolean') whereClause.isRead = isRead;
      if (priority) whereClause.priority = priority;

      const { count, rows } = await Notification.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'createdByUser',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        notifications: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          nextPage: page < totalPages ? parseInt(page) + 1 : null,
          prevPage: page > 1 ? parseInt(page) - 1 : null
        }
      };
    } catch (error) {
      logger.error('Get user notifications failed', { error: error.message, userId, options });
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(id, userId) {
    try {
      const notification = await this.getNotificationById(id);

      // Check if user is the recipient
      if (notification.recipientId !== userId) {
        throw new AppError('Unauthorized to mark this notification as read', 403, 'UNAUTHORIZED');
      }

      await notification.update({
        isRead: true,
        readAt: new Date()
      });

      logger.info('Notification marked as read', { notificationId: id, userId });
      return await this.getNotificationById(id);
    } catch (error) {
      logger.error('Mark notification as read failed', { error: error.message, notificationId: id, userId });
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId) {
    try {
      const result = await Notification.update(
        {
          isRead: true,
          readAt: new Date()
        },
        {
          where: {
            recipientId: userId,
            isRead: false
          }
        }
      );

      logger.info('All notifications marked as read', { userId, updatedCount: result[0] });
      return { message: 'All notifications marked as read', updatedCount: result[0] };
    } catch (error) {
      logger.error('Mark all notifications as read failed', { error: error.message, userId });
      throw error;
    }
  }

  /**
   * Update notification
   */
  static async updateNotification(id, updateData, userId) {
    try {
      const notification = await this.getNotificationById(id);

      await notification.update(updateData);

      logger.info('Notification updated successfully', { notificationId: id, updateData, updatedBy: userId });
      return await this.getNotificationById(id);
    } catch (error) {
      logger.error('Update notification failed', { error: error.message, notificationId: id, updateData, userId });
      throw error;
    }
  }

  /**
   * Send notification (update status to sent)
   */
  static async sendNotification(id, userId) {
    try {
      const notification = await this.getNotificationById(id);

      await notification.update({
        status: 'sent',
        sentAt: new Date()
      });

      logger.info('Notification sent successfully', { notificationId: id, sentBy: userId });
      return await this.getNotificationById(id);
    } catch (error) {
      logger.error('Send notification failed', { error: error.message, notificationId: id, userId });
      throw error;
    }
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(options = {}) {
    try {
      const { startDate, endDate, recipientId } = options;
      const whereClause = {};

      if (startDate && endDate) {
        whereClause.createdAt = { [Op.between]: [startDate, endDate] };
      }
      if (recipientId) whereClause.recipientId = recipientId;

      const totalNotifications = await Notification.count({ where: whereClause });
      
      const statusStats = await Notification.findAll({
        where: whereClause,
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['status'],
        raw: true
      });

      const typeStats = await Notification.findAll({
        where: whereClause,
        attributes: ['type', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['type'],
        raw: true
      });

      const readStats = await Notification.findAll({
        where: whereClause,
        attributes: ['isRead', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        group: ['isRead'],
        raw: true
      });

      return { totalNotifications, statusStats, typeStats, readStats };
    } catch (error) {
      logger.error('Get notification stats failed', { error: error.message, options });
      throw error;
    }
  }

  /**
   * Delete notification (soft delete)
   */
  static async deleteNotification(id, userId) {
    try {
      const notification = await this.getNotificationById(id);
      await notification.destroy();

      logger.info('Notification deleted successfully', { notificationId: id, deletedBy: userId });
      return { message: 'Notification deleted successfully' };
    } catch (error) {
      logger.error('Delete notification failed', { error: error.message, notificationId: id, userId });
      throw error;
    }
  }
}

module.exports = NotificationService;
