<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Authentication Test</h1>
        <p>This page tests the authentication flow with real localStorage and token validation.</p>
        
        <div class="test-section info">
            <h3>📊 Current State</h3>
            <div id="currentState"></div>
        </div>
        
        <div class="test-section">
            <h3>🔐 Actions</h3>
            <button onclick="testLogin()">1. Test Login</button>
            <button onclick="testTokenValidation()">2. Test Token Validation</button>
            <button onclick="testAuthService()">3. Test AuthService</button>
            <button onclick="simulatePageRefresh()">4. Simulate Page Refresh</button>
            <button onclick="testLogout()">5. Test Logout</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="test-section">
            <h3>📝 Test Logs</h3>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script>
        // Mock console.log to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            if (type === 'error') logEntry.style.color = 'red';
            if (type === 'success') logEntry.style.color = 'green';
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also log to console
            originalLog(message);
        }
        
        console.log = (...args) => {
            log(args.join(' '));
            originalLog(...args);
        };
        
        console.error = (...args) => {
            log(args.join(' '), 'error');
            originalError(...args);
        };
        
        // Token Manager (same as frontend)
        const tokenManager = {
            getToken: () => localStorage.getItem('accessToken'),
            getRefreshToken: () => localStorage.getItem('refreshToken'),
            getUser: () => {
                const user = localStorage.getItem('user');
                return user ? JSON.parse(user) : null;
            },
            setToken: (token) => {
                localStorage.setItem('accessToken', token);
                document.cookie = `accessToken=${token}; path=/; secure; samesite=strict`;
            },
            setRefreshToken: (token) => localStorage.setItem('refreshToken', token),
            setUser: (user) => localStorage.setItem('user', JSON.stringify(user)),
            removeTokens: () => {
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('user');
                document.cookie = 'accessToken=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
            },
            isTokenValid: () => {
                const token = tokenManager.getToken();
                if (!token) {
                    log('🔍 Token validation: No token found');
                    return false;
                }
                
                try {
                    const parts = token.split('.');
                    if (parts.length !== 3) {
                        log('🔍 Token validation: Invalid JWT format, parts: ' + parts.length);
                        return false;
                    }
                    
                    const payload = JSON.parse(atob(parts[1]));
                    const currentTime = Math.floor(Date.now() / 1000);
                    
                    log('🔍 Token validation: ' + JSON.stringify({
                        hasExp: !!payload.exp,
                        exp: payload.exp,
                        currentTime: currentTime,
                        timeUntilExpiry: payload.exp ? (payload.exp - currentTime) : 'N/A',
                        isValid: payload.exp && payload.exp > (currentTime + 30)
                    }));
                    
                    return payload.exp && payload.exp > (currentTime + 30);
                } catch (error) {
                    log('🔍 Token validation error: ' + error.message, 'error');
                    return false;
                }
            }
        };
        
        // Auth Service (same as frontend)
        const authService = {
            isAuthenticated: () => {
                const token = tokenManager.getToken();
                const user = tokenManager.getUser();
                const isTokenValid = tokenManager.isTokenValid();
                
                log('🔍 AuthService.isAuthenticated(): ' + JSON.stringify({
                    hasToken: !!token,
                    hasUser: !!user,
                    isTokenValid: isTokenValid,
                    tokenLength: token ? token.length : 0,
                    userEmail: user ? user.email : null
                }));
                
                const result = !!(token && user && isTokenValid);
                log('🔍 AuthService.isAuthenticated() result: ' + result);
                return result;
            },
            getCurrentUser: () => tokenManager.getUser()
        };
        
        // Test functions
        async function testLogin() {
            log('🧪 Testing login...', 'info');
            try {
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    const { token: accessToken, refreshToken, user } = data.data;
                    
                    tokenManager.setToken(accessToken);
                    tokenManager.setRefreshToken(refreshToken);
                    tokenManager.setUser(user);
                    
                    log('✅ Login successful! Tokens stored.', 'success');
                    updateCurrentState();
                } else {
                    log('❌ Login failed: ' + data.message, 'error');
                }
            } catch (error) {
                log('❌ Login error: ' + error.message, 'error');
            }
        }
        
        function testTokenValidation() {
            log('🧪 Testing token validation...', 'info');
            const isValid = tokenManager.isTokenValid();
            log(isValid ? '✅ Token is valid' : '❌ Token is invalid', isValid ? 'success' : 'error');
        }
        
        function testAuthService() {
            log('🧪 Testing AuthService...', 'info');
            const isAuth = authService.isAuthenticated();
            const user = authService.getCurrentUser();
            log('📊 AuthService results: isAuthenticated=' + isAuth + ', user=' + (user ? user.email : 'null'));
        }
        
        function simulatePageRefresh() {
            log('🧪 Simulating page refresh (AuthContext initialization)...', 'info');
            
            // Simulate AuthContext.initializeAuth()
            const isAuth = authService.isAuthenticated();
            const currentUser = authService.getCurrentUser();
            const token = tokenManager.getToken();
            
            log('🔍 AuthContext: Authentication check results: ' + JSON.stringify({
                isAuth,
                hasCurrentUser: !!currentUser,
                hasToken: !!token,
                currentUser: currentUser ? { id: currentUser.id, email: currentUser.email } : null
            }));
            
            if (isAuth && currentUser && token) {
                log('✅ AuthContext: User authenticated, would stay on current page', 'success');
            } else {
                log('❌ AuthContext: User not authenticated, would redirect to login', 'error');
            }
        }
        
        function testLogout() {
            log('🧪 Testing logout...', 'info');
            tokenManager.removeTokens();
            log('✅ Tokens cleared from localStorage', 'success');
            updateCurrentState();
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function updateCurrentState() {
            const token = tokenManager.getToken();
            const user = tokenManager.getUser();
            const isValid = token ? tokenManager.isTokenValid() : false;
            const isAuth = authService.isAuthenticated();
            
            const stateElement = document.getElementById('currentState');
            stateElement.innerHTML = `
                <strong>Token:</strong> ${token ? 'Present (' + token.length + ' chars)' : 'None'}<br>
                <strong>User:</strong> ${user ? user.email + ' (' + user.role + ')' : 'None'}<br>
                <strong>Token Valid:</strong> ${isValid ? '✅ Yes' : '❌ No'}<br>
                <strong>Is Authenticated:</strong> ${isAuth ? '✅ Yes' : '❌ No'}
            `;
        }
        
        // Initialize
        updateCurrentState();
        log('🚀 Frontend Authentication Test initialized');
    </script>
</body>
</html>
