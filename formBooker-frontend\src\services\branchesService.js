/**
 * Branches Service
 * Handles all branches-related API calls
 */

import api, { endpoints } from '@/lib/api';

class BranchesService {
  /**
   * Get all branches with pagination and filters
   * Now supports all user roles (admin, staff, customer) with role-based filtering on backend
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.city - Filter by city
   * @param {string} params.district - Filter by district
   * @param {boolean} params.isActive - Filter by active status
   * @param {string} params.search - Search term
   * @param {string} params.sort - Sort order
   * @returns {Promise<Object>} Branches response
   */
  async getBranches(params = {}) {
    try {
      const endpoint = endpoints.branches.list;
      const requestParams = { ...params };

      // Remove userRole from request params as it's not needed
      delete requestParams.userRole;

      const response = await api.get(endpoint, { params: requestParams });

      return {
        success: true,
        data: response.data.data,
        pagination: response.data.pagination,
        message: response.data.message || 'Branches retrieved successfully',
      };
    } catch (error) {
      console.error('Get branches error:', error);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error?.message ||
                          'Failed to fetch branches. Please try again.';

      const validationErrors = error.response?.data?.errors || [];

      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Get branch by ID
   * @param {number} id - Branch ID
   * @returns {Promise<Object>} Branch response
   */
  async getBranchById(id) {
    try {
      const response = await api.get(endpoints.branches.update(id));
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Branch retrieved successfully',
      };
    } catch (error) {
      console.error('Get branch by ID error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to fetch branch. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Create a new branch
   * @param {Object} branchData - Branch data
   * @param {string} branchData.name - Branch name (required)
   * @param {string} branchData.address - Full address (required)
   * @param {string} branchData.phone - Phone number (required)
   * @param {string} branchData.email - Email address (optional)
   * @param {string} branchData.city - City name (required)
   * @param {string} branchData.district - District name (optional)
   * @param {string} branchData.ward - Ward name (optional)
   * @param {number} branchData.latitude - Latitude (optional)
   * @param {number} branchData.longitude - Longitude (optional)
   * @param {string} branchData.openTime - Opening time in HH:MM format (optional)
   * @param {string} branchData.closeTime - Closing time in HH:MM format (optional)
   * @param {Array} branchData.workingDays - Array of working days (optional)
   * @param {string} branchData.description - Description (optional)
   * @param {Array} branchData.images - Array of image URLs (optional)
   * @returns {Promise<Object>} Create response
   */
  async createBranch(branchData) {
    try {
      const response = await api.post(endpoints.branches.create, branchData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Branch created successfully',
      };
    } catch (error) {
      console.error('Create branch error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to create branch. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Update branch
   * @param {number} id - Branch ID
   * @param {Object} branchData - Updated branch data
   * @returns {Promise<Object>} Update response
   */
  async updateBranch(id, branchData) {
    try {
      const response = await api.put(endpoints.branches.update(id), branchData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Branch updated successfully',
      };
    } catch (error) {
      console.error('Update branch error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update branch. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Delete branch (soft delete)
   * @param {number} id - Branch ID
   * @returns {Promise<Object>} Delete response
   */
  async deleteBranch(id) {
    try {
      const response = await api.delete(endpoints.branches.delete(id));
      
      return {
        success: true,
        message: response.data.message || 'Branch deleted successfully',
      };
    } catch (error) {
      console.error('Delete branch error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to delete branch. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Toggle branch status (active/inactive)
   * @param {number} id - Branch ID
   * @param {boolean} isActive - New active status
   * @returns {Promise<Object>} Toggle response
   */
  async toggleBranchStatus(id, isActive) {
    try {
      const response = await api.patch(`${endpoints.branches.update(id)}/status`, {
        isActive
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || `Branch ${isActive ? 'activated' : 'deactivated'} successfully`,
      };
    } catch (error) {
      console.error('Toggle branch status error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error?.message ||
                          'Failed to update branch status. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Get active branches (simple list)
   * @param {number} limit - Maximum number of branches to return
   * @returns {Promise<Object>} Active branches response
   */
  async getActiveBranches(limit = 50) {
    try {
      const response = await api.get('/branches/active', { 
        params: { limit } 
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Active branches retrieved successfully',
      };
    } catch (error) {
      console.error('Get active branches error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch active branches. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Find nearby branches
   * @param {number} latitude - Latitude
   * @param {number} longitude - Longitude
   * @param {number} radius - Search radius in kilometers
   * @param {number} limit - Maximum number of branches to return
   * @returns {Promise<Object>} Nearby branches response
   */
  async findNearbyBranches(latitude, longitude, radius = 10, limit = 20) {
    try {
      const response = await api.get('/branches/nearby', {
        params: { latitude, longitude, radius, limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Nearby branches retrieved successfully',
      };
    } catch (error) {
      console.error('Find nearby branches error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to find nearby branches. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Get branches by city
   * @param {string} city - City name
   * @param {number} limit - Maximum number of branches to return
   * @returns {Promise<Object>} Branches by city response
   */
  async getBranchesByCity(city, limit = 50) {
    try {
      const response = await api.get(`/branches/city/${encodeURIComponent(city)}`, {
        params: { limit }
      });
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Branches retrieved successfully',
      };
    } catch (error) {
      console.error('Get branches by city error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch branches by city. Please try again.';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }
}

// Export singleton instance
export default new BranchesService();
