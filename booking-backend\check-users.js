/**
 * Check Users Script
 * Check existing users in the database and create admin if needed
 */

const { sequelize } = require('./src/database/connection');
const User = require('./src/modules/auth/model');

async function checkUsers() {
  try {
    console.log('🔍 Checking existing users in database...\n');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Get all users
    const users = await User.findAll({
      attributes: ['id', 'email', 'name', 'role', 'isActive', 'createdAt']
    });

    console.log(`\n📊 Found ${users.length} users in database:`);
    
    if (users.length === 0) {
      console.log('   No users found in database');
    } else {
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.email})`);
        console.log(`      Role: ${user.role}`);
        console.log(`      Active: ${user.isActive}`);
        console.log(`      Created: ${user.createdAt}`);
        console.log('');
      });
    }

    // Check if admin user exists
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    
    if (!adminUser) {
      console.log('❌ No admin user found. Creating admin user...');
      
      try {
        const newAdmin = await User.create({
          email: '<EMAIL>',
          password: 'admin123456',
          name: 'System Administrator',
          phone: '0123456789',
          role: 'admin',
          isActive: true,
          isEmailVerified: true
        });

        console.log('✅ Admin user created successfully:');
        console.log(`   ID: ${newAdmin.id}`);
        console.log(`   Email: ${newAdmin.email}`);
        console.log(`   Name: ${newAdmin.name}`);
        console.log(`   Role: ${newAdmin.role}`);
      } catch (createError) {
        console.log('❌ Failed to create admin user:', createError.message);
      }
    } else {
      console.log('✅ Admin user exists:');
      console.log(`   ID: ${adminUser.id}`);
      console.log(`   Email: ${adminUser.email}`);
      console.log(`   Name: ${adminUser.name}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Active: ${adminUser.isActive}`);
    }

    // Check if customer user exists
    const customerUser = await User.findOne({ where: { role: 'customer' } });
    
    if (!customerUser) {
      console.log('\n❌ No customer user found. Creating customer user...');
      
      try {
        const newCustomer = await User.create({
          email: '<EMAIL>',
          password: 'customer123456',
          name: 'Test Customer',
          phone: '0987654321',
          role: 'customer',
          isActive: true,
          isEmailVerified: true
        });

        console.log('✅ Customer user created successfully:');
        console.log(`   ID: ${newCustomer.id}`);
        console.log(`   Email: ${newCustomer.email}`);
        console.log(`   Name: ${newCustomer.name}`);
        console.log(`   Role: ${newCustomer.role}`);
      } catch (createError) {
        console.log('❌ Failed to create customer user:', createError.message);
      }
    } else {
      console.log('\n✅ Customer user exists:');
      console.log(`   ID: ${customerUser.id}`);
      console.log(`   Email: ${customerUser.email}`);
      console.log(`   Name: ${customerUser.name}`);
      console.log(`   Role: ${customerUser.role}`);
      console.log(`   Active: ${customerUser.isActive}`);
    }

    console.log('\n🎉 User check completed!');
    console.log('\n📋 Test Credentials:');
    console.log('Admin Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123456');
    console.log('  Expected Role: admin');
    console.log('');
    console.log('Customer Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: customer123456');
    console.log('  Expected Role: customer');

  } catch (error) {
    console.error('❌ Error checking users:', error.message);
    console.error(error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run the check
checkUsers();
