/**
 * Migration: Create Forms Table
 * Creates the forms table for storing booking form configurations
 */

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('forms', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Form name (e.g., Hair Cut Booking Form)'
      },
      slug: {
        type: Sequelize.STRING(150),
        allowNull: false,
        unique: true,
        comment: 'URL-friendly form identifier'
      },
      service_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'services',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Associated service ID'
      },
      branch_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'branches',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Associated branch ID'
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Form owner (business user)'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'draft'),
        defaultValue: 'active',
        comment: 'Form status'
      },
      fields_config: {
        type: Sequelize.JSON,
        defaultValue: {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        comment: 'Form fields configuration'
      },
      branding_config: {
        type: Sequelize.JSON,
        defaultValue: {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        },
        comment: 'Form branding and customization'
      },
      booking_count: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        comment: 'Total bookings received through this form'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create indexes for better query performance
    await queryInterface.addIndex('forms', ['slug']);
    await queryInterface.addIndex('forms', ['user_id']);
    await queryInterface.addIndex('forms', ['service_id']);
    await queryInterface.addIndex('forms', ['branch_id']);
    await queryInterface.addIndex('forms', ['status']);
    await queryInterface.addIndex('forms', ['is_active']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('forms');
  }
};
