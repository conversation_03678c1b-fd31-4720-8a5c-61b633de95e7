/**
 * Customers Routes
 * API routes for customer management
 */

const express = require('express');
const router = express.Router();
const CustomersController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validatePagination, validateIdParam } = require('../../middleware/validation');
const { adminLimiter, generalLimiter } = require('../../middleware/rateLimiter');
const { body, query, param } = require('express-validator');

// Validation rules
const createCustomerValidation = [
  body('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID required'),
  body('customerCode')
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('Customer code must be 3-20 alphanumeric characters'),
  body('membershipLevel')
    .optional()
    .isIn(['bronze', 'silver', 'gold', 'platinum', 'diamond'])
    .withMessage('Invalid membership level'),
  body('membershipStartDate')
    .optional()
    .isISO8601()
    .withMessage('Valid membership start date required'),
  body('membershipExpiryDate')
    .optional()
    .isISO8601()
    .withMessage('Valid membership expiry date required'),
  body('loyaltyPoints')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Loyalty points must be non-negative'),
  body('preferredBranchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid preferred branch ID required'),
  body('preferredServices')
    .optional()
    .isArray()
    .withMessage('Preferred services must be an array'),
  body('preferredEmployees')
    .optional()
    .isArray()
    .withMessage('Preferred employees must be an array'),
  body('allergies')
    .optional()
    .isArray()
    .withMessage('Allergies must be an array'),
  body('medicalConditions')
    .optional()
    .isArray()
    .withMessage('Medical conditions must be an array'),
  body('skinType')
    .optional()
    .isIn(['normal', 'dry', 'oily', 'combination', 'sensitive'])
    .withMessage('Invalid skin type'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('Preferences must be an object'),
  body('emergencyContact')
    .optional()
    .isObject()
    .withMessage('Emergency contact must be an object'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('referralCode')
    .optional()
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Referral code must be 3-20 characters'),
  body('referredBy')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid referrer ID required')
];

const updateCustomerValidation = [
  body('customerCode')
    .optional()
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('Customer code must be 3-20 alphanumeric characters'),
  body('membershipLevel')
    .optional()
    .isIn(['bronze', 'silver', 'gold', 'platinum', 'diamond'])
    .withMessage('Invalid membership level'),
  body('membershipStartDate')
    .optional()
    .isISO8601()
    .withMessage('Valid membership start date required'),
  body('membershipExpiryDate')
    .optional()
    .isISO8601()
    .withMessage('Valid membership expiry date required'),
  body('loyaltyPoints')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Loyalty points must be non-negative'),
  body('totalSpent')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total spent must be non-negative'),
  body('preferredBranchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid preferred branch ID required'),
  body('preferredServices')
    .optional()
    .isArray()
    .withMessage('Preferred services must be an array'),
  body('preferredEmployees')
    .optional()
    .isArray()
    .withMessage('Preferred employees must be an array'),
  body('allergies')
    .optional()
    .isArray()
    .withMessage('Allergies must be an array'),
  body('medicalConditions')
    .optional()
    .isArray()
    .withMessage('Medical conditions must be an array'),
  body('skinType')
    .optional()
    .isIn(['normal', 'dry', 'oily', 'combination', 'sensitive'])
    .withMessage('Invalid skin type'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('Preferences must be an object'),
  body('emergencyContact')
    .optional()
    .isObject()
    .withMessage('Emergency contact must be an object'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('referralCode')
    .optional()
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Referral code must be 3-20 characters'),
  body('isVip')
    .optional()
    .isBoolean()
    .withMessage('isVip must be a boolean'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'blacklisted'])
    .withMessage('Invalid customer status')
];

const updateStatusValidation = [
  body('status')
    .isIn(['active', 'inactive', 'suspended', 'blacklisted'])
    .withMessage('Invalid customer status')
];

const toggleVipValidation = [
  body('isVip')
    .isBoolean()
    .withMessage('isVip is required and must be a boolean')
];

const loyaltyPointsValidation = [
  body('points')
    .isInt({ min: 1 })
    .withMessage('Points must be a positive integer')
];

const searchValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

const getCustomersValidation = [
  query('membershipLevel')
    .optional()
    .isIn(['bronze', 'silver', 'gold', 'platinum', 'diamond'])
    .withMessage('Invalid membership level'),
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'blacklisted'])
    .withMessage('Invalid customer status'),
  query('isVip')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isVip must be true or false'),
  query('preferredBranchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid preferred branch ID required'),
  query('minSpent')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum spent must be non-negative'),
  query('maxSpent')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum spent must be non-negative'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/customers
 * @desc    Get all customers with pagination and filters
 * @access  Private (Admin, Staff)
 */
router.get('/', 
  authorize(['admin', 'staff']),
  getCustomersValidation,
  validatePagination,
  validateRequest,
  CustomersController.getCustomers
);

/**
 * @route   GET /api/customers/stats
 * @desc    Get customer statistics
 * @access  Private (Admin)
 */
router.get('/stats', 
  authorize(['admin']),
  CustomersController.getCustomerStats
);

/**
 * @route   GET /api/customers/vip
 * @desc    Get VIP customers
 * @access  Private (Admin, Staff)
 */
router.get('/vip', 
  authorize(['admin', 'staff']),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  CustomersController.getVipCustomers
);

/**
 * @route   GET /api/customers/high-value
 * @desc    Get high value customers
 * @access  Private (Admin, Staff)
 */
router.get('/high-value', 
  authorize(['admin', 'staff']),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  CustomersController.getHighValueCustomers
);

/**
 * @route   GET /api/customers/frequent
 * @desc    Get frequent customers
 * @access  Private (Admin, Staff)
 */
router.get('/frequent', 
  authorize(['admin', 'staff']),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  CustomersController.getFrequentCustomers
);

/**
 * @route   GET /api/customers/search
 * @desc    Search customers
 * @access  Private (Admin, Staff)
 */
router.get('/search', 
  authorize(['admin', 'staff']),
  searchValidation,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
  CustomersController.searchCustomers
);

/**
 * @route   GET /api/customers/membership/:level
 * @desc    Get customers by membership level
 * @access  Private (Admin, Staff)
 */
router.get('/membership/:level', 
  authorize(['admin', 'staff']),
  param('level')
    .isIn(['bronze', 'silver', 'gold', 'platinum', 'diamond'])
    .withMessage('Invalid membership level'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  CustomersController.getCustomersByMembership
);

/**
 * @route   GET /api/customers/:id
 * @desc    Get customer by ID
 * @access  Private (Admin, Staff, Own Customer)
 */
router.get('/:id', 
  authorize(['admin', 'staff', 'customer']),
  validateIdParam(),
  validateRequest,
  CustomersController.getCustomerById
);

/**
 * @route   POST /api/customers
 * @desc    Create new customer
 * @access  Private (Admin, Staff)
 */
router.post('/', 
  authorize(['admin', 'staff']),
  adminLimiter,
  createCustomerValidation,
  validateRequest,
  CustomersController.createCustomer
);

/**
 * @route   PUT /api/customers/:id
 * @desc    Update customer
 * @access  Private (Admin, Staff)
 */
router.put('/:id', 
  authorize(['admin', 'staff']),
  adminLimiter,
  validateIdParam(),
  updateCustomerValidation,
  validateRequest,
  CustomersController.updateCustomer
);

/**
 * @route   DELETE /api/customers/:id
 * @desc    Delete customer (soft delete)
 * @access  Private (Admin)
 */
router.delete('/:id', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  CustomersController.deleteCustomer
);

/**
 * @route   PATCH /api/customers/:id/status
 * @desc    Update customer status
 * @access  Private (Admin)
 */
router.patch('/:id/status', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  updateStatusValidation,
  validateRequest,
  CustomersController.updateCustomerStatus
);

/**
 * @route   PATCH /api/customers/:id/vip
 * @desc    Toggle VIP status
 * @access  Private (Admin)
 */
router.patch('/:id/vip', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  toggleVipValidation,
  validateRequest,
  CustomersController.toggleVipStatus
);

/**
 * @route   POST /api/customers/:id/loyalty-points/add
 * @desc    Add loyalty points
 * @access  Private (Admin, Staff)
 */
router.post('/:id/loyalty-points/add', 
  authorize(['admin', 'staff']),
  adminLimiter,
  validateIdParam(),
  loyaltyPointsValidation,
  validateRequest,
  CustomersController.addLoyaltyPoints
);

/**
 * @route   POST /api/customers/:id/loyalty-points/redeem
 * @desc    Redeem loyalty points
 * @access  Private (Admin, Staff)
 */
router.post('/:id/loyalty-points/redeem', 
  authorize(['admin', 'staff']),
  adminLimiter,
  validateIdParam(),
  loyaltyPointsValidation,
  validateRequest,
  CustomersController.redeemLoyaltyPoints
);

module.exports = router;
