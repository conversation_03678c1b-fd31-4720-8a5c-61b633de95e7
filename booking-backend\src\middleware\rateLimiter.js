/**
 * Rate Limiting Middleware
 * Protects API endpoints from abuse
 */

const rateLimit = require('express-rate-limit');
const config = require('../config/environment');
const logger = require('../utils/logger');
const { HTTP_STATUS, ERROR_CODES } = require('../utils/constants');

/**
 * Create rate limiter with custom options
 * @param {Object} options - Rate limiting options
 * @returns {Function} Rate limiting middleware
 */
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: config.security.rateLimitWindow,
    max: config.security.rateLimitMax,
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: ERROR_CODES.TOO_MANY_REQUESTS || 'TOO_MANY_REQUESTS',
        message: 'Too many requests, please try again later'
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: config.app.version
      }
    },
    handler: (req, res) => {
      // Validate res parameter
      if (!res || typeof res.status !== 'function') {
        console.error('res is not defined in rate limiter handler');
        return;
      }

      logger.security('rate_limit_exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl,
        method: req.method,
        userId: req.user ? req.user.id : null
      });

      res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(options.message || defaultOptions.message);
    },
    skip: (req, res) => {
      try {
        // Skip rate limiting for certain conditions
        if (options.skipSuccessfulRequests && res && res.statusCode < 400) {
          return true;
        }

        // Skip for whitelisted IPs (if configured)
        if (options.whitelist && options.whitelist.includes(req.ip)) {
          return true;
        }

        return false;
      } catch (error) {
        console.error('Error in rate limiter skip function:', error);
        return false;
      }
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
};

/**
 * General API rate limiter
 * Applied to all API routes
 */
const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per window per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_REQUESTS',
      message: 'Too many API requests, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * Authentication rate limiter
 * Applied to login, register, password reset endpoints
 */
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_AUTH_ATTEMPTS',
      message: 'Too many authentication attempts, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  },
  skipSuccessfulRequests: true // Don't count successful requests
});

/**
 * Registration rate limiter
 * More restrictive for user registration
 * Adjusted for development environment
 */
const registerLimiter = createRateLimiter({
  windowMs: config.app.env === 'development' ? 5 * 60 * 1000 : 60 * 60 * 1000, // 5 minutes in dev, 1 hour in prod
  max: config.app.env === 'development' ? 50 : 3, // 50 registrations in dev, 3 in prod
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_REGISTRATIONS',
      message: config.app.env === 'development'
        ? 'Too many registration attempts. Please wait 5 minutes before trying again (development mode).'
        : 'Too many registration attempts, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  },
  // Skip rate limiting entirely in development if needed
  skip: (req) => {
    // Uncomment the line below to disable rate limiting in development
    // return config.app.env === 'development';
    return false;
  }
});

/**
 * Password reset rate limiter
 */
const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset attempts per hour per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_PASSWORD_RESETS',
      message: 'Too many password reset attempts, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * Booking creation rate limiter
 * Prevent spam booking creation
 */
const bookingLimiter = createRateLimiter({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 10, // 10 booking attempts per window per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_BOOKING_ATTEMPTS',
      message: 'Too many booking attempts, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * File upload rate limiter
 */
const uploadLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // 20 uploads per window per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_UPLOADS',
      message: 'Too many file uploads, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * Search rate limiter
 * Prevent search abuse
 */
const searchLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // 30 searches per minute per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_SEARCHES',
      message: 'Too many search requests, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * Admin action rate limiter
 * For sensitive admin operations
 */
const adminLimiter = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // 50 admin actions per window per IP
  message: {
    success: false,
    error: {
      code: 'TOO_MANY_ADMIN_ACTIONS',
      message: 'Too many admin actions, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  }
});

/**
 * Dynamic rate limiter based on user role
 * Different limits for different user types
 */
const dynamicLimiter = (options = {}) => {
  return (req, res, next) => {
    const user = req.user;
    let limiterOptions = { ...options };
    
    if (user) {
      switch (user.role) {
        case 'admin':
          limiterOptions.max = (options.max || 100) * 5; // 5x limit for admins
          break;
        case 'staff':
          limiterOptions.max = (options.max || 100) * 3; // 3x limit for staff
          break;
        case 'customer':
          limiterOptions.max = options.max || 100; // Normal limit for customers
          break;
        default:
          limiterOptions.max = (options.max || 100) * 0.5; // Reduced limit for unknown roles
      }
    } else {
      limiterOptions.max = (options.max || 100) * 0.3; // Very limited for unauthenticated users
    }
    
    const limiter = createRateLimiter(limiterOptions);
    return limiter(req, res, next);
  };
};

/**
 * IP-based rate limiter with memory store
 * For development/testing environments
 */
const memoryLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Sliding window rate limiter
 * More sophisticated rate limiting
 */
const slidingWindowLimiter = (options = {}) => {
  const store = new Map();
  const windowMs = options.windowMs || 15 * 60 * 1000;
  const max = options.max || 100;
  
  return (req, res, next) => {
    const key = req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get or create request log for this IP
    if (!store.has(key)) {
      store.set(key, []);
    }
    
    const requests = store.get(key);
    
    // Remove old requests outside the window
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (validRequests.length >= max) {
      logger.security('sliding_window_rate_limit_exceeded', {
        ip: req.ip,
        requestCount: validRequests.length,
        limit: max,
        windowMs,
        url: req.originalUrl
      });
      
      return res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json({
        success: false,
        error: {
          code: 'TOO_MANY_REQUESTS',
          message: 'Rate limit exceeded'
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: config.app.version
        }
      });
    }
    
    // Add current request
    validRequests.push(now);
    store.set(key, validRequests);
    
    // Clean up old entries periodically
    if (Math.random() < 0.01) { // 1% chance
      for (const [ip, timestamps] of store.entries()) {
        const validTimestamps = timestamps.filter(t => t > windowStart);
        if (validTimestamps.length === 0) {
          store.delete(ip);
        } else {
          store.set(ip, validTimestamps);
        }
      }
    }
    
    next();
  };
};

module.exports = {
  createRateLimiter,
  generalLimiter,
  authLimiter,
  registerLimiter,
  passwordResetLimiter,
  bookingLimiter,
  uploadLimiter,
  searchLimiter,
  adminLimiter,
  dynamicLimiter,
  memoryLimiter,
  slidingWindowLimiter
};
