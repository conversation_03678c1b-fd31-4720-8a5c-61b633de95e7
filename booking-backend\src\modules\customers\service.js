/**
 * Customers Service
 * Business logic for customer management
 */

const Customer = require('./model');
const User = require('../auth/model');
const Branch = require('../branches/model');
const { AppError } = require('../../middleware/errorHandler');
const { paginate } = require('../../utils/pagination');
const logger = require('../../utils/logger');
const { USER_ROLES, MEMBERSHIP_LEVELS } = require('../../utils/constants');

class CustomersService {
  /**
   * Get all customers with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {Object} pagination - Pagination options
   * @returns {Promise<Object>} Paginated customers
   */
  async getCustomers(filters = {}, pagination = {}) {
    try {
      const { membershipLevel, status, isVip, preferredBranchId, search, minSpent, maxSpent } = filters;
      const { Op } = require('sequelize');
      const whereClause = {};

      // Apply filters
      if (membershipLevel && Object.values(MEMBERSHIP_LEVELS).includes(membershipLevel)) {
        whereClause.membershipLevel = membershipLevel;
      }

      if (status) {
        whereClause.status = status;
      }

      if (isVip !== undefined) {
        whereClause.isVip = isVip === 'true';
      }

      if (preferredBranchId) {
        whereClause.preferredBranchId = parseInt(preferredBranchId);
      }

      if (minSpent || maxSpent) {
        whereClause.totalSpent = {};
        if (minSpent) whereClause.totalSpent[Op.gte] = parseFloat(minSpent);
        if (maxSpent) whereClause.totalSpent[Op.lte] = parseFloat(maxSpent);
      }

      // Search functionality
      if (search) {
        whereClause[Op.or] = [
          { customerCode: { [Op.like]: `%${search}%` } },
          { referralCode: { [Op.like]: `%${search}%` } }
        ];
      }

      const options = {
        ...pagination,
        include: [
          {
            model: User,
            as: 'user',
            attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] },
            where: search ? {
              [Op.or]: [
                { name: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } },
                { phone: { [Op.like]: `%${search}%` } }
              ]
            } : undefined,
            required: !!search
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      };

      return await paginate(Customer, whereClause, options);

    } catch (error) {
      logger.error('Get customers failed:', error);
      throw error;
    }
  }

  /**
   * Get customer by ID
   * @param {number} customerId - Customer ID
   * @returns {Promise<Object>} Customer data
   */
  async getCustomerById(customerId) {
    try {
      const customer = await Customer.findByPk(customerId, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city', 'address', 'phone']
          },
          {
            model: Customer,
            as: 'referrer',
            attributes: ['id', 'customerCode'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email']
            }],
            required: false
          }
        ]
      });

      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      return customer;

    } catch (error) {
      logger.error('Get customer by ID failed:', error);
      throw error;
    }
  }

  /**
   * Create new customer
   * @param {Object} customerData - Customer data
   * @returns {Promise<Object>} Created customer
   */
  async createCustomer(customerData) {
    try {
      const { userId, customerCode, referralCode, referredBy } = customerData;

      // Check if user exists and is customer
      const user = await User.findByPk(userId);
      if (!user || !user.isActive) {
        throw new AppError('User not found or inactive', 404, 'USER_NOT_FOUND');
      }

      if (user.role !== 'customer') {
        throw new AppError('User must be a customer', 400, 'INVALID_USER_ROLE');
      }

      // Check if user is already a customer
      const existingCustomer = await Customer.findByUserId(userId);
      if (existingCustomer) {
        throw new AppError('User is already a customer', 409, 'USER_ALREADY_CUSTOMER');
      }

      // Check if customer code already exists
      const existingCode = await Customer.findByCustomerCode(customerCode);
      if (existingCode) {
        throw new AppError('Customer code already exists', 409, 'CUSTOMER_CODE_EXISTS');
      }

      // Check if referral code already exists (if provided)
      if (referralCode) {
        const existingReferralCode = await Customer.findByReferralCode(referralCode);
        if (existingReferralCode) {
          throw new AppError('Referral code already exists', 409, 'REFERRAL_CODE_EXISTS');
        }
      }

      // Validate referrer if provided
      if (referredBy) {
        const referrer = await Customer.findByPk(referredBy);
        if (!referrer || referrer.status !== 'active') {
          throw new AppError('Referrer not found or inactive', 404, 'REFERRER_NOT_FOUND');
        }
      }

      // Validate preferred branch if provided
      if (customerData.preferredBranchId) {
        const branch = await Branch.findByPk(customerData.preferredBranchId);
        if (!branch || !branch.isActive) {
          throw new AppError('Preferred branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
        }
      }

      // Generate referral code if not provided
      if (!referralCode) {
        customerData.referralCode = this.generateReferralCode(customerCode);
      }

      // Create customer
      const customer = await Customer.create(customerData);

      // Update referrer's referral count if applicable
      if (referredBy) {
        const referrer = await Customer.findByPk(referredBy);
        await referrer.update({
          referralCount: referrer.referralCount + 1
        });

        // Add bonus loyalty points to referrer
        await referrer.addLoyaltyPoints(100); // 100 points for successful referral
      }

      logger.business('customer_created', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        userId: customer.userId,
        referredBy: customer.referredBy
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Create customer failed:', error);
      throw error;
    }
  }

  /**
   * Update customer
   * @param {number} customerId - Customer ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated customer
   */
  async updateCustomer(customerId, updateData) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Check customer code uniqueness if updating
      if (updateData.customerCode && updateData.customerCode !== customer.customerCode) {
        const existingCode = await Customer.findByCustomerCode(updateData.customerCode);
        if (existingCode) {
          throw new AppError('Customer code already exists', 409, 'CUSTOMER_CODE_EXISTS');
        }
      }

      // Check referral code uniqueness if updating
      if (updateData.referralCode && updateData.referralCode !== customer.referralCode) {
        const existingReferralCode = await Customer.findByReferralCode(updateData.referralCode);
        if (existingReferralCode) {
          throw new AppError('Referral code already exists', 409, 'REFERRAL_CODE_EXISTS');
        }
      }

      // Validate preferred branch if updating
      if (updateData.preferredBranchId && updateData.preferredBranchId !== customer.preferredBranchId) {
        const branch = await Branch.findByPk(updateData.preferredBranchId);
        if (!branch || !branch.isActive) {
          throw new AppError('Preferred branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
        }
      }

      // Validate membership level if updating
      if (updateData.membershipLevel && !Object.values(MEMBERSHIP_LEVELS).includes(updateData.membershipLevel)) {
        throw new AppError('Invalid membership level', 400, 'INVALID_MEMBERSHIP_LEVEL');
      }

      await customer.update(updateData);

      logger.business('customer_updated', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        updatedFields: Object.keys(updateData)
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Update customer failed:', error);
      throw error;
    }
  }

  /**
   * Delete customer (soft delete)
   * @param {number} customerId - Customer ID
   * @returns {Promise<Object>} Success message
   */
  async deleteCustomer(customerId) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Check if customer has active bookings
      // TODO: Implement booking check when Booking model is available
      // const activeBookings = await Booking.count({
      //   where: {
      //     customerId,
      //     status: { [Op.in]: ['pending', 'confirmed'] }
      //   }
      // });

      // if (activeBookings > 0) {
      //   throw new AppError('Cannot delete customer with active bookings', 400, 'CUSTOMER_HAS_ACTIVE_BOOKINGS');
      // }

      // Soft delete
      await customer.destroy();

      logger.business('customer_deleted', {
        customerId: customer.id,
        customerCode: customer.customerCode
      });

      return { message: 'Customer deleted successfully' };

    } catch (error) {
      logger.error('Delete customer failed:', error);
      throw error;
    }
  }

  /**
   * Update customer status
   * @param {number} customerId - Customer ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated customer
   */
  async updateCustomerStatus(customerId, status) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      const validStatuses = ['active', 'inactive', 'suspended', 'blacklisted'];
      if (!validStatuses.includes(status)) {
        throw new AppError('Invalid customer status', 400, 'INVALID_STATUS');
      }

      await customer.update({ status });

      logger.business('customer_status_changed', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        oldStatus: customer.status,
        newStatus: status
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Update customer status failed:', error);
      throw error;
    }
  }

  /**
   * Generate referral code
   * @param {string} customerCode - Customer code
   * @returns {string} Generated referral code
   */
  generateReferralCode(customerCode) {
    const timestamp = Date.now().toString().slice(-6);
    return `REF${customerCode}${timestamp}`.toUpperCase();
  }

  /**
   * Toggle VIP status
   * @param {number} customerId - Customer ID
   * @param {boolean} isVip - VIP status
   * @returns {Promise<Object>} Updated customer
   */
  async toggleVipStatus(customerId, isVip) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      await customer.update({ isVip });

      logger.business('customer_vip_status_changed', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        isVip
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Toggle VIP status failed:', error);
      throw error;
    }
  }

  /**
   * Get customers by membership level
   * @param {string} membershipLevel - Membership level
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Customers with the membership level
   */
  async getCustomersByMembership(membershipLevel, options = {}) {
    try {
      if (!Object.values(MEMBERSHIP_LEVELS).includes(membershipLevel)) {
        throw new AppError('Invalid membership level', 400, 'INVALID_MEMBERSHIP_LEVEL');
      }

      return await Customer.getCustomersByMembership(membershipLevel, {
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get customers by membership failed:', error);
      throw error;
    }
  }

  /**
   * Get VIP customers
   * @param {Object} options - Query options
   * @returns {Promise<Array>} VIP customers
   */
  async getVipCustomers(options = {}) {
    try {
      return await Customer.getVipCustomers({
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get VIP customers failed:', error);
      throw error;
    }
  }

  /**
   * Get high value customers
   * @param {Object} options - Query options
   * @returns {Promise<Array>} High value customers
   */
  async getHighValueCustomers(options = {}) {
    try {
      return await Customer.getHighValueCustomers({
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get high value customers failed:', error);
      throw error;
    }
  }

  /**
   * Get frequent customers
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Frequent customers
   */
  async getFrequentCustomers(options = {}) {
    try {
      return await Customer.getFrequentCustomers({
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get frequent customers failed:', error);
      throw error;
    }
  }

  /**
   * Get customer statistics
   * @returns {Promise<Object>} Customer statistics
   */
  async getCustomerStats() {
    try {
      const { Op } = require('sequelize');
      const sequelize = require('../../database/connection').sequelize;

      const totalCustomers = await Customer.count({ where: { status: 'active' } });

      const customersByMembership = await Customer.findAll({
        attributes: [
          'membershipLevel',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: { status: 'active' },
        group: ['membershipLevel'],
        raw: true
      });

      const vipCustomers = await Customer.count({
        where: { status: 'active', isVip: true }
      });

      const newCustomersThisMonth = await Customer.count({
        where: {
          status: 'active',
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      });

      const totalRevenue = await Customer.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('total_spent')), 'totalRevenue']
        ],
        where: { status: 'active' },
        raw: true
      });

      const averageSpending = await Customer.findOne({
        attributes: [
          [sequelize.fn('AVG', sequelize.col('total_spent')), 'avgSpending']
        ],
        where: { status: 'active' },
        raw: true
      });

      const totalLoyaltyPoints = await Customer.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('loyalty_points')), 'totalPoints']
        ],
        where: { status: 'active' },
        raw: true
      });

      return {
        total: totalCustomers,
        vip: vipCustomers,
        newThisMonth: newCustomersThisMonth,
        totalRevenue: parseFloat(totalRevenue.totalRevenue) || 0,
        averageSpending: parseFloat(averageSpending.avgSpending) || 0,
        totalLoyaltyPoints: parseInt(totalLoyaltyPoints.totalPoints) || 0,
        byMembership: customersByMembership.reduce((acc, customer) => {
          acc[customer.membershipLevel] = parseInt(customer.count);
          return acc;
        }, {})
      };

    } catch (error) {
      logger.error('Get customer stats failed:', error);
      throw error;
    }
  }

  /**
   * Search customers
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Matching customers
   */
  async searchCustomers(searchTerm, options = {}) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new AppError('Search term must be at least 2 characters', 400, 'INVALID_SEARCH_TERM');
      }

      const { Op } = require('sequelize');

      return await Customer.findAll({
        where: {
          status: 'active',
          [Op.or]: [
            { customerCode: { [Op.like]: `%${searchTerm}%` } },
            { referralCode: { [Op.like]: `%${searchTerm}%` } }
          ]
        },
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone'],
            where: {
              [Op.or]: [
                { name: { [Op.like]: `%${searchTerm}%` } },
                { email: { [Op.like]: `%${searchTerm}%` } },
                { phone: { [Op.like]: `%${searchTerm}%` } }
              ]
            },
            required: false
          },
          {
            model: Branch,
            as: 'preferredBranch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Search customers failed:', error);
      throw error;
    }
  }

  /**
   * Add loyalty points to customer
   * @param {number} customerId - Customer ID
   * @param {number} points - Points to add
   * @returns {Promise<Object>} Updated customer
   */
  async addLoyaltyPoints(customerId, points) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      if (points <= 0) {
        throw new AppError('Points must be positive', 400, 'INVALID_POINTS');
      }

      await customer.addLoyaltyPoints(points);

      logger.business('loyalty_points_added', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        pointsAdded: points,
        newTotal: customer.loyaltyPoints + points
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Add loyalty points failed:', error);
      throw error;
    }
  }

  /**
   * Redeem loyalty points
   * @param {number} customerId - Customer ID
   * @param {number} points - Points to redeem
   * @returns {Promise<Object>} Updated customer
   */
  async redeemLoyaltyPoints(customerId, points) {
    try {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      if (points <= 0) {
        throw new AppError('Points must be positive', 400, 'INVALID_POINTS');
      }

      if (customer.loyaltyPoints < points) {
        throw new AppError('Insufficient loyalty points', 400, 'INSUFFICIENT_POINTS');
      }

      await customer.redeemLoyaltyPoints(points);

      logger.business('loyalty_points_redeemed', {
        customerId: customer.id,
        customerCode: customer.customerCode,
        pointsRedeemed: points,
        newTotal: customer.loyaltyPoints - points
      });

      return await this.getCustomerById(customer.id);

    } catch (error) {
      logger.error('Redeem loyalty points failed:', error);
      throw error;
    }
  }
}

module.exports = new CustomersService();
