const axios = require('axios');

async function quickFormTest() {
  try {
    console.log('🧪 Quick Forms Test with Fixed Permissions\n');
    
    // Login
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>', 
      password: 'admin123456'
    });
    
    const token = loginRes.data.data.token;
    console.log('✅ Login successful');
    
    // Create form with correct IDs
    const formData = {
      name: 'Final Test Form - ' + Date.now(),
      serviceId: 9,
      branchId: 5,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };
    
    const createResponse = await axios.post('http://localhost:3000/api/forms', formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
      console.log('Raw response data:', createResponse.data);
    console.log('Response data type:', typeof createResponse.data.data);
    console.log('Response data keys:', Object.keys(createResponse.data.data || {}));
    
    const form = createResponse.data.data;
    
    console.log('✅ Form created successfully!');
    console.log(`📝 Form Name: ${form?.name || 'undefined'}`);
    console.log(`🔗 Form Slug: ${form?.slug || 'undefined'}`);
    console.log(`🌐 Public URL: ${form?.publicUrl || 'undefined'}`);
    console.log(`📋 Embed Code: ${form?.embedCode ? form.embedCode.substring(0, 80) + '...' : 'undefined'}`);
    
    // Verify both are present
    if (form.publicUrl && form.embedCode) {
      console.log('\n🎉 SUCCESS! Both publicUrl and embedCode are working!');
      console.log('✅ The forms functionality is now FIXED!');
    } else {
      console.log('\n❌ Still missing some fields');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

quickFormTest();
