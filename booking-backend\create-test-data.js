/**
 * Create test data for public booking testing
 */

const { sequelize } = require('./src/database/connection');

async function createTestData() {
  console.log('🧪 Creating test data for public booking...');
  
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected');    // Create test branch if not exists
    await sequelize.query(`
      INSERT INTO branches (name, address, phone, city, open_time, close_time, working_days, is_active, created_at, updated_at) 
      VALUES ('Test Branch', '123 Test Street', '0123456789', 'Test City', '08:00:00', '18:00:00', '["Monday","Tuesday","Wednesday","Thursday","Friday"]', 1, NOW(), NOW())
      ON DUPLICATE KEY UPDATE updated_at = NOW()
    `);
    console.log('✅ Test branch created');

    // Create test service if not exists
    await sequelize.query(`
      INSERT INTO services (name, description, category, duration, price, is_active, created_at, updated_at) 
      VALUES ('Test Service', 'Test service for booking', 'General', 60, 100000.00, 1, NOW(), NOW())
      ON DUPLICATE KEY UPDATE updated_at = NOW()
    `);
    console.log('✅ Test service created');

    // Get branch and service IDs
    const [branches] = await sequelize.query(`SELECT id FROM branches WHERE name = 'Test Branch' LIMIT 1`);
    const [services] = await sequelize.query(`SELECT id FROM services WHERE name = 'Test Service' LIMIT 1`);

    const branchId = branches[0]?.id;
    const serviceId = services[0]?.id;

    if (!branchId || !serviceId) {
      console.error('❌ Failed to get branch or service ID');
      return;
    }    // Create test form if not exists
    const testSlug = 'test-public-form';
    await sequelize.query(`
      INSERT INTO forms (name, slug, service_id, branch_id, user_id, status, is_active, created_at, updated_at)
      VALUES (
        'Test Public Form', 
        '${testSlug}', 
        ${serviceId}, 
        ${branchId}, 
        1, 
        'active', 
        1, 
        NOW(), 
        NOW()
      )
      ON DUPLICATE KEY UPDATE updated_at = NOW()
    `);
    console.log('✅ Test form created');

    // Get form details
    const [forms] = await sequelize.query(`
      SELECT f.*, s.name as service_name, s.price, s.duration, b.name as branch_name 
      FROM forms f 
      JOIN services s ON f.service_id = s.id 
      JOIN branches b ON f.branch_id = b.id 
      WHERE f.slug = '${testSlug}' 
      LIMIT 1
    `);

    const testForm = forms[0];
    console.log('📋 Test form details:', {
      slug: testForm.slug,
      service: testForm.service_name,
      branch: testForm.branch_name,
      price: testForm.price,
      duration: testForm.duration
    });

    return testForm;

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

// Run test
createTestData();
