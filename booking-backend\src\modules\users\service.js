/**
 * Users Service
 * Business logic for user management
 */

const User = require('./model');
const { AppError } = require('../../middleware/errorHandler');
const { paginate } = require('../../utils/pagination');
const logger = require('../../utils/logger');
const { USER_ROLES } = require('../../utils/constants');

class UsersService {
  /**
   * Get all users with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {Object} pagination - Pagination options
   * @returns {Promise<Object>} Paginated users
   */
  async getUsers(filters = {}, pagination = {}) {
    try {
      const { role, isActive, search, branchId } = filters;
      const whereClause = {};

      // Apply filters
      if (role && Object.values(USER_ROLES).includes(role)) {
        whereClause.role = role;
      }

      if (isActive !== undefined) {
        whereClause.isActive = isActive === 'true';
      }

      if (branchId) {
        whereClause.branchId = parseInt(branchId);
      }

      // Search functionality
      if (search) {
        const { Op } = require('sequelize');
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { phone: { [Op.like]: `%${search}%` } }
        ];
      }

      const options = {
        ...pagination,
        include: [
          {
            association: 'branch',
            attributes: ['id', 'name', 'address']
          }
        ],
        attributes: { 
          exclude: ['password', 'emailVerificationToken', 'passwordResetToken', 'loginAttempts', 'lockUntil'] 
        }
      };

      return await paginate(User, whereClause, options);

    } catch (error) {
      logger.error('Get users failed:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User data
   */
  async getUserById(userId) {
    try {
      const user = await User.scope('withoutSensitive').findByPk(userId, {
        include: [
          {
            association: 'branch',
            attributes: ['id', 'name', 'address', 'phone']
          }
        ]
      });

      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      return user;

    } catch (error) {
      logger.error('Get user by ID failed:', error);
      throw error;
    }
  }

  /**
   * Create new user (admin only)
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      const { email, phone } = userData;

      // Check if email already exists
      const existingEmail = await User.findByEmail(email);
      if (existingEmail) {
        throw new AppError('Email already exists', 409, 'EMAIL_EXISTS');
      }

      // Check if phone already exists
      if (phone) {
        const existingPhone = await User.findOne({ where: { phone } });
        if (existingPhone) {
          throw new AppError('Phone number already exists', 409, 'PHONE_EXISTS');
        }
      }

      // Create user
      const user = await User.create({
        ...userData,
        email: email.toLowerCase(),
        isEmailVerified: true // Admin-created users are auto-verified
      });

      logger.business('user_created_by_admin', {
        userId: user.id,
        email: user.email,
        role: user.role
      });

      return user.toJSON();

    } catch (error) {
      logger.error('Create user failed:', error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {number} userId - User ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, updateData) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      // Fields that can be updated by admin
      const allowedFields = [
        'name', 'phone', 'role', 'isActive', 'branchId', 
        'dateOfBirth', 'gender', 'avatar'
      ];

      const filteredData = {};
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      });

      // Check email uniqueness if updating email
      if (updateData.email && updateData.email !== user.email) {
        const existingEmail = await User.findByEmail(updateData.email);
        if (existingEmail) {
          throw new AppError('Email already exists', 409, 'EMAIL_EXISTS');
        }
        filteredData.email = updateData.email.toLowerCase();
      }

      // Check phone uniqueness if updating phone
      if (updateData.phone && updateData.phone !== user.phone) {
        const existingPhone = await User.findOne({ 
          where: { 
            phone: updateData.phone,
            id: { [require('sequelize').Op.ne]: userId }
          } 
        });
        if (existingPhone) {
          throw new AppError('Phone number already exists', 409, 'PHONE_EXISTS');
        }
      }

      await user.update(filteredData);

      logger.business('user_updated', {
        userId: user.id,
        email: user.email,
        updatedFields: Object.keys(filteredData)
      });

      return user.toJSON();

    } catch (error) {
      logger.error('Update user failed:', error);
      throw error;
    }
  }

  /**
   * Delete user (soft delete)
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Success message
   */
  async deleteUser(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      // Soft delete by setting isActive to false
      await user.update({ isActive: false });

      logger.business('user_deleted', {
        userId: user.id,
        email: user.email
      });

      return { message: 'User deleted successfully' };

    } catch (error) {
      logger.error('Delete user failed:', error);
      throw error;
    }
  }

  /**
   * Activate/Deactivate user
   * @param {number} userId - User ID
   * @param {boolean} isActive - Active status
   * @returns {Promise<Object>} Updated user
   */
  async toggleUserStatus(userId, isActive) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      await user.update({ isActive });

      logger.business('user_status_changed', {
        userId: user.id,
        email: user.email,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return user.toJSON();

    } catch (error) {
      logger.error('Toggle user status failed:', error);
      throw error;
    }
  }

  /**
   * Get users by role
   * @param {string} role - User role
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Users with specified role
   */
  async getUsersByRole(role, options = {}) {
    try {
      if (!Object.values(USER_ROLES).includes(role)) {
        throw new AppError('Invalid role', 400, 'INVALID_ROLE');
      }

      return await User.getUsersByRole(role, options);

    } catch (error) {
      logger.error('Get users by role failed:', error);
      throw error;
    }
  }

  /**
   * Search users
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Matching users
   */
  async searchUsers(searchTerm, options = {}) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new AppError('Search term must be at least 2 characters', 400, 'INVALID_SEARCH_TERM');
      }

      return await User.searchUsers(searchTerm.trim(), options);

    } catch (error) {
      logger.error('Search users failed:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   * @returns {Promise<Object>} User statistics
   */
  async getUserStats() {
    try {
      return await User.getUserStats();

    } catch (error) {
      logger.error('Get user stats failed:', error);
      throw error;
    }
  }

  /**
   * Reset user password (admin only)
   * @param {number} userId - User ID
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Success message
   */
  async resetUserPassword(userId, newPassword) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      await user.update({
        password: newPassword,
        loginAttempts: 0,
        lockUntil: null
      });

      logger.business('user_password_reset_by_admin', {
        userId: user.id,
        email: user.email
      });

      return { message: 'Password reset successfully' };

    } catch (error) {
      logger.error('Reset user password failed:', error);
      throw error;
    }
  }

  /**
   * Unlock user account
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Success message
   */
  async unlockUser(userId) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }

      await user.update({
        loginAttempts: 0,
        lockUntil: null
      });

      logger.business('user_unlocked', {
        userId: user.id,
        email: user.email
      });

      return { message: 'User account unlocked successfully' };

    } catch (error) {
      logger.error('Unlock user failed:', error);
      throw error;
    }
  }
}

module.exports = new UsersService();
