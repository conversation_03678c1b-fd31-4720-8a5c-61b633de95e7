/**
 * Protected Route Component
 * Handles route protection and authentication checks
 */

'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Calendar } from 'lucide-react';

/**
 * ProtectedRoute Component
 * Protects routes that require authentication
 */
export default function ProtectedRoute({ 
  children, 
  requiredRoles = [], 
  fallbackPath = '/login',
  showLoading = true 
}) {
  const { user, isLoading, isAuthenticated, isInitialized, hasAnyRole } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      console.log('🔍 ProtectedRoute: Checking auth...', {
        pathname,
        isLoading,
        isInitialized,
        isAuthenticated,
        hasUser: !!user,
        userEmail: user?.email
      });

      // Wait for auth initialization to complete
      if (isLoading || !isInitialized) {
        console.log('🔍 ProtectedRoute: Still loading or not initialized, waiting...', {
          isLoading,
          isInitialized
        });
        return;
      }

      // Check if user is authenticated
      if (!isAuthenticated || !user) {
        console.log('❌ ProtectedRoute: User not authenticated, redirecting to login');
        console.log('  - isAuthenticated:', isAuthenticated);
        console.log('  - user:', !!user);
        // Use replace instead of push to avoid back button issues
        router.replace(`${fallbackPath}?redirect=${encodeURIComponent(pathname)}`);
        return;
      }

      // Check role requirements if specified
      if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
        console.log('❌ ProtectedRoute: User does not have required roles:', requiredRoles);
        // Redirect to dashboard or unauthorized page
        router.replace('/dashboard');
        return;
      }

      console.log('✅ ProtectedRoute: Auth check passed, allowing access');
      setIsChecking(false);
    };

    checkAuth();
  }, [isLoading, isInitialized, isAuthenticated, user, requiredRoles, router, pathname, fallbackPath, hasAnyRole]);

  // Show loading spinner while checking authentication
  if (isLoading || !isInitialized || isChecking) {
    console.log('🔄 ProtectedRoute: Showing loading state...', { isLoading, isInitialized, isChecking });

    if (!showLoading) {
      return null;
    }

    return (
      <div className="min-h-screen bg-background flex flex-col justify-center items-center">
        <div className="text-center">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center">
                <Calendar className="h-7 w-7 text-white" />
              </div>
              <span className="ml-3 text-2xl font-bold text-text">FormBooker</span>
            </div>
          </div>

          {/* Loading Spinner */}
          <div className="flex justify-center mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>

          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If we reach here, user is authenticated and has required permissions
  return children;
}

/**
 * Higher-order component for protecting pages
 * Usage: export default withAuth(YourComponent, { requiredRoles: ['admin'] })
 */
export function withAuth(WrappedComponent, options = {}) {
  const {
    requiredRoles = [],
    fallbackPath = '/login',
    showLoading = true,
  } = options;

  return function AuthenticatedComponent(props) {
    return (
      <ProtectedRoute
        requiredRoles={requiredRoles}
        fallbackPath={fallbackPath}
        showLoading={showLoading}
      >
        <WrappedComponent {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Role-based access control component
 * Shows content only if user has required roles
 */
export function RoleGuard({ 
  children, 
  requiredRoles = [], 
  fallback = null,
  requireAll = false 
}) {
  const { user, hasAnyRole, hasRole } = useAuth();

  if (!user) {
    return fallback;
  }

  // Check if user has required roles
  let hasAccess = false;

  if (requiredRoles.length === 0) {
    // No role requirements, allow access
    hasAccess = true;
  } else if (requireAll) {
    // User must have ALL specified roles
    hasAccess = requiredRoles.every(role => hasRole(role));
  } else {
    // User must have ANY of the specified roles
    hasAccess = hasAnyRole(requiredRoles);
  }

  return hasAccess ? children : fallback;
}

/**
 * Component to show content only for authenticated users
 */
export function AuthGuard({ children, fallback = null }) {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? children : fallback;
}

/**
 * Component to show content only for unauthenticated users
 */
export function GuestGuard({ children, fallback = null }) {
  const { isAuthenticated } = useAuth();
  return !isAuthenticated ? children : fallback;
}
