const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');

console.log('🚀 Starting simplified booking test server...');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockForm = {
  id: 1,
  slug: 'test-public-form',
  title: 'Test Public Form',
  description: 'Test form for public booking',
  isPublic: true,
  isActive: true,
  serviceName: 'Test Service',
  branchName: 'Test Branch'
};

const mockBookings = [];

// Public form endpoint
app.get('/api/public/forms/:slug', (req, res) => {
  const { slug } = req.params;
  console.log('📋 Public form request for slug:', slug);
  
  if (slug === 'test-public-form') {
    res.json({
      success: true,
      data: mockForm
    });
  } else {
    res.status(404).json({
      success: false,
      error: {
        code: 'FORM_NOT_FOUND',
        message: 'Form not found'
      }
    });
  }
});

// Public booking endpoint  
app.post('/api/public/bookings', async (req, res) => {
  try {
    console.log('📝 Public booking request:', req.body);
    
    const { formSlug, customerName, phoneNumber, emailAddress, preferredDate, preferredTime, specialRequests } = req.body;
    
    // Basic validation
    if (!formSlug || !customerName || !phoneNumber || !emailAddress) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Missing required fields'
        }
      });
    }
    
    // Check if form exists
    if (formSlug !== 'test-public-form') {
      return res.status(404).json({
        success: false,
        error: {
          code: 'FORM_NOT_FOUND',
          message: 'Form not found'
        }
      });
    }
    
    // Create mock user with hashed password
    const randomPassword = Math.random().toString(36).substring(2, 15);
    const hashedPassword = await bcrypt.hash(randomPassword, 10);
    
    console.log('✅ Password hashed successfully');
    
    // Create mock booking
    const booking = {
      id: mockBookings.length + 1,
      formSlug,
      customerName,
      phoneNumber,
      emailAddress,
      preferredDate,
      preferredTime,
      specialRequests,
      status: 'pending',
      createdAt: new Date().toISOString(),
      mockUser: {
        email: emailAddress,
        hashedPassword: hashedPassword.substring(0, 20) + '...' // Don't log full hash
      }
    };
    
    mockBookings.push(booking);
    
    console.log('✅ Mock booking created:', {
      id: booking.id,
      customerName: booking.customerName,
      status: booking.status
    });
    
    res.json({
      success: true,
      data: {
        id: booking.id,
        status: booking.status,
        message: 'Booking created successfully'
      }
    });
    
  } catch (error) {
    console.error('❌ Booking error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create booking'
      }
    });
  }
});

// List bookings endpoint (for testing)
app.get('/api/test/bookings', (req, res) => {
  res.json({
    success: true,
    data: mockBookings,
    count: mockBookings.length
  });
});

const PORT = 3003;

app.listen(PORT, () => {
  console.log(`✅ Simplified booking server running on port ${PORT}`);
  console.log(`📋 Test form URL: http://localhost:${PORT}/api/public/forms/test-public-form`);
  console.log(`📝 Booking endpoint: http://localhost:${PORT}/api/public/bookings`);
  console.log(`📊 Test bookings: http://localhost:${PORT}/api/test/bookings`);
});
