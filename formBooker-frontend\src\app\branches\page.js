'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import branchesService from '@/services/branchesService';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Plus, 
  Edit, 
  Settings, 
  MapPin, 
  Phone,
  Clock,
  X,
  Check,
  Loader2,
  AlertCircle,
  Trash2,
  Mail,
  Globe,
  CheckCircle,
  XCircle
} from 'lucide-react';

export default function BranchesPage() {
  // Auth context
  const { user } = useAuth();

  // State management
  const [branches, setBranches] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalClosing, setIsModalClosing] = useState(false);
  const [editingBranch, setEditingBranch] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [notification, setNotification] = useState(null);
  
  // Form data matching backend API structure
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    city: '',
    district: '',
    ward: '',
    latitude: '',
    longitude: '',
    openTime: '09:00',
    closeTime: '18:00',
    workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    description: '',
    images: []
  });

  // Available working days
  const daysOfWeek = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' }
  ];

  // Load branches on component mount
  useEffect(() => {
    loadBranches();
  }, []);

  // Show notification function
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type, isVisible: false });
    
    // Trigger animation after a brief delay
    setTimeout(() => {
      setNotification(prev => prev ? { ...prev, isVisible: true } : null);
    }, 50);
    
    // Auto hide after 4 seconds
    setTimeout(() => {
      setNotification(prev => prev ? { ...prev, isVisible: false } : null);
    }, 3800);
    
    // Remove notification after fade out
    setTimeout(() => {
      setNotification(null);
    }, 4300);
  };

  // Load branches from API
  const loadBranches = async () => {
    try {
      setIsLoading(true);
      const response = await branchesService.getBranches({
        page: 1,
        limit: 20,
        sort: 'name'
      });
      console.log('Branches response:', response);
      setBranches(response.data || []);
      setErrors({});
    } catch (error) {
      console.error('Failed to load branches:', error);
      showNotification(error.message || 'Failed to load branches. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' ? '' : parseFloat(value)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear field-specific errors
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle working days change
  const handleWorkingDaysChange = (day) => {
    setFormData(prev => ({
      ...prev,
      workingDays: prev.workingDays.includes(day)
        ? prev.workingDays.filter(d => d !== day)
        : [...prev.workingDays, day]
    }));
  };

  // Open modal for create/edit
  const openModal = (branch = null) => {
    if (branch) {
      setEditingBranch(branch);
      setFormData({
        name: branch.name || '',
        address: branch.address || '',
        phone: branch.phone || '',
        email: branch.email || '',
        city: branch.city || '',
        district: branch.district || '',
        ward: branch.ward || '',
        latitude: branch.latitude || '',
        longitude: branch.longitude || '',
        openTime: branch.open_time || '09:00',
        closeTime: branch.close_time || '18:00',
        workingDays: branch.working_days || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        description: branch.description || '',
        images: branch.images || []
      });
    } else {
      setEditingBranch(null);
      setFormData({
        name: '',
        address: '',
        phone: '',
        email: '',
        city: '',
        district: '',
        ward: '',
        latitude: '',
        longitude: '',
        openTime: '09:00',
        closeTime: '18:00',
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        description: '',
        images: []
      });
    }
    setErrors({});
    setNotification(null);
    setIsModalOpen(true);
    setIsModalClosing(false);
  };

  // Close modal with smooth animation
  const closeModal = () => {
    setIsModalClosing(true);
    
    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsModalOpen(false);
      setIsModalClosing(false);
      setEditingBranch(null);
      setErrors({});
      setNotification(null);
    }, 300); // Match the animation duration
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Branch name is required';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^(0|\+84)[0-9]{9,10}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid Vietnamese phone number';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Validate coordinates - require both if one is provided
    const hasLatitude = formData.latitude !== '' && formData.latitude !== null;
    const hasLongitude = formData.longitude !== '' && formData.longitude !== null;
    
    if (hasLatitude && !hasLongitude) {
      newErrors.longitude = 'Please enter longitude coordinates to complete the location';
    }
    
    if (hasLongitude && !hasLatitude) {
      newErrors.latitude = 'Please enter latitude coordinates to complete the location';
    }
    
    if (hasLatitude) {
      const lat = parseFloat(formData.latitude);
      if (isNaN(lat) || lat < -90 || lat > 90) {
        newErrors.latitude = 'Latitude must be between -90 and 90';
      }
    }

    if (hasLongitude) {
      const lng = parseFloat(formData.longitude);
      if (isNaN(lng) || lng < -180 || lng > 180) {
        newErrors.longitude = 'Longitude must be between -180 and 180';
      }
    }

    if (formData.workingDays.length === 0) {
      newErrors.workingDays = 'Please select at least one working day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Clean form data before sending to API
  const cleanFormData = (data) => {
    const cleaned = { ...data };
    
    // Convert empty strings to null for optional fields
    const optionalFields = ['district', 'ward', 'email', 'description'];
    optionalFields.forEach(field => {
      if (cleaned[field] === '') {
        cleaned[field] = null;
      }
    });

    // Handle coordinates
    if (cleaned.latitude === '' || cleaned.latitude === null) {
      cleaned.latitude = null;
    } else {
      cleaned.latitude = parseFloat(cleaned.latitude);
    }

    if (cleaned.longitude === '' || cleaned.longitude === null) {
      cleaned.longitude = null;
    } else {
      cleaned.longitude = parseFloat(cleaned.longitude);
    }

    // Ensure arrays are properly formatted
    if (!Array.isArray(cleaned.workingDays)) {
      cleaned.workingDays = [];
    }
    
    if (!Array.isArray(cleaned.images)) {
      cleaned.images = [];
    }

    return cleaned;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      // Clean form data before sending
      const cleanedData = cleanFormData(formData);
      let response;
      
      if (editingBranch) {
        // Update existing branch
        response = await branchesService.updateBranch(editingBranch.id, cleanedData);
        
        // Update local state
        setBranches(prev => prev.map(branch => 
          branch.id === editingBranch.id 
            ? { ...branch, ...response.data }
            : branch
        ));
        
        // Close modal first
        closeModal();
        
        // Then show notification after modal closes
        setTimeout(() => {
          showNotification(`Branch "${formData.name}" updated successfully!`, 'success');
        }, 300);
      } else {
        // Create new branch
        response = await branchesService.createBranch(cleanedData);
        
        // Add to local state
        setBranches(prev => [...prev, response.data]);
        
        // Close modal first
        closeModal();
        
        // Then show notification after modal closes
        setTimeout(() => {
          showNotification(`Branch "${formData.name}" created successfully!`, 'success');
        }, 300);
      }

    } catch (error) {
      console.error('Submit error:', error);

      // Handle backend validation errors
      if (error.response?.data?.error?.details) {
        const newErrors = {};
        error.response.data.error.details.forEach(detail => {
          if (detail.field) {
            newErrors[detail.field] = detail.message;
          }
        });
        setErrors(newErrors);
      } else if (error.errors && error.errors.length > 0) {
        // Handle validation errors from backend (old format)
        const newErrors = {};
        error.errors.forEach(err => {
          if (err.path) {
            newErrors[err.path] = err.msg;
          }
        });
        setErrors(newErrors);
      } else {
        // General error
        setErrors({
          submit: error.message || 'Failed to save branch. Please try again.'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete branch
  const handleDelete = async (branchId) => {
    if (!confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
      return;
    }

    try {
      // Get branch name before deletion
      const branchName = branches.find(b => b.id === branchId)?.name || 'Branch';
      
      await branchesService.deleteBranch(branchId);
      
      // Remove from local state
      setBranches(prev => prev.filter(branch => branch.id !== branchId));
      
      showNotification(`"${branchName}" deleted successfully!`, 'success');
    } catch (error) {
      console.error('Delete error:', error);
      showNotification(error.message || 'Failed to delete branch. Please try again.', 'error');
    }
  };

  // Toggle branch status
  const handleToggleStatus = async (branchId, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await branchesService.toggleBranchStatus(branchId, newStatus);
      
      // Update local state
      setBranches(prev => prev.map(branch => 
        branch.id === branchId 
          ? { ...branch, isActive: newStatus }
          : branch
      ));
      
      // Get branch name for notification
      const branchName = branches.find(b => b.id === branchId)?.name || 'Branch';
      
      showNotification(`"${branchName}" ${newStatus ? 'activated' : 'deactivated'} successfully!`, 'success');
    } catch (error) {
      console.error('Toggle status error:', error);
      showNotification(error.message || 'Failed to update branch status. Please try again.', 'error');
    }
  };

  // Format working days for display
  const formatWorkingDays = (workingDays) => {
    // Handle null, undefined, or empty values
    if (!workingDays) return 'No working days set';
    
    let daysArray = [];
    
    // Handle different data types that might come from the backend
    if (Array.isArray(workingDays)) {
      daysArray = workingDays;
    } else if (typeof workingDays === 'string') {
      try {
        // Try to parse as JSON array
        const parsed = JSON.parse(workingDays);
        if (Array.isArray(parsed)) {
          daysArray = parsed;
        } else {
          // If it's a single string, split by comma
          daysArray = workingDays.split(',').map(day => day.trim());
        }
      } catch (error) {
        // If JSON parsing fails, treat as comma-separated string
        daysArray = workingDays.split(',').map(day => day.trim());
      }
    } else {
      // Fallback for any other data type
      return 'Invalid working days format';
    }
    
    // Filter out empty strings and check if we have valid days
    daysArray = daysArray.filter(day => day && day.trim() !== '');
    
    if (daysArray.length === 0) return 'No working days set';
    
    // Format the day names
    const dayNames = daysArray.map(day => {
      const dayStr = String(day).toLowerCase().trim();
      return dayStr.charAt(0).toUpperCase() + dayStr.slice(1, 3);
    });
    
    return dayNames.join(', ');
  };

  // Get status badge class
  const getStatusBadge = (isActive) => {
    return isActive ? 'badge-success' : 'badge-neutral';
  };

  // Check if user can manage branches (admin and customer)
  const canManageBranches = () => {
    return user?.role === 'admin' || user?.role === 'customer';
  };

  // Check if user can view branch details (all authenticated users)
  const canViewBranchDetails = () => {
    return true; // All authenticated users can view details
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-text">Your Locations</h1>
              <p className="mt-2 text-neutral-600">
                Manage your business locations and operating hours
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              {canManageBranches() && (
                <button
                  onClick={() => openModal()}
                  className="btn-primary inline-flex items-center"
                  disabled={isLoading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Branch
                </button>
              )}
            </div>
          </div>

          {/* Loading State */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
              <span className="ml-2 text-neutral-600">Loading branches...</span>
            </div>
          ) : (
            <>
              {/* Branches List */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {branches.map((branch) => (
                  <div key={branch.id} className="card-hover">
                    <div className="card-body">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-text mb-1">
                            {branch.name}
                          </h3>
                          <span className={`badge ${getStatusBadge(branch.isActive)}`}>
                            {branch.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <div className="flex space-x-1">
                          {canManageBranches() && (
                            <>
                              <button
                                onClick={() => openModal(branch)}
                                className="btn-ghost btn-sm p-2"
                                title="Edit branch"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleToggleStatus(branch.id, branch.isActive)}
                                className="btn-ghost btn-sm p-2"
                                title={branch.isActive ? 'Deactivate branch' : 'Activate branch'}
                              >
                                <Settings className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(branch.id)}
                                className="btn-ghost btn-sm p-2 text-red-600 hover:text-red-700"
                                title="Delete branch"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="space-y-3 text-sm">
                        <div className="flex items-start text-neutral-600">
                          <MapPin className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                          <span>
                            {branch.address}
                            {branch.ward && `, ${branch.ward}`}
                            {branch.district && `, ${branch.district}`}
                            {branch.city && `, ${branch.city}`}
                          </span>
                        </div>
                        
                        <div className="flex items-center text-neutral-600">
                          <Phone className="h-4 w-4 mr-2" />
                          <span>{branch.phone}</span>
                        </div>
                        
                        {branch.email && (
                          <div className="flex items-center text-neutral-600">
                            <Mail className="h-4 w-4 mr-2" />
                            <span>{branch.email}</span>
                          </div>
                        )}
                        
                        <div className="flex items-start text-neutral-600">
                          <Clock className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Business Hours:</span>
                            <div className="mt-1 text-xs">
                              {branch.openTime && branch.closeTime 
                                ? `${branch.openTime} - ${branch.closeTime}`
                                : 'Not set'
                              }
                            </div>
                            <div className="mt-1 text-xs">
                              {formatWorkingDays(branch.workingDays)}
                            </div>
                          </div>
                        </div>

                      </div>

                      {branch.description && (
                        <div className="mt-4 pt-4 border-t border-neutral-200">
                          <p className="text-sm text-neutral-600">{branch.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Add Branch Card */}
                {!isLoading && canManageBranches() && (
                  <div
                    onClick={() => openModal()}
                    className="card-hover cursor-pointer border-2 border-dashed border-neutral-300 hover:border-primary-300"
                  >
                    <div className="card-body text-center">
                      <div className="w-12 h-12 bg-neutral-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <Plus className="h-6 w-6 text-neutral-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-neutral-600 mb-2">
                        Add New Branch
                      </h3>
                      <p className="text-sm text-neutral-500">
                        {user?.role === 'customer' 
                          ? 'Add another location for your business' 
                          : 'Create a new branch location'
                        }
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Empty State */}
              {!isLoading && branches.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-neutral-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <MapPin className="h-8 w-8 text-neutral-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-600 mb-2">
                    {user?.role === 'customer' ? 'No branches yet' : 'No branches found'}
                  </h3>
                  <p className="text-sm text-neutral-500 mb-6">
                    {user?.role === 'customer' 
                      ? 'Get started by adding your first business location' 
                      : 'No branches match your current filters'
                    }
                  </p>
                  {canManageBranches() && (
                    <button
                      onClick={() => openModal()}
                      className="btn-primary inline-flex items-center"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {user?.role === 'customer' ? 'Add Your First Branch' : 'Add Branch'}
                    </button>
                  )}
                </div>
              )}
            </>
          )}

          {/* Toast Notification */}
          {notification && (
            <div className="fixed top-6 right-6 z-50">
              <div className={`min-w-[420px] max-w-lg w-full shadow-xl rounded-xl pointer-events-auto backdrop-blur-sm overflow-hidden transform transition-all duration-700 ease-out hover:scale-105 ${
                notification.isVisible 
                  ? 'translate-x-0 opacity-100 scale-100' 
                  : 'translate-x-full opacity-0 scale-95'
              } ${
                notification.type === 'success' 
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200/60 shadow-green-100/50' 
                  : 'bg-gradient-to-r from-red-50 to-rose-50 border-2 border-red-200/60 shadow-red-100/50'
              }`}>
                {/* Progress bar */}
                <div className={`h-1 w-full ${
                  notification.type === 'success' ? 'bg-green-200' : 'bg-red-200'
                } relative overflow-hidden`}>
                  <div className={`h-full ${
                    notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'
                  } animate-pulse`}></div>
                </div>
                
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className={`p-2 rounded-full ${
                        notification.type === 'success' ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        {notification.type === 'success' ? (
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        ) : (
                          <XCircle className="h-6 w-6 text-red-600" />
                        )}
                      </div>
                    </div>
                    <div className="ml-4 w-0 flex-1">
                      <p className={`text-base font-semibold ${
                        notification.type === 'success' ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {notification.type === 'success' ? '🎉 Success!' : '⚠️ Error!'}
                      </p>
                      <p className={`mt-2 text-sm leading-relaxed ${
                        notification.type === 'success' ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {notification.message}
                      </p>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                      <button
                        className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                          notification.type === 'success' 
                            ? 'text-green-400 hover:text-green-600 hover:bg-green-100/50 focus:ring-green-500' 
                            : 'text-red-400 hover:text-red-600 hover:bg-red-100/50 focus:ring-red-500'
                        } focus:outline-none focus:ring-2 focus:ring-offset-2`}
                        onClick={() => setNotification(null)}
                      >
                        <span className="sr-only">Close</span>
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Branch Modal - Only for admin users */}
          {isModalOpen && canManageBranches() && (
            <div className={`modal-overlay flex items-center justify-center p-4 ${isModalClosing ? 'modal-closing' : ''}`}>
              <div className={`modal-form max-w-4xl w-full mx-4 ${isModalClosing ? 'modal-closing' : ''}`}>
                {/* Modal Header - Fixed */}
                <div className="flex items-center justify-between p-6 border-b border-neutral-200 bg-white rounded-t-xl">
                  <h2 className="text-xl font-semibold text-text">
                    {editingBranch ? 'Edit Branch' : 'Add New Branch'}
                  </h2>
                  <button
                    onClick={closeModal}
                    className="p-2 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
                    disabled={isSubmitting}
                  >
                    <X className="h-5 w-5 text-neutral-500" />
                  </button>
                </div>

                {/* Modal Body - Scrollable */}
                <div className="modal-form-body modal-scroll">
                  <form onSubmit={handleSubmit} className="p-6 space-y-6">

                    {/* General Error */}
                    {errors.submit && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                          <span className="text-red-800">{errors.submit}</span>
                        </div>
                      </div>
                    )}

                    {/* Basic Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Basic Information</h3>

                      <div>
                        <label className="form-label">Branch Name *</label>
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={`form-input ${errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="e.g., Downtown Spa"
                          required
                        />
                        {errors.name && (
                          <p className="form-error">{errors.name}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Address *</label>
                        <input
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          className={`form-input ${errors.address ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="123 Main Street"
                          required
                        />
                        {errors.address && (
                          <p className="form-error">{errors.address}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="form-label">City *</label>
                          <input
                            type="text"
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            className={`form-input ${errors.city ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="Ho Chi Minh City"
                            required
                          />
                          {errors.city && (
                            <p className="form-error">{errors.city}</p>
                          )}
                        </div>
                        <div>
                          <label className="form-label">District</label>
                          <input
                            type="text"
                            name="district"
                            value={formData.district}
                            onChange={handleInputChange}
                            className="form-input"
                            placeholder="District 1"
                          />
                        </div>
                        <div>
                          <label className="form-label">Ward</label>
                          <input
                            type="text"
                            name="ward"
                            value={formData.ward}
                            onChange={handleInputChange}
                            className="form-input"
                            placeholder="Ben Nghe Ward"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Phone Number *</label>
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className={`form-input ${errors.phone ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="0123456789 or +84123456789"
                            required
                          />
                          {errors.phone && (
                            <p className="form-error">{errors.phone}</p>
                          )}
                          <p className="form-help">Vietnamese phone number format</p>
                        </div>
                        <div>
                          <label className="form-label">Email Address</label>
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className={`form-input ${errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="<EMAIL>"
                          />
                          {errors.email && (
                            <p className="form-error">{errors.email}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Location Coordinates */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Location Coordinates (Optional)</h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Latitude</label>
                          <input
                            type="number"
                            name="latitude"
                            value={formData.latitude}
                            onChange={handleInputChange}
                            className={`form-input ${errors.latitude ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="10.762622 (Vietnam: 8.0 to 23.5)"
                            step="any"
                            min="-90"
                            max="90"
                          />
                          {errors.latitude && (
                            <p className="form-error">{errors.latitude}</p>
                          )}
                          <p className="form-help">Latitude: -90 to 90 (Vietnam: 8.0 to 23.5)</p>
                        </div>
                        <div>
                          <label className="form-label">Longitude</label>
                          <input
                            type="number"
                            name="longitude"
                            value={formData.longitude}
                            onChange={handleInputChange}
                            className={`form-input ${errors.longitude ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="106.660172 (Vietnam: 102.0 to 110.0)"
                            step="any"
                            min="-180"
                            max="180"
                          />
                          {errors.longitude && (
                            <p className="form-error">{errors.longitude}</p>
                          )}
                          <p className="form-help">Longitude: -180 to 180 (Vietnam: 102.0 to 110.0)</p>
                        </div>
                      </div>
                    </div>

                    {/* Business Hours */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Business Hours</h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="form-label">Opening Time</label>
                          <input
                            type="time"
                            name="openTime"
                            value={formData.openTime}
                            onChange={handleInputChange}
                            className="form-input"
                          />
                        </div>
                        <div>
                          <label className="form-label">Closing Time</label>
                          <input
                            type="time"
                            name="closeTime"
                            value={formData.closeTime}
                            onChange={handleInputChange}
                            className="form-input"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="form-label">Working Days *</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                          {daysOfWeek.map((day) => (
                            <label key={day.key} className="flex items-center space-x-2 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.workingDays.includes(day.key)}
                                onChange={() => handleWorkingDaysChange(day.key)}
                                className="form-checkbox"
                              />
                              <span className="text-sm text-neutral-700">{day.label}</span>
                            </label>
                          ))}
                        </div>
                        {errors.workingDays && (
                          <p className="form-error">{errors.workingDays}</p>
                        )}
                      </div>
                    </div>

                    {/* Description */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-text">Description (Optional)</h3>

                      <div>
                        <label className="form-label">Branch Description</label>
                        <textarea
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          className="form-input"
                          rows="3"
                          placeholder="Describe this branch location, special features, or any additional information..."
                        />
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-3 pt-4 border-t border-neutral-200 bg-neutral-50 -mx-6 px-6 py-4 rounded-b-xl">
                      <button
                        type="button"
                        onClick={closeModal}
                        className="btn-outline flex-1"
                        disabled={isSubmitting}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="btn-primary flex-1 inline-flex items-center justify-center"
                        disabled={isSubmitting || !formData.name || !formData.address || !formData.city || !formData.phone}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            {editingBranch ? 'Updating...' : 'Creating...'}
                          </>
                        ) : (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            {editingBranch ? 'Save Changes' : 'Create Branch'}
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
