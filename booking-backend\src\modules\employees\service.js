/**
 * Employees Service
 * Business logic for employee management
 */

const Employee = require('./model');
const User = require('../auth/model');
const Branch = require('../branches/model');
const { AppError } = require('../../middleware/errorHandler');
const { paginate } = require('../../utils/pagination');
const logger = require('../../utils/logger');
const { USER_ROLES, EMPLOYEE_SPECIALIZATIONS } = require('../../utils/constants');

class EmployeesService {
  /**
   * Get all employees with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {Object} pagination - Pagination options
   * @returns {Promise<Object>} Paginated employees
   */
  async getEmployees(filters = {}, pagination = {}) {
    try {
      const { branchId, status, position, specialization, isAvailable, search } = filters;
      const { Op } = require('sequelize');
      const whereClause = {};

      // Apply filters
      if (branchId) {
        whereClause.branchId = parseInt(branchId);
      }

      if (status) {
        whereClause.status = status;
      }

      if (position) {
        whereClause.position = { [Op.like]: `%${position}%` };
      }

      if (specialization) {
        whereClause.specializations = {
          [Op.contains]: [specialization]
        };
      }

      if (isAvailable !== undefined) {
        whereClause.isAvailable = isAvailable === 'true';
      }

      // Search functionality
      if (search) {
        whereClause[Op.or] = [
          { employeeCode: { [Op.like]: `%${search}%` } },
          { position: { [Op.like]: `%${search}%` } },
          { department: { [Op.like]: `%${search}%` } }
        ];
      }

      const options = {
        ...pagination,
        include: [
          {
            model: User,
            as: 'user',
            attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city', 'address']
          },
          {
            model: Employee,
            as: 'manager',
            attributes: ['id', 'employeeCode', 'position'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email']
            }],
            required: false
          }
        ]
      };

      return await paginate(Employee, whereClause, options);

    } catch (error) {
      logger.error('Get employees failed:', error);
      throw error;
    }
  }

  /**
   * Get employee by ID
   * @param {number} employeeId - Employee ID
   * @returns {Promise<Object>} Employee data
   */
  async getEmployeeById(employeeId) {
    try {
      const employee = await Employee.findByPk(employeeId, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city', 'address', 'phone']
          },
          {
            model: Employee,
            as: 'manager',
            attributes: ['id', 'employeeCode', 'position'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email']
            }],
            required: false
          },
          {
            model: Employee,
            as: 'subordinates',
            attributes: ['id', 'employeeCode', 'position'],
            include: [{
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email']
            }],
            required: false
          }
        ]
      });

      if (!employee) {
        throw new AppError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND');
      }

      return employee;

    } catch (error) {
      logger.error('Get employee by ID failed:', error);
      throw error;
    }
  }

  /**
   * Create new employee
   * @param {Object} employeeData - Employee data
   * @returns {Promise<Object>} Created employee
   */
  async createEmployee(employeeData) {
    try {
      const { userId, employeeCode, branchId, managerId } = employeeData;

      // Check if user exists and is staff/admin
      const user = await User.findByPk(userId);
      if (!user || !user.isActive) {
        throw new AppError('User not found or inactive', 404, 'USER_NOT_FOUND');
      }

      if (!['admin', 'staff'].includes(user.role)) {
        throw new AppError('User must be admin or staff to become employee', 400, 'INVALID_USER_ROLE');
      }

      // Check if user is already an employee
      const existingEmployee = await Employee.findByUserId(userId);
      if (existingEmployee) {
        throw new AppError('User is already an employee', 409, 'USER_ALREADY_EMPLOYEE');
      }

      // Check if employee code already exists
      const existingCode = await Employee.findByEmployeeCode(employeeCode);
      if (existingCode) {
        throw new AppError('Employee code already exists', 409, 'EMPLOYEE_CODE_EXISTS');
      }

      // Validate branch
      const branch = await Branch.findByPk(branchId);
      if (!branch || !branch.isActive) {
        throw new AppError('Branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
      }

      // Validate manager if provided
      if (managerId) {
        const manager = await Employee.findByPk(managerId);
        if (!manager || manager.status !== 'active') {
          throw new AppError('Manager not found or inactive', 404, 'MANAGER_NOT_FOUND');
        }
      }

      // Validate specializations
      if (employeeData.specializations) {
        const validSpecializations = Object.values(EMPLOYEE_SPECIALIZATIONS);
        const invalidSpecs = employeeData.specializations.filter(spec => !validSpecializations.includes(spec));
        if (invalidSpecs.length > 0) {
          throw new AppError(`Invalid specializations: ${invalidSpecs.join(', ')}`, 400, 'INVALID_SPECIALIZATIONS');
        }
      }

      // Create employee
      const employee = await Employee.create(employeeData);

      logger.business('employee_created', {
        employeeId: employee.id,
        employeeCode: employee.employeeCode,
        userId: employee.userId,
        branchId: employee.branchId,
        position: employee.position
      });

      return await this.getEmployeeById(employee.id);

    } catch (error) {
      logger.error('Create employee failed:', error);
      throw error;
    }
  }

  /**
   * Update employee
   * @param {number} employeeId - Employee ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated employee
   */
  async updateEmployee(employeeId, updateData) {
    try {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new AppError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND');
      }

      // Check employee code uniqueness if updating
      if (updateData.employeeCode && updateData.employeeCode !== employee.employeeCode) {
        const existingCode = await Employee.findByEmployeeCode(updateData.employeeCode);
        if (existingCode) {
          throw new AppError('Employee code already exists', 409, 'EMPLOYEE_CODE_EXISTS');
        }
      }

      // Validate branch if updating
      if (updateData.branchId && updateData.branchId !== employee.branchId) {
        const branch = await Branch.findByPk(updateData.branchId);
        if (!branch || !branch.isActive) {
          throw new AppError('Branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
        }
      }

      // Validate manager if updating
      if (updateData.managerId && updateData.managerId !== employee.managerId) {
        if (updateData.managerId === employeeId) {
          throw new AppError('Employee cannot be their own manager', 400, 'SELF_MANAGER_NOT_ALLOWED');
        }

        const manager = await Employee.findByPk(updateData.managerId);
        if (!manager || manager.status !== 'active') {
          throw new AppError('Manager not found or inactive', 404, 'MANAGER_NOT_FOUND');
        }
      }

      // Validate specializations if updating
      if (updateData.specializations) {
        const validSpecializations = Object.values(EMPLOYEE_SPECIALIZATIONS);
        const invalidSpecs = updateData.specializations.filter(spec => !validSpecializations.includes(spec));
        if (invalidSpecs.length > 0) {
          throw new AppError(`Invalid specializations: ${invalidSpecs.join(', ')}`, 400, 'INVALID_SPECIALIZATIONS');
        }
      }

      await employee.update(updateData);

      logger.business('employee_updated', {
        employeeId: employee.id,
        employeeCode: employee.employeeCode,
        updatedFields: Object.keys(updateData)
      });

      return await this.getEmployeeById(employee.id);

    } catch (error) {
      logger.error('Update employee failed:', error);
      throw error;
    }
  }

  /**
   * Delete employee (soft delete)
   * @param {number} employeeId - Employee ID
   * @returns {Promise<Object>} Success message
   */
  async deleteEmployee(employeeId) {
    try {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new AppError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND');
      }

      // Check if employee has active bookings
      // TODO: Implement booking check when Booking model is available
      // const activeBookings = await Booking.count({
      //   where: {
      //     employeeId,
      //     status: { [Op.in]: ['pending', 'confirmed'] }
      //   }
      // });

      // if (activeBookings > 0) {
      //   throw new AppError('Cannot delete employee with active bookings', 400, 'EMPLOYEE_HAS_ACTIVE_BOOKINGS');
      // }

      // Check if employee is a manager of other employees
      const subordinates = await Employee.count({
        where: { managerId: employeeId, status: 'active' }
      });

      if (subordinates > 0) {
        throw new AppError('Cannot delete employee who manages other employees', 400, 'EMPLOYEE_IS_MANAGER');
      }

      // Soft delete
      await employee.destroy();

      logger.business('employee_deleted', {
        employeeId: employee.id,
        employeeCode: employee.employeeCode
      });

      return { message: 'Employee deleted successfully' };

    } catch (error) {
      logger.error('Delete employee failed:', error);
      throw error;
    }
  }

  /**
   * Update employee status
   * @param {number} employeeId - Employee ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated employee
   */
  async updateEmployeeStatus(employeeId, status) {
    try {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new AppError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND');
      }

      const validStatuses = ['active', 'inactive', 'on_leave', 'terminated'];
      if (!validStatuses.includes(status)) {
        throw new AppError('Invalid employee status', 400, 'INVALID_STATUS');
      }

      await employee.update({ status });

      logger.business('employee_status_changed', {
        employeeId: employee.id,
        employeeCode: employee.employeeCode,
        oldStatus: employee.status,
        newStatus: status
      });

      return await this.getEmployeeById(employee.id);

    } catch (error) {
      logger.error('Update employee status failed:', error);
      throw error;
    }
  }

  /**
   * Toggle employee availability
   * @param {number} employeeId - Employee ID
   * @param {boolean} isAvailable - Availability status
   * @returns {Promise<Object>} Updated employee
   */
  async toggleEmployeeAvailability(employeeId, isAvailable) {
    try {
      const employee = await Employee.findByPk(employeeId);
      if (!employee) {
        throw new AppError('Employee not found', 404, 'EMPLOYEE_NOT_FOUND');
      }

      await employee.update({ isAvailable });

      logger.business('employee_availability_changed', {
        employeeId: employee.id,
        employeeCode: employee.employeeCode,
        isAvailable
      });

      return await this.getEmployeeById(employee.id);

    } catch (error) {
      logger.error('Toggle employee availability failed:', error);
      throw error;
    }
  }

  /**
   * Get employees by branch
   * @param {number} branchId - Branch ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Employees in the branch
   */
  async getEmployeesByBranch(branchId, options = {}) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      return await Employee.getEmployeesByBranch(branchId, {
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          }
        ]
      });

    } catch (error) {
      logger.error('Get employees by branch failed:', error);
      throw error;
    }
  }

  /**
   * Get employees by specialization
   * @param {string} specialization - Specialization
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Employees with the specialization
   */
  async getEmployeesBySpecialization(specialization, options = {}) {
    try {
      const validSpecializations = Object.values(EMPLOYEE_SPECIALIZATIONS);
      if (!validSpecializations.includes(specialization)) {
        throw new AppError('Invalid specialization', 400, 'INVALID_SPECIALIZATION');
      }

      return await Employee.getEmployeesBySpecialization(specialization, {
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city']
          }
        ]
      });

    } catch (error) {
      logger.error('Get employees by specialization failed:', error);
      throw error;
    }
  }

  /**
   * Get available employees
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Available employees
   */
  async getAvailableEmployees(options = {}) {
    try {
      return await Employee.getAvailableEmployees({
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city']
          }
        ]
      });

    } catch (error) {
      logger.error('Get available employees failed:', error);
      throw error;
    }
  }

  /**
   * Get top rated employees
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Top rated employees
   */
  async getTopRatedEmployees(options = {}) {
    try {
      return await Employee.getTopRatedEmployees({
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone']
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city']
          }
        ]
      });

    } catch (error) {
      logger.error('Get top rated employees failed:', error);
      throw error;
    }
  }

  /**
   * Get employee statistics
   * @returns {Promise<Object>} Employee statistics
   */
  async getEmployeeStats() {
    try {
      const { Op } = require('sequelize');
      const sequelize = require('../../database/connection').sequelize;

      const totalEmployees = await Employee.count({ where: { status: 'active' } });

      const employeesByStatus = await Employee.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const employeesByBranch = await Employee.findAll({
        attributes: [
          'branchId',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: { status: 'active' },
        group: ['branchId'],
        include: [{
          model: Branch,
          as: 'branch',
          attributes: ['name', 'city']
        }],
        raw: true
      });

      const averageRating = await Employee.findOne({
        attributes: [
          [sequelize.fn('AVG', sequelize.col('rating')), 'avgRating']
        ],
        where: {
          status: 'active',
          rating: { [Op.ne]: null }
        },
        raw: true
      });

      const availableEmployees = await Employee.count({
        where: { status: 'active', isAvailable: true }
      });

      return {
        total: totalEmployees,
        available: availableEmployees,
        averageRating: parseFloat(averageRating.avgRating) || 0,
        byStatus: employeesByStatus.reduce((acc, emp) => {
          acc[emp.status] = parseInt(emp.count);
          return acc;
        }, {}),
        byBranch: employeesByBranch.reduce((acc, emp) => {
          const branchName = emp['branch.name'] || 'Unknown';
          acc[branchName] = parseInt(emp.count);
          return acc;
        }, {})
      };

    } catch (error) {
      logger.error('Get employee stats failed:', error);
      throw error;
    }
  }

  /**
   * Search employees
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Matching employees
   */
  async searchEmployees(searchTerm, options = {}) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new AppError('Search term must be at least 2 characters', 400, 'INVALID_SEARCH_TERM');
      }

      const { Op } = require('sequelize');

      return await Employee.findAll({
        where: {
          status: 'active',
          [Op.or]: [
            { employeeCode: { [Op.like]: `%${searchTerm}%` } },
            { position: { [Op.like]: `%${searchTerm}%` } },
            { department: { [Op.like]: `%${searchTerm}%` } }
          ]
        },
        ...options,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'phone'],
            where: {
              [Op.or]: [
                { name: { [Op.like]: `%${searchTerm}%` } },
                { email: { [Op.like]: `%${searchTerm}%` } }
              ]
            },
            required: false
          },
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city']
          }
        ]
      });

    } catch (error) {
      logger.error('Search employees failed:', error);
      throw error;
    }
  }
}

module.exports = new EmployeesService();
