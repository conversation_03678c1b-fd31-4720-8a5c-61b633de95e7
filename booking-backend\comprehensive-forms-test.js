/**
 * Comprehensive Forms Test
 * Tests the entire forms creation flow including debugging
 */

const axios = require('axios');
const { sequelize } = require('./src/database/connection');

// Environment check first
require('dotenv').config();

const BASE_URL = 'http://localhost:3000';

async function comprehensiveFormsTest() {
  console.log('🚀 Starting Comprehensive Forms Test...\n');
  
  try {
    // 1. Environment check
    console.log('1. Environment Variables Check:');
    console.log('   NODE_ENV:', process.env.NODE_ENV);
    console.log('   PORT:', process.env.PORT);
    console.log('   FRONTEND_URL:', process.env.FRONTEND_URL);
    console.log('   DB_HOST:', process.env.DB_HOST);
    console.log('   DB_DATABASE:', process.env.DB_DATABASE);
    
    // 2. URL Generation Test
    console.log('\n2. URL Generation Test:');
    const testSlug = 'test-form-123';
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4000';
    
    const expectedPublicUrl = `${frontendUrl}/book/${testSlug}`;
    const expectedEmbedCode = `<iframe src="${frontendUrl}/book/${testSlug}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`;
    
    console.log('   Frontend URL:', frontendUrl);
    console.log('   Expected Public URL:', expectedPublicUrl);
    console.log('   Expected Embed Code Length:', expectedEmbedCode.length);
    
    // 3. Database connection test
    console.log('\n3. Database Connection Test:');
    try {
      await sequelize.authenticate();
      console.log('   ✅ Database connection successful');
    } catch (dbError) {
      console.log('   ❌ Database connection failed:', dbError.message);
      return;
    }
    
    // 4. API Health Check
    console.log('\n4. API Health Check:');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      console.log('   ✅ API Health:', healthResponse.data.status);
      console.log('   Database Health:', healthResponse.data.database.status);
    } catch (healthError) {
      console.log('   ❌ API Health check failed:', healthError.message);
      if (healthError.code === 'ECONNREFUSED') {
        console.log('   Server is not running. Please start the server first:');
        console.log('   cd booking-backend && node start-server.js');
        return;
      }
    }
    
    // 5. Authentication Test
    console.log('\n5. Authentication Test:');
    let token;
    try {      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123456'
      });
      
      if (loginResponse.data.success) {
        token = loginResponse.data.data.token;
        console.log('   ✅ Login successful');
        console.log('   Token preview:', token.substring(0, 30) + '...');
      } else {
        console.log('   ❌ Login failed:', loginResponse.data.message);
        console.log('   Please run: node reset-admin-password.js');
        return;
      }
    } catch (authError) {
      console.log('   ❌ Authentication error:', authError.response?.data?.message || authError.message);
      return;
    }
    
    // 6. Services and Branches Check
    console.log('\n6. Prerequisites Check (Services & Branches):');
    try {
      const servicesResponse = await axios.get(`${BASE_URL}/api/services`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const branchesResponse = await axios.get(`${BASE_URL}/api/branches`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const services = servicesResponse.data.data || [];
      const branches = branchesResponse.data.data || [];
      
      console.log('   Services available:', services.length);
      console.log('   Branches available:', branches.length);
      
      if (services.length === 0 || branches.length === 0) {
        console.log('   ⚠️  Warning: Need at least 1 service and 1 branch to create forms');
        console.log('   Please create services and branches first');
        return;
      }
      
      console.log('   ✅ Prerequisites check passed');
    } catch (prereqError) {
      console.log('   ❌ Prerequisites check failed:', prereqError.response?.data?.message || prereqError.message);
      return;
    }
      // 7. Forms Creation Test
    console.log('\n7. Forms Creation Test:');
    const formData = {
      name: 'Test API Form - ' + Date.now(),
      serviceId: 8,
      branchId: 9,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };
    
    console.log('   Sending form data:', JSON.stringify(formData, null, 4));
    
    try {
      const createResponse = await axios.post(`${BASE_URL}/api/forms`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('   ✅ Form creation response status:', createResponse.status);
      console.log('   Response structure:', Object.keys(createResponse.data));
      
      if (createResponse.data.data) {
        const form = createResponse.data.data;
        console.log('   Form data keys:', Object.keys(form));
        
        // Detailed analysis
        console.log('\n8. Response Analysis:');
        console.log('   Form ID:', form.id);
        console.log('   Form Name:', form.name);
        console.log('   Form Slug:', form.slug);
        console.log('   Public URL:', form.publicUrl || '❌ MISSING');
        console.log('   Embed Code:', form.embedCode ? '✅ Present (' + form.embedCode.length + ' chars)' : '❌ MISSING');
        
        // Check specific URLs
        if (form.publicUrl) {
          console.log('   ✅ Public URL generated correctly');
          console.log('   URL format check:', form.publicUrl.includes('/book/') ? '✅ Correct format' : '❌ Wrong format');
        } else {
          console.log('   ❌ Public URL is missing from response');
        }
        
        if (form.embedCode) {
          console.log('   ✅ Embed code generated correctly');
          console.log('   Embed format check:', form.embedCode.includes('<iframe') ? '✅ Correct format' : '❌ Wrong format');
          console.log('   Embed code preview:', form.embedCode.substring(0, 100) + '...');
        } else {
          console.log('   ❌ Embed code is missing from response');
        }
        
        // Full response dump for debugging
        console.log('\n9. Full Response (for debugging):');
        console.log(JSON.stringify(createResponse.data, null, 2));
        
      } else {
        console.log('   ❌ No form data in response');
        console.log('   Full response:', JSON.stringify(createResponse.data, null, 2));
      }
      
    } catch (createError) {
      console.log('   ❌ Form creation failed:', createError.response?.data?.message || createError.message);
      if (createError.response?.data) {
        console.log('   Error details:', JSON.stringify(createError.response.data, null, 2));
      }
    }
    
    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error(error.stack);
  } finally {
    await sequelize.close();
  }
}

// Run the comprehensive test
comprehensiveFormsTest();
