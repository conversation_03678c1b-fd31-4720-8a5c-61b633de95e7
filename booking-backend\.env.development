# Application Configuration
NODE_ENV=development
PORT=3000
API_VERSION=1.0.0
APP_NAME="Spa Booking System"
APP_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=261331064
DB_DATABASE=spa_booking_development
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Database Alter Table Configuration
DB_ALTER_ENABLED=true
DB_ALTER_DROP=false
DB_ALTER_FORCE=false

# Database Sync Configuration
DB_SYNC_FORCE=true
DB_SYNC_ALTER=true

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=dev-refresh-secret-key-change-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Bcrypt Configuration
BCRYPT_ROUNDS=10

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:4000,http://localhost:5173

# Frontend Configuration
FRONTEND_URL=http://localhost:4000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=./logs

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
