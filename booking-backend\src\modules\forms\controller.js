const FormsService = require('./service');
const { successResponse, errorResponse } = require('../../utils/response');
const { ValidationError, NotFoundError } = require('../../utils/errors');

/**
 * Forms Controller
 * Handles HTTP requests for forms management
 */
class FormsController {
  /**
   * Create a new form
   * POST /api/forms
   */  static async createForm(req, res, next) {
    try {
      const userId = req.user.id;
      
      console.log('=== Forms Controller - createForm Debug ===');
      console.log('User ID:', userId);
      console.log('Request body:', JSON.stringify(req.body, null, 2));
      console.log('Environment variables:');
      console.log('  - NODE_ENV:', process.env.NODE_ENV);
      console.log('  - FRONTEND_URL:', process.env.FRONTEND_URL);
      console.log('  - FRONTEND_URL type:', typeof process.env.FRONTEND_URL);
      console.log('  - FRONTEND_URL defined:', process.env.FRONTEND_URL !== undefined);
      
      const form = await FormsService.createForm(req.body, userId);
      
      console.log('Form created by service:');
      console.log('  - Form ID:', form.id);
      console.log('  - Form name:', form.name);
      console.log('  - Form slug:', form.slug);
      console.log('  - Form slug type:', typeof form.slug);
      console.log('  - Form slug defined:', form.slug !== undefined);
      console.log('  - Form object keys:', Object.keys(form.toJSON()));
        // Generate enhanced URLs and embed codes
      const urlsAndCodes = FormsService.generateFormUrls(form.slug);
      
      // Generate social media content
      const socialContent = FormsService.generateSocialMediaContent(
        form.name,
        form.service?.name || 'Service',
        form.owner?.name || 'Business'
      );
      
      console.log('Generated enhanced URLs and codes');
      console.log('  - Public URL:', urlsAndCodes.urls.publicUrl);
      console.log('  - QR Code URL:', urlsAndCodes.urls.qrCodeUrl);
      console.log('  - Social URLs count:', Object.keys(urlsAndCodes.urls.socialUrls).length);
      console.log('  - Embed codes count:', Object.keys(urlsAndCodes.embedCodes).length);
      
      const responseData = {
        ...form.toJSON(),
        // Basic URLs
        publicUrl: urlsAndCodes.urls.publicUrl,
        directUrl: urlsAndCodes.urls.directUrl,
        trackingUrl: urlsAndCodes.urls.trackingUrl,
        qrCodeUrl: urlsAndCodes.urls.qrCodeUrl,
        
        // Social media URLs
        socialUrls: urlsAndCodes.urls.socialUrls,
        
        // Multiple embed code options
        embedCode: urlsAndCodes.embedCodes.standard, // Default
        embedCodes: urlsAndCodes.embedCodes,
        
        // Social media content
        socialContent: socialContent
      };
      
      console.log('Response data keys:', Object.keys(responseData));
      console.log('Response data publicUrl:', responseData.publicUrl);
      console.log('Response data embedCode present:', !!responseData.embedCode);
      console.log('=== End Debug ===');      return successResponse(res, responseData, 'Form created successfully', 201);
    } catch (error) {
      console.error('Create form error:', error);
      
      if (error instanceof ValidationError) {
        return errorResponse(res, error.message, 400);
      }
      if (error instanceof NotFoundError) {
        return errorResponse(res, error.message, 404);
      }
      
      return errorResponse(res, 'Failed to create form', 500);
    }
  }

  /**
   * Get all forms for authenticated user
   * GET /api/forms
   */
  static async getUserForms(req, res, next) {
    try {
      const userId = req.user.id;
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        status: req.query.status,
        search: req.query.search
      };      const result = await FormsService.getUserForms(userId, options);
      
      // Add enhanced URLs and embed codes to each form
      const formsWithEnhancedUrls = result.forms.map(form => {
        const urlsAndCodes = FormsService.generateFormUrls(form.slug);
        const socialContent = FormsService.generateSocialMediaContent(
          form.name,
          form.service?.name || 'Service',
          form.owner?.name || 'Business'
        );
          return {
          ...form.toJSON(),
          // Basic info for easier frontend access
          service: form.service?.name || 'Unknown Service',
          branch: form.branch?.name || 'Unknown Branch', 
          createdAt: form.created_at,
          bookingsThisMonth: 0, // TODO: Calculate from bookings
          lastBooking: 'No bookings yet', // TODO: Get from latest booking
          // Enhanced URLs and codes
          publicUrl: urlsAndCodes.urls.publicUrl,
          directUrl: urlsAndCodes.urls.directUrl,
          qrCodeUrl: urlsAndCodes.urls.qrCodeUrl,
          socialUrls: urlsAndCodes.urls.socialUrls,
          embedCode: urlsAndCodes.embedCodes.standard,
          embedCodes: urlsAndCodes.embedCodes,
          socialContent: socialContent
        };
      });      return successResponse(res, {
        forms: formsWithEnhancedUrls,
        pagination: result.pagination
      }, 'Forms retrieved successfully');
    } catch (error) {
      console.error('Get user forms error:', error);
      return errorResponse(res, 'Failed to retrieve forms', 500);
    }
  }

  /**
   * Get form by ID
   * GET /api/forms/:id
   */
  static async getFormById(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const form = await FormsService.getFormById(id);
        // Check if user owns this form
      if (form.user_id !== userId) {
        return errorResponse(res, 'Access denied', 403);
      }

      // Generate enhanced URLs and embed codes
      const urlsAndCodes = FormsService.generateFormUrls(form.slug);
      const socialContent = FormsService.generateSocialMediaContent(
        form.name,
        form.service?.name || 'Service',
        form.owner?.name || 'Business'
      );      return successResponse(res, {
        ...form.toJSON(),
        publicUrl: urlsAndCodes.urls.publicUrl,
        directUrl: urlsAndCodes.urls.directUrl,
        qrCodeUrl: urlsAndCodes.urls.qrCodeUrl,
        socialUrls: urlsAndCodes.urls.socialUrls,
        embedCode: urlsAndCodes.embedCodes.standard,
        embedCodes: urlsAndCodes.embedCodes,
        socialContent: socialContent
      }, 'Form retrieved successfully');
    } catch (error) {
      console.error('Get form by ID error:', error);
      
      if (error instanceof NotFoundError) {
        return errorResponse(res, error.message, 404);
      }
      
      return errorResponse(res, 'Failed to retrieve form', 500);
    }
  }

  /**
   * Get form by slug (public access)
   * GET /api/public/forms/:slug
   */
  static async getFormBySlug(req, res, next) {
    try {
      const { slug } = req.params;
      const form = await FormsService.getFormBySlug(slug);      return successResponse(res, {
        id: form.slug, // Use slug as ID for public access
        name: form.name,
        businessName: form.owner.name,
        service: {
          name: form.service.name,
          price: form.service.price,
          duration: form.service.duration,
          description: form.service.description
        },
        branch: {
          name: form.branch.name,
          address: form.branch.address,
          phone: form.branch.phone,
          city: form.branch.city
        },        fields: typeof form.fields_config === 'string' ? JSON.parse(form.fields_config) : form.fields_config,
        branding: typeof form.branding_config === 'string' ? JSON.parse(form.branding_config) : form.branding_config
      }, 'Form retrieved successfully');
    } catch (error) {
      console.error('Get form by slug error:', error);
      
      if (error instanceof NotFoundError) {
        return errorResponse(res, error.message, 404);
      }
      
      return errorResponse(res, 'Failed to retrieve form', 500);
    }
  }

  /**
   * Update form
   * PUT /api/forms/:id
   */
  static async updateForm(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
        const form = await FormsService.updateForm(id, req.body, userId);

      // Generate enhanced URLs and embed codes
      const urlsAndCodes = FormsService.generateFormUrls(form.slug);
      const socialContent = FormsService.generateSocialMediaContent(
        form.name,
        form.service?.name || 'Service',
        form.owner?.name || 'Business'
      );      return successResponse(res, {
        ...form.toJSON(),
        publicUrl: urlsAndCodes.urls.publicUrl,
        directUrl: urlsAndCodes.urls.directUrl,
        qrCodeUrl: urlsAndCodes.urls.qrCodeUrl,
        socialUrls: urlsAndCodes.urls.socialUrls,
        embedCode: urlsAndCodes.embedCodes.standard,
        embedCodes: urlsAndCodes.embedCodes,
        socialContent: socialContent
      }, 'Form updated successfully');
    } catch (error) {
      console.error('Update form error:', error);
      
      if (error instanceof ValidationError) {
        return errorResponse(res, error.message, 400);
      }
      if (error instanceof NotFoundError) {
        return errorResponse(res, error.message, 404);
      }
      
      return errorResponse(res, 'Failed to update form', 500);
    }
  }

  /**
   * Delete form
   * DELETE /api/forms/:id
   */
  static async deleteForm(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      const result = await FormsService.deleteForm(id, userId);

      return successResponse(res, result);
    } catch (error) {
      console.error('Delete form error:', error);
      
      if (error instanceof NotFoundError) {
        return errorResponse(res, error.message, 404);
      }
      
      return errorResponse(res, 'Failed to delete form', 500);
    }
  }

  /**
   * Get form statistics
   * GET /api/forms/stats
   */
  static async getFormStats(req, res, next) {
    try {
      const userId = req.user.id;
      const stats = await FormsService.getFormStats(userId);

      return successResponse(res, {
        message: 'Form statistics retrieved successfully',
        data: stats
      });
    } catch (error) {
      console.error('Get form stats error:', error);
      return errorResponse(res, 'Failed to retrieve form statistics', 500);
    }
  }
}

module.exports = FormsController;
