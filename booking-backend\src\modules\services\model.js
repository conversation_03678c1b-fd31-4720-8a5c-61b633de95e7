/**
 * Service Model
 * Spa services management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Service = sequelize.define('Service', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [2, 100],
      notEmpty: true
    },
    comment: 'Service name'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Service description'
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      isIn: [['massage', 'facial', 'body_treatment', 'nail_care', 'hair_care', 'wellness', 'package']]
    },
    comment: 'Service category'
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 15,
      max: 480 // 8 hours max
    },
    comment: 'Service duration in minutes'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    },
    comment: 'Service price in VND'
  },
  discountPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    field: 'discount_price',
    comment: 'Discounted price in VND'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: 'Service status'
  },
  isPopular: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_popular',
    comment: 'Popular service flag'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Service images URLs'
  },
  benefits: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Service benefits list'
  },
  requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Service requirements/contraindications'
  },
  preparationInstructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'preparation_instructions',
    comment: 'Pre-service preparation instructions'
  },
  aftercareInstructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'aftercare_instructions',
    comment: 'Post-service care instructions'
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Branch ID (null for all branches)'
  },
  minAge: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    field: 'min_age',
    comment: 'Minimum age requirement'
  },
  maxAge: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 150
    },
    field: 'max_age',
    comment: 'Maximum age requirement'
  },
  genderRestriction: {
    type: DataTypes.STRING(10),
    allowNull: true,
    validate: {
      isIn: [['male', 'female', null]]
    },
    field: 'gender_restriction',
    comment: 'Gender restriction if any'
  },
  bookingAdvanceDays: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 30,
    validate: {
      min: 0,
      max: 365
    },
    field: 'booking_advance_days',
    comment: 'How many days in advance can be booked'
  },
  cancellationDeadlineHours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 24,
    validate: {
      min: 0,
      max: 168 // 1 week
    },
    field: 'cancellation_deadline_hours',
    comment: 'Cancellation deadline in hours'
  },
  maxBookingsPerDay: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 100
    },
    field: 'max_bookings_per_day',
    comment: 'Maximum bookings per day'
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    field: 'sort_order',
    comment: 'Display sort order'
  }
}, {
  tableName: 'services',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete
  
  // Indexes
  indexes: [
    { fields: ['name'] },
    { fields: ['category'] },
    { fields: ['is_active'] },
    { fields: ['is_popular'] },
    { fields: ['branch_id'] },
    { fields: ['price'] },
    { fields: ['sort_order'] },
    { fields: ['created_at'] }
  ],
  
  // Scopes
  scopes: {
    active: {
      where: { isActive: true }
    },
    popular: {
      where: { isPopular: true, isActive: true }
    },
    byCategory: (category) => ({
      where: { category, isActive: true }
    }),
    byBranch: (branchId) => ({
      where: { 
        [sequelize.Sequelize.Op.or]: [
          { branchId },
          { branchId: null } // Services available at all branches
        ],
        isActive: true
      }
    }),
    priceRange: (minPrice, maxPrice) => ({
      where: {
        price: {
          [sequelize.Sequelize.Op.between]: [minPrice, maxPrice]
        },
        isActive: true
      }
    })
  }
});

// Instance methods
Service.prototype.getEffectivePrice = function() {
  return this.discountPrice || this.price;
};

Service.prototype.getDiscountPercentage = function() {
  if (!this.discountPrice || this.discountPrice >= this.price) {
    return 0;
  }
  return Math.round(((this.price - this.discountPrice) / this.price) * 100);
};

Service.prototype.isAvailableForAge = function(age) {
  if (this.minAge && age < this.minAge) return false;
  if (this.maxAge && age > this.maxAge) return false;
  return true;
};

Service.prototype.isAvailableForGender = function(gender) {
  if (!this.genderRestriction) return true;
  return this.genderRestriction === gender;
};

Service.prototype.canBookInAdvance = function(bookingDate) {
  if (!this.bookingAdvanceDays) return true;
  
  const today = new Date();
  const maxBookingDate = new Date();
  maxBookingDate.setDate(today.getDate() + this.bookingAdvanceDays);
  
  return bookingDate <= maxBookingDate;
};

// Class methods
Service.getActiveServices = function(options = {}) {
  return this.scope('active').findAll({
    ...options,
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Service.getPopularServices = function(options = {}) {
  return this.scope('popular').findAll({
    ...options,
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Service.getServicesByCategory = function(category, options = {}) {
  return this.scope({ method: ['byCategory', category] }).findAll({
    ...options,
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Service.getServicesByBranch = function(branchId, options = {}) {
  return this.scope({ method: ['byBranch', branchId] }).findAll({
    ...options,
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Service.searchServices = function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  return this.scope('active').findAll({
    where: {
      [Op.or]: [
        { name: { [Op.like]: `%${searchTerm}%` } },
        { description: { [Op.like]: `%${searchTerm}%` } },
        { category: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options,
    order: [['sortOrder', 'ASC'], ['name', 'ASC']]
  });
};

Service.getServicesByPriceRange = function(minPrice, maxPrice, options = {}) {
  return this.scope({ method: ['priceRange', minPrice, maxPrice] }).findAll({
    ...options,
    order: [['price', 'ASC'], ['name', 'ASC']]
  });
};

// Define associations
Service.associate = (models) => {
  // Service belongs to Branch
  Service.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch',
    allowNull: true
  });

  // Service has many Bookings
  Service.hasMany(models.bookings, {
    foreignKey: 'service_id',
    as: 'bookings'
  });

  // Service belongs to many Users (employees who can perform this service)
  Service.belongsToMany(models.users, {
    through: 'employee_services',
    foreignKey: 'service_id',
    otherKey: 'employee_id',
    as: 'employees'
  });
};

module.exports = Service;
