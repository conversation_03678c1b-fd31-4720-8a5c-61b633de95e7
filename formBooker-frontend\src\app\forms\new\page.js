'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import servicesService from '@/services/servicesService';
import branchesService from '@/services/branchesService';
import FormsService from '@/services/formsService';
import { 
  Save, 
  Eye, 
  Copy, 
  Link as LinkIcon,
  Code,
  ArrowLeft,
  Check,
  X
} from 'lucide-react';

/**
 * Form Builder Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function NewFormPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    serviceId: '',
    branchId: '',
    fields: {
      customerName: true,
      phoneNumber: true,
      emailAddress: true,
      preferredDate: true,
      preferredTime: true,
      specialRequests: false
    }
  });
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedForm, setSavedForm] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState('');
  
  // API data states
  const [services, setServices] = useState([]);
  const [branches, setBranches] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [loadError, setLoadError] = useState(null);

  // Service instances
  // Using singleton instances from service modules

  // Load data on component mount
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load services and branches in parallel
      const [servicesResponse, branchesResponse] = await Promise.allSettled([
        servicesService.getServices({ isActive: true, limit: 100 }),
        branchesService.getBranches({ isActive: true, limit: 100 })
      ]);

      // Handle services response
      if (servicesResponse.status === 'fulfilled' && servicesResponse.value.success) {
        setServices(servicesResponse.value.data || []);
      } else {
        console.error('Failed to load services:', servicesResponse.reason);
      }
      setIsLoadingServices(false);

      // Handle branches response  
      if (branchesResponse.status === 'fulfilled' && branchesResponse.value.success) {
        setBranches(branchesResponse.value.data || []);
      } else {
        console.error('Failed to load branches:', branchesResponse.reason);
      }
      setIsLoadingBranches(false);

    } catch (error) {
      console.error('Error loading initial data:', error);
      setLoadError('Failed to load form data. Please refresh the page.');
      setIsLoadingServices(false);
      setIsLoadingBranches(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Removed handleFieldToggle since we're not using checkboxes anymore
  // All form fields are now standard and always included

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Validate form data
      if (!formData.name || !formData.serviceId || !formData.branchId) {
        alert('Please fill in all required fields');
        setIsSaving(false);
        return;
      }

      // Create form using FormsService
      const result = await FormsService.createForm(formData);
      
      if (result.success) {
        console.log('Form created successfully - Full result:', result);
        console.log('Form data received:', result.data);
        console.log('Public URL:', result.data?.publicUrl);
        console.log('Embed Code:', result.data?.embedCode);

        setSavedForm(result.data);
        setShowSuccessModal(true);
      } else {
        // Handle different error types with appropriate user feedback
        if (result.errorType === 'AUTHENTICATION_REQUIRED') {
          alert(`Authentication Required: ${result.error}`);
          if (result.redirectTo) {
            router.push(result.redirectTo);
          }
        } else if (result.errorType === 'MISSING_PREREQUISITES') {
          const message = `${result.error}\n\nSuggestions:\n${result.suggestions?.join('\n') || 'Please create services and branches first.'}`;
          alert(message);
        } else if (result.errorType === 'VALIDATION_ERROR') {
          let message = `Validation Error: ${result.error}`;
          if (result.validationErrors?.length > 0) {
            message += '\n\nDetails:\n' + result.validationErrors.map(err => `- ${err.field}: ${err.message}`).join('\n');
          }
          alert(message);
        } else {
          alert(`Error: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('Error saving form:', error);
      alert('Failed to save form. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCopy = async (text, type) => {
    try {
      if (!text) {
        setCopySuccess(`Failed to copy ${type}: No content available`);
        setTimeout(() => setCopySuccess(''), 3000);
        return;
      }

      await navigator.clipboard.writeText(text);
      setCopySuccess(`${type} copied to clipboard!`);
      setTimeout(() => setCopySuccess(''), 3000);
      console.log(`${type} copied to clipboard`);
    } catch (err) {
      console.error('Failed to copy:', err);
      setCopySuccess(`Failed to copy ${type}`);
      setTimeout(() => setCopySuccess(''), 3000);
    }
  };

  const selectedService = services.find(s => s.id === parseInt(formData.serviceId));
  const selectedBranch = branches.find(b => b.id === parseInt(formData.branchId));

  // Success Modal Component
  const SuccessModal = () => {
    if (!showSuccessModal || !savedForm) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Modal Header */}
          <div className="p-6 border-b border-neutral-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-text">Form Created Successfully!</h2>
                  <p className="text-neutral-600 text-sm mt-1">Your booking form is ready to share with customers</p>
                </div>
              </div>
              <button
                onClick={() => setShowSuccessModal(false)}
                className="text-neutral-400 hover:text-neutral-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Modal Body */}
          <div className="p-6 space-y-6">
            {/* Form Info */}
            <div className="bg-neutral-50 rounded-lg p-4">
              <h3 className="font-medium text-text mb-2">Form Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Name:</span>
                  <span className="font-medium">{savedForm.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Service:</span>
                  <span className="font-medium">{selectedService?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Branch:</span>
                  <span className="font-medium">{selectedBranch?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Status:</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {savedForm.status || 'Active'}
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Share Options */}
            <div className="space-y-6">
              <h3 className="font-medium text-text">🚀 Share Your Booking Form</h3>

              {/* Primary Sharing Methods */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Direct Link */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <LinkIcon className="h-4 w-4 text-primary-600 mr-2" />
                      <span className="text-sm font-medium text-neutral-700">Direct Link</span>
                    </div>
                    <button
                      onClick={() => handleCopy(savedForm.publicUrl, 'Direct Link')}
                      className="btn-outline btn-sm"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Link
                    </button>
                  </div>
                  <div className="p-3 bg-neutral-50 rounded-lg text-xs text-neutral-600 break-all border">
                    {savedForm.publicUrl || 'Link not available - Check console for debug info'}
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">Perfect for social media, email, and SMS sharing</p>
                </div>

                {/* Standard Embed Code */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Code className="h-4 w-4 text-accent-600 mr-2" />
                      <span className="text-sm font-medium text-neutral-700">Standard Embed</span>
                    </div>
                    <button
                      onClick={() => handleCopy(savedForm.embedCode, 'Embed Code')}
                      className="btn-outline btn-sm"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Code
                    </button>
                  </div>
                  <div className="p-3 bg-neutral-50 rounded-lg text-xs text-neutral-600 break-all border font-mono max-h-20 overflow-y-auto">
                    {savedForm.embedCode || 'Embed code not available - Check console for debug info'}
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">Embed directly on your website (600px height)</p>
                </div>
              </div>

              {/* QR Code */}
              {savedForm.qrCodeUrl && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-purple-600 rounded-lg flex items-center justify-center mr-2">
                        <span className="text-white text-xs">📱</span>
                      </div>
                      <span className="text-sm font-medium text-purple-800">QR Code for Mobile Access</span>
                    </div>
                    <button
                      onClick={() => window.open(savedForm.qrCodeUrl, '_blank')}
                      className="btn-outline btn-sm text-purple-600 border-purple-300"
                    >
                      View QR Code
                    </button>
                  </div>
                  <p className="text-xs text-purple-700">Let customers scan to book instantly on their phones</p>
                </div>
              )}

              {/* Social Media Sharing */}
              {savedForm.socialUrls && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-3">📢 Social Media Sharing</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <button
                      onClick={() => window.open(savedForm.socialUrls.facebook, '_blank')}
                      className="flex items-center justify-center p-2 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 transition-colors"
                    >
                      📘 Facebook
                    </button>
                    <button
                      onClick={() => window.open(savedForm.socialUrls.twitter, '_blank')}
                      className="flex items-center justify-center p-2 bg-sky-500 text-white rounded text-xs hover:bg-sky-600 transition-colors"
                    >
                      🐦 Twitter
                    </button>
                    <button
                      onClick={() => window.open(savedForm.socialUrls.linkedin, '_blank')}
                      className="flex items-center justify-center p-2 bg-blue-800 text-white rounded text-xs hover:bg-blue-900 transition-colors"
                    >
                      💼 LinkedIn
                    </button>
                    <button
                      onClick={() => window.open(savedForm.socialUrls.whatsapp, '_blank')}
                      className="flex items-center justify-center p-2 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors"
                    >
                      💬 WhatsApp
                    </button>
                  </div>
                </div>
              )}

              {/* Advanced Embed Options */}
              {savedForm.embedCodes && (
                <details className="bg-neutral-50 border border-neutral-200 rounded-lg">
                  <summary className="p-4 cursor-pointer text-sm font-medium text-neutral-700 hover:text-neutral-900 flex items-center justify-between">
                    <span>⚙️ Advanced Embed Options</span>
                    <span className="text-xs text-neutral-500">Click to expand</span>
                  </summary>
                  <div className="px-4 pb-4 border-t border-neutral-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                      {/* Compact Version */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">Compact (400px)</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.compact, 'Compact Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Perfect for sidebars and smaller spaces</p>
                      </div>

                      {/* Full Height Version */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">Full Height (800px)</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.fullHeight, 'Full Height Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Extra space for longer forms</p>
                      </div>

                      {/* Mobile Optimized */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">Mobile Optimized</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.mobile, 'Mobile Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Responsive design for mobile devices</p>
                      </div>

                      {/* Styled with Shadow */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">Styled Shadow</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.styled, 'Styled Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Enhanced visual appeal with shadow</p>
                      </div>

                      {/* JavaScript Dynamic */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">JavaScript Dynamic</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.javascript, 'JavaScript Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Dynamic loading with JavaScript</p>
                      </div>

                      {/* Popup Button */}
                      <div className="p-3 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-neutral-700">Popup Button</span>
                          <button
                            onClick={() => handleCopy(savedForm.embedCodes.button, 'Button Embed')}
                            className="btn-outline btn-xs"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-neutral-500">Opens form in popup window</p>
                      </div>
                    </div>
                  </div>
                </details>
              )}
            </div>
          </div>

          {/* Modal Footer */}
          <div className="p-6 border-t border-neutral-200 bg-neutral-50 rounded-b-lg">
            <div className="flex justify-between items-center">
              <button
                onClick={() => router.push('/forms')}
                className="btn-outline"
              >
                View All Forms
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSuccessModal(false)}
                  className="btn-outline"
                >
                  Continue Editing
                </button>
                <button
                  onClick={() => {
                    setShowSuccessModal(false);
                    // Reset form for creating another
                    setFormData({
                      name: '',
                      serviceId: '',
                      branchId: '',
                      fields: {
                        customerName: true,
                        phoneNumber: true,
                        emailAddress: true,
                        preferredDate: true,
                        preferredTime: true,
                        specialRequests: false
                      }
                    });
                    setSavedForm(null);
                  }}
                  className="btn-primary"
                >
                  Create Another Form
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <DashboardLayout>
      {/* Success Modal */}
      <SuccessModal />

      <div className="py-6">          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center">
                <button
                  onClick={() => router.back()}
                  className="btn-ghost btn-sm mr-4"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back
                </button>
                <div>
                  <h1 className="text-3xl font-bold text-text">Create Booking Form</h1>
                  <p className="mt-2 text-neutral-600">
                    Build a custom booking form for your service and share it with customers
                  </p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {loadError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <X className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-red-700">{loadError}</p>
                </div>
              </div>
            )}

            {/* Prerequisites Check */}
            {(!isLoadingServices && !isLoadingBranches && (services.length === 0 || branches.length === 0)) && (
              <div className="mb-6 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <span className="text-yellow-600 text-lg">⚠️</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-yellow-800 mb-2">Setup Required</h3>
                    <p className="text-yellow-700 mb-4">
                      Before creating a booking form, you need to set up your business information. This is why you're seeing a "404 error" - the system cannot find the required services and branches.
                    </p>

                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="font-semibold text-red-800 mb-2">🔍 Why am I seeing a 404 error?</h4>
                      <p className="text-sm text-red-700">
                        The "404 Not Found" error occurs because the backend cannot find a valid service and branch combination for your account.
                        This is a business logic validation, not a missing API endpoint.
                      </p>
                    </div>

                    <div className="space-y-3">
                      {branches.length === 0 && (
                        <div className="flex items-center p-3 bg-white border border-yellow-200 rounded-lg">
                          <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-yellow-600 text-sm">1</span>
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-yellow-800">Create a Branch First</p>
                            <p className="text-sm text-yellow-600">Add your business location and contact details</p>
                            <p className="text-xs text-yellow-500 mt-1">Required: Name, Address, Phone, City, District, Ward</p>
                          </div>
                          <Link href="/branches" className="btn-primary btn-sm">
                            Create Branch
                          </Link>
                        </div>
                      )}
                      {services.length === 0 && (
                        <div className="flex items-center p-3 bg-white border border-yellow-200 rounded-lg">
                          <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-yellow-600 text-sm">{branches.length === 0 ? '2' : '1'}</span>
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-yellow-800">Create a Service</p>
                            <p className="text-sm text-yellow-600">Define the services customers can book</p>
                            <p className="text-xs text-yellow-500 mt-1">Required: Name, Category, Duration, Price, Link to Branch</p>
                          </div>
                          <Link href="/services" className="btn-primary btn-sm">
                            Create Service
                          </Link>
                        </div>
                      )}
                    </div>

                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-700">
                        <strong>💡 Technical Note:</strong> The service must be linked to a branch where you are the manager.
                        Once you have both, the form creation will work without any 404 errors.
                      </p>
                    </div>

                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">✅ After Setup:</h4>
                      <ul className="text-sm text-green-700 space-y-1">
                        <li>• The 404 error will disappear</li>
                        <li>• You can create unlimited booking forms</li>
                        <li>• Each form will have a unique public URL</li>
                        <li>• Customers can book your services online</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Copy Success Toast */}
            {copySuccess && (
              <div className="fixed top-4 right-4 z-50 p-4 bg-green-50 border border-green-200 rounded-lg shadow-lg">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-2" />
                  <p className="text-green-700 font-medium">{copySuccess}</p>
                </div>
              </div>
            )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Builder */}
            <div className="space-y-6">
              {/* Form Settings */}
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-semibold text-text">Form Settings</h2>
                  <p className="text-sm text-neutral-500 mt-1">Configure your booking form details</p>
                </div>
                <div className="card-body space-y-4">
                  {/* Form Name */}
                  <div>
                    <label className="form-label">Form Name *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="e.g., Hair Cut Booking Form"
                    />
                    <p className="text-xs text-neutral-500 mt-1">Give your form a descriptive name</p>
                  </div>

                  {/* Service Selection */}
                  <div>
                    <label className="form-label">Service *</label>
                    {isLoadingServices ? (
                      <div className="form-input flex items-center">
                        <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        Loading services...
                      </div>
                    ) : (
                      <select
                        name="serviceId"
                        value={formData.serviceId}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Choose a service for this form</option>
                        {services.length === 0 ? (
                          <option disabled>No services available</option>
                        ) : (
                          services.map(service => (
                            <option key={service.id} value={service.id}>
                              {service.name} - ${service.price} ({service.duration} min)
                            </option>
                          ))
                        )}
                      </select>
                    )}
                    <p className="text-xs text-neutral-500 mt-1">Select the service customers will book</p>
                  </div>

                  {/* Branch Selection */}
                  <div>
                    <label className="form-label">Branch *</label>
                    {isLoadingBranches ? (
                      <div className="form-input flex items-center">
                        <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                        Loading branches...
                      </div>
                    ) : (
                      <select
                        name="branchId"
                        value={formData.branchId}
                        onChange={handleInputChange}
                        className="form-input"
                      >
                        <option value="">Choose a branch location</option>
                        {branches.length === 0 ? (
                          <option disabled>No branches available</option>
                        ) : (
                          branches.map(branch => (
                            <option key={branch.id} value={branch.id}>
                              {branch.name} - {branch.address}
                            </option>
                          ))
                        )}
                      </select>
                    )}
                    <p className="text-xs text-neutral-500 mt-1">Select the branch where service will be provided</p>
                  </div>
                </div>
              </div>

              {/* Form Fields */}
              <div className="card">
                <div className="card-header">
                  <h2 className="text-lg font-semibold text-text">Form Fields</h2>
                  <p className="text-sm text-neutral-500 mt-1">Your form will include these standard booking fields</p>
                </div>
                <div className="card-body">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Customer Name</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Phone Number</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Email Address</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Preferred Date</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <Check className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Preferred Time</p>
                        <p className="text-xs text-green-600">Required field</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <Check className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-blue-800">Special Requests</p>
                        <p className="text-xs text-blue-600">Optional field</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 p-3 bg-neutral-50 border border-neutral-200 rounded-lg">
                    <p className="text-sm text-neutral-600">
                      <strong>Note:</strong> These standard fields are automatically included in all booking forms to ensure we collect the necessary information from customers.
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-4">
                {/* Validation Messages */}
                {(!formData.name || !formData.serviceId || !formData.branchId) && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className="w-5 h-5 rounded-full bg-yellow-400 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">!</span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-yellow-800">Complete Required Fields</p>
                        <ul className="mt-1 text-xs text-yellow-700 space-y-1">
                          {!formData.name && <li>• Form name is required</li>}
                          {!formData.serviceId && <li>• Please select a service</li>}
                          {!formData.branchId && <li>• Please select a branch</li>}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex space-x-4">
                  <button
                    onClick={() => setIsPreviewOpen(true)}
                    className="btn-outline flex-1"
                    disabled={!formData.name || !formData.serviceId || !formData.branchId || services.length === 0 || branches.length === 0}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Form
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isSaving || !formData.name || !formData.serviceId || !formData.branchId || services.length === 0 || branches.length === 0}
                    className="btn-primary flex-1"
                  >
                    {isSaving ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Creating Form...
                      </div>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create & Save Form
                      </>
                    )}
                  </button>
                </div>
              </div>


            </div>

            {/* Live Preview */}
            <div className="lg:sticky lg:top-6">
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-semibold text-text">Live Preview</h3>
                  <p className="text-sm text-neutral-500 mt-1">See how your form will look to customers</p>
                </div>
                <div className="card-body">
                  {/* Form Preview */}
                  <div className="border border-neutral-200 rounded-lg p-6 bg-white shadow-sm">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-semibold text-text mb-2">
                        {formData.name || 'Your Booking Form'}
                      </h3>
                      {selectedService && (
                        <div className="text-sm text-neutral-600 space-y-1 p-3 bg-primary-50 rounded-lg border border-primary-200">
                          <p className="font-medium text-primary-800">Service: {selectedService.name}</p>
                          <p className="text-primary-700">Duration: {selectedService.duration} minutes</p>
                          <p className="text-primary-700">Price: ${selectedService.price}</p>
                        </div>
                      )}
                      {selectedBranch && (
                        <div className="text-sm text-neutral-600 mt-2 p-2 bg-neutral-50 rounded border">
                          <p className="font-medium">📍 Location: {selectedBranch.name}</p>
                          <p className="text-xs">{selectedBranch.address}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      {formData.fields.customerName && (
                        <div>
                          <label className="form-label">Full Name *</label>
                          <input type="text" className="form-input" placeholder="Enter your full name" disabled />
                        </div>
                      )}
                      {formData.fields.phoneNumber && (
                        <div>
                          <label className="form-label">Phone Number *</label>
                          <input type="tel" className="form-input" placeholder="Enter your phone number" disabled />
                        </div>
                      )}
                      {formData.fields.emailAddress && (
                        <div>
                          <label className="form-label">Email Address *</label>
                          <input type="email" className="form-input" placeholder="Enter your email address" disabled />
                        </div>
                      )}
                      {formData.fields.preferredDate && (
                        <div>
                          <label className="form-label">Preferred Date *</label>
                          <input type="date" className="form-input" disabled />
                        </div>
                      )}
                      {formData.fields.preferredTime && (
                        <div>
                          <label className="form-label">Preferred Time *</label>
                          <input type="time" className="form-input" disabled />
                        </div>
                      )}
                      {formData.fields.specialRequests && (
                        <div>
                          <label className="form-label">Special Requests</label>
                          <textarea className="form-input" rows="3" placeholder="Any special requests or notes..." disabled />
                        </div>
                      )}
                      <button className="btn-primary w-full" disabled>
                        📅 Book My Appointment
                      </button>
                    </div>

                    <div className="text-center mt-6 pt-4 border-t border-neutral-200">
                      <p className="text-xs text-neutral-400">Powered by FormBooker</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
