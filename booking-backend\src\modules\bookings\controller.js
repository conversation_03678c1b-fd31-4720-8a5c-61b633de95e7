/**
 * Booking Controller
 * Handles HTTP requests for booking management
 */

const BookingService = require('./service');
const { successResponse, errorResponse } = require('../../utils/response');
const logger = require('../../utils/logger');

class BookingController {
  /**
   * Create a new booking
   * POST /api/bookings
   */
  static async createBooking(req, res, next) {
    try {
      const bookingData = req.body;
      const userId = req.user.id;

      const booking = await BookingService.createBooking(bookingData, userId);

      logger.info('Booking created successfully', {
        bookingId: booking.id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking created successfully', 201);
    } catch (error) {
      logger.error('Create booking failed', {
        error: error.message,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Get all bookings with pagination and filtering
   * GET /api/bookings
   */
  static async getAllBookings(req, res, next) {
    try {
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        customerId: req.query.customerId,
        serviceId: req.query.serviceId,
        branchId: req.query.branchId,
        employeeId: req.query.employeeId,
        bookingDate: req.query.bookingDate,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        paymentStatus: req.query.paymentStatus,
        search: req.query.search
      };

      const result = await BookingService.getAllBookings(options);

      logger.info('Bookings retrieved successfully', {
        userId: req.user.id,
        count: result.bookings.length,
        page: options.page
      });

      return successResponse(res, result.bookings, 'Bookings retrieved successfully', 200, {
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Get all bookings failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get booking by ID
   * GET /api/bookings/:id
   */
  static async getBookingById(req, res, next) {
    try {
      const { id } = req.params;
      const booking = await BookingService.getBookingById(parseInt(id));

      logger.info('Booking retrieved successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking retrieved successfully');
    } catch (error) {
      logger.error('Get booking by ID failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Get booking by booking code
   * GET /api/bookings/code/:code
   */
  static async getBookingByCode(req, res, next) {
    try {
      const { code } = req.params;
      const booking = await BookingService.getBookingByCode(code);

      logger.info('Booking retrieved by code successfully', {
        bookingCode: code,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking retrieved successfully');
    } catch (error) {
      logger.error('Get booking by code failed', {
        error: error.message,
        bookingCode: req.params.code,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Update booking
   * PUT /api/bookings/:id
   */
  static async updateBooking(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      const booking = await BookingService.updateBooking(parseInt(id), updateData, userId);

      logger.info('Booking updated successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking updated successfully');
    } catch (error) {
      logger.error('Update booking failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Cancel booking
   * PATCH /api/bookings/:id/cancel
   */
  static async cancelBooking(req, res, next) {
    try {
      const { id } = req.params;
      const cancellationData = req.body;
      const userId = req.user.id;

      const booking = await BookingService.cancelBooking(parseInt(id), cancellationData, userId);

      logger.info('Booking cancelled successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking cancelled successfully');
    } catch (error) {
      logger.error('Cancel booking failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Confirm booking
   * PATCH /api/bookings/:id/confirm
   */
  static async confirmBooking(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const booking = await BookingService.confirmBooking(parseInt(id), userId);

      logger.info('Booking confirmed successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking confirmed successfully');
    } catch (error) {
      logger.error('Confirm booking failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Complete booking
   * PATCH /api/bookings/:id/complete
   */
  static async completeBooking(req, res, next) {
    try {
      const { id } = req.params;
      const completionData = req.body;
      const userId = req.user.id;

      const booking = await BookingService.completeBooking(parseInt(id), completionData, userId);

      logger.info('Booking completed successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, booking, 'Booking completed successfully');
    } catch (error) {
      logger.error('Complete booking failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Get booking statistics
   * GET /api/bookings/stats
   */
  static async getBookingStats(req, res, next) {
    try {
      const options = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        branchId: req.query.branchId,
        employeeId: req.query.employeeId
      };

      const stats = await BookingService.getBookingStats(options);

      logger.info('Booking statistics retrieved successfully', {
        userId: req.user.id,
        options
      });

      return successResponse(res, stats, 'Booking statistics retrieved successfully');
    } catch (error) {
      logger.error('Get booking stats failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get available time slots
   * GET /api/bookings/available-slots
   */
  static async getAvailableTimeSlots(req, res, next) {
    try {
      const { branchId, serviceId, employeeId, date } = req.query;

      if (!branchId || !serviceId || !date) {
        return errorResponse(res, 'Branch ID, Service ID, and date are required', 400, 'MISSING_REQUIRED_FIELDS');
      }

      const timeSlots = await BookingService.getAvailableTimeSlots(
        parseInt(branchId),
        parseInt(serviceId),
        employeeId ? parseInt(employeeId) : null,
        date
      );

      logger.info('Available time slots retrieved successfully', {
        userId: req.user.id,
        branchId,
        serviceId,
        employeeId,
        date
      });

      return successResponse(res, timeSlots, 'Available time slots retrieved successfully');
    } catch (error) {
      logger.error('Get available time slots failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Delete booking
   * DELETE /api/bookings/:id
   */
  static async deleteBooking(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const result = await BookingService.deleteBooking(parseInt(id), userId);

      logger.info('Booking deleted successfully', {
        bookingId: id,
        userId: req.user.id
      });

      return successResponse(res, result, 'Booking deleted successfully');
    } catch (error) {
      logger.error('Delete booking failed', {
        error: error.message,
        bookingId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Create booking from public form (no auth required)
   * POST /api/public/bookings
   */
  static async createPublicBooking(req, res, next) {
    try {
      const { formSlug, customerName, phoneNumber, emailAddress, preferredDate, preferredTime, specialRequests } = req.body;

      // Validate required fields
      if (!formSlug || !customerName || !phoneNumber || !emailAddress || !preferredDate || !preferredTime) {
        return errorResponse(res, 'Missing required fields', 400);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailAddress)) {
        return errorResponse(res, 'Invalid email format', 400);
      }

      // Validate phone format (basic)
      const phoneRegex = /^[\d\s\-\+\(\)]+$/;
      if (!phoneRegex.test(phoneNumber)) {
        return errorResponse(res, 'Invalid phone number format', 400);
      }

      const booking = await BookingService.createPublicBooking({
        formSlug,
        customerName,
        phoneNumber,
        emailAddress,
        preferredDate,
        preferredTime,
        specialRequests
      });

      logger.info('Public booking created successfully', {
        bookingId: booking.id,
        formSlug,
        customerEmail: emailAddress
      });

      return successResponse(res, booking, 'Booking created successfully', 201);
    } catch (error) {
      logger.error('Create public booking failed', {
        error: error.message,
        body: req.body
      });
      next(error);
    }
  }
}

module.exports = BookingController;
