/**
 * Branches Swagger Documentation
 * API documentation for branch management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Branch:
 *       type: object
 *       required:
 *         - name
 *         - address
 *         - phone
 *         - city
 *       properties:
 *         id:
 *           type: integer
 *           description: Branch ID
 *         name:
 *           type: string
 *           description: Branch name
 *           example: "Spa Relax Nguyen Hue"
 *         address:
 *           type: string
 *           description: Full address
 *           example: "123 Nguyen Hue Street, District 1, Ho Chi Minh City"
 *         phone:
 *           type: string
 *           description: Branch phone number
 *           example: "0123456789"
 *         email:
 *           type: string
 *           format: email
 *           description: Branch email address
 *           example: "<EMAIL>"
 *         city:
 *           type: string
 *           description: City name
 *           example: "Ho Chi Minh City"
 *         district:
 *           type: string
 *           description: District name
 *           example: "District 1"
 *         ward:
 *           type: string
 *           description: Ward name
 *           example: "Ben Nghe Ward"
 *         latitude:
 *           type: number
 *           format: float
 *           description: GPS latitude
 *           example: 10.7769
 *         longitude:
 *           type: number
 *           format: float
 *           description: GPS longitude
 *           example: 106.7009
 *         openTime:
 *           type: string
 *           format: time
 *           description: Opening time
 *           example: "08:00"
 *         closeTime:
 *           type: string
 *           format: time
 *           description: Closing time
 *           example: "20:00"
 *         workingDays:
 *           type: array
 *           items:
 *             type: string
 *             enum: [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
 *           description: Working days of the week
 *           example: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
 *         isActive:
 *           type: boolean
 *           description: Branch status
 *           example: true
 *         description:
 *           type: string
 *           description: Branch description
 *           example: "Premium spa services in the heart of the city"
 *         facilities:
 *           type: array
 *           items:
 *             type: string
 *           description: Available facilities
 *           example: ["parking", "wifi", "air_conditioning", "wheelchair_access"]
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           description: Branch images URLs
 *           example: ["https://example.com/branch1.jpg", "https://example.com/branch2.jpg"]
 *         managerId:
 *           type: integer
 *           description: Branch manager user ID
 *           example: 5
 *         manager:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *             phone:
 *               type: string
 *         employees:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               role:
 *                 type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     BranchCreate:
 *       type: object
 *       required:
 *         - name
 *         - address
 *         - phone
 *         - city
 *       properties:
 *         name:
 *           type: string
 *           example: "Spa Relax Nguyen Hue"
 *         address:
 *           type: string
 *           example: "123 Nguyen Hue Street, District 1, Ho Chi Minh City"
 *         phone:
 *           type: string
 *           example: "0123456789"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         city:
 *           type: string
 *           example: "Ho Chi Minh City"
 *         district:
 *           type: string
 *           example: "District 1"
 *         ward:
 *           type: string
 *           example: "Ben Nghe Ward"
 *         latitude:
 *           type: number
 *           format: float
 *           example: 10.7769
 *         longitude:
 *           type: number
 *           format: float
 *           example: 106.7009
 *         openTime:
 *           type: string
 *           format: time
 *           example: "08:00"
 *         closeTime:
 *           type: string
 *           format: time
 *           example: "20:00"
 *         workingDays:
 *           type: array
 *           items:
 *             type: string
 *             enum: [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
 *           example: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
 *         managerId:
 *           type: integer
 *           example: 5
 *         description:
 *           type: string
 *           example: "Premium spa services in the heart of the city"
 *         facilities:
 *           type: array
 *           items:
 *             type: string
 *           example: ["parking", "wifi", "air_conditioning"]
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           example: ["https://example.com/branch1.jpg"]
 *     
 *     BranchUpdate:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           example: "Spa Relax Nguyen Hue - Updated"
 *         address:
 *           type: string
 *           example: "456 Nguyen Hue Street, District 1, Ho Chi Minh City"
 *         phone:
 *           type: string
 *           example: "0987654321"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         city:
 *           type: string
 *           example: "Ho Chi Minh City"
 *         district:
 *           type: string
 *           example: "District 1"
 *         ward:
 *           type: string
 *           example: "Ben Nghe Ward"
 *         latitude:
 *           type: number
 *           format: float
 *           example: 10.7769
 *         longitude:
 *           type: number
 *           format: float
 *           example: 106.7009
 *         openTime:
 *           type: string
 *           format: time
 *           example: "07:00"
 *         closeTime:
 *           type: string
 *           format: time
 *           example: "21:00"
 *         workingDays:
 *           type: array
 *           items:
 *             type: string
 *             enum: [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
 *           example: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
 *         isActive:
 *           type: boolean
 *           example: true
 *         managerId:
 *           type: integer
 *           example: 7
 *         description:
 *           type: string
 *           example: "Updated premium spa services"
 *         facilities:
 *           type: array
 *           items:
 *             type: string
 *           example: ["parking", "wifi", "air_conditioning", "wheelchair_access"]
 *         images:
 *           type: array
 *           items:
 *             type: string
 *             format: uri
 *           example: ["https://example.com/updated1.jpg", "https://example.com/updated2.jpg"]
 *     
 *     BranchWithDistance:
 *       allOf:
 *         - $ref: '#/components/schemas/Branch'
 *         - type: object
 *           properties:
 *             distance:
 *               type: number
 *               format: float
 *               description: Distance in kilometers
 *               example: 2.5
 *     
 *     BranchStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of active branches
 *           example: 25
 *         newThisMonth:
 *           type: integer
 *           description: New branches this month
 *           example: 3
 *         withLocation:
 *           type: integer
 *           description: Branches with GPS coordinates
 *           example: 20
 *         byCity:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "Ho Chi Minh City": 15
 *             "Hanoi": 8
 *             "Da Nang": 2
 *     
 *     BranchOpenStatus:
 *       type: object
 *       properties:
 *         isOpen:
 *           type: boolean
 *           description: Whether the branch is currently open
 *           example: true
 *         workingHours:
 *           type: object
 *           properties:
 *             openTime:
 *               type: string
 *               format: time
 *               example: "08:00"
 *             closeTime:
 *               type: string
 *               format: time
 *               example: "20:00"
 *             workingDays:
 *               type: array
 *               items:
 *                 type: string
 *               example: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
 *         currentTime:
 *           type: string
 *           format: date-time
 *           description: The time that was checked
 *           example: "2025-06-09T14:30:00.000Z"
 *         message:
 *           type: string
 *           description: Status message
 *           example: "Branch is currently open"
 */

/**
 * @swagger
 * /api/branches:
 *   get:
 *     summary: Get all branches with pagination and filters
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Filter by city name
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: Filter by district name
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Filter by active status
 *       - in: query
 *         name: hasLocation
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Filter branches with GPS coordinates
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in name, address, city, district
 *     responses:
 *       200:
 *         description: Branches retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Branch'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Branches retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create new branch
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BranchCreate'
 *     responses:
 *       201:
 *         description: Branch created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branch created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       409:
 *         description: Branch name, phone, or email already exists
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/{id}:
 *   get:
 *     summary: Get branch by ID
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *     responses:
 *       200:
 *         description: Branch retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branch retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update branch
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BranchUpdate'
 *     responses:
 *       200:
 *         description: Branch updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branch updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       409:
 *         description: Branch name, phone, or email already exists
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete branch (soft delete)
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *     responses:
 *       200:
 *         description: Branch deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Cannot delete branch with active employees
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/stats:
 *   get:
 *     summary: Get branch statistics
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Branch statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/BranchStats'
 *                 message:
 *                   type: string
 *                   example: "Branch statistics retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/active:
 *   get:
 *     summary: Get active branches (simple list)
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 200
 *           default: 100
 *         description: Maximum number of branches to return
 *     responses:
 *       200:
 *         description: Active branches retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       address:
 *                         type: string
 *                       city:
 *                         type: string
 *                       phone:
 *                         type: string
 *                 message:
 *                   type: string
 *                   example: "Active branches retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/nearby:
 *   get:
 *     summary: Find nearby branches
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: latitude
 *         required: true
 *         schema:
 *           type: number
 *           format: float
 *           minimum: -90
 *           maximum: 90
 *         description: Latitude coordinate
 *         example: 10.7769
 *       - in: query
 *         name: longitude
 *         required: true
 *         schema:
 *           type: number
 *           format: float
 *           minimum: -180
 *           maximum: 180
 *         description: Longitude coordinate
 *         example: 106.7009
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *           format: float
 *           minimum: 0.1
 *           maximum: 100
 *           default: 10
 *         description: Search radius in kilometers
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 20
 *         description: Maximum number of branches to return
 *     responses:
 *       200:
 *         description: Nearby branches retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/BranchWithDistance'
 *                 message:
 *                   type: string
 *                   example: "Nearby branches retrieved successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/city/{city}:
 *   get:
 *     summary: Get branches by city
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: city
 *         required: true
 *         schema:
 *           type: string
 *         description: City name
 *         example: "Ho Chi Minh City"
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Maximum number of branches to return
 *     responses:
 *       200:
 *         description: Branches in city retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branches in Ho Chi Minh City retrieved successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/{id}/status:
 *   patch:
 *     summary: Toggle branch status (activate/deactivate)
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - isActive
 *             properties:
 *               isActive:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Branch status changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branch activated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/{id}/images:
 *   post:
 *     summary: Update branch images
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - images
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 description: Array of image URLs
 *                 example: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
 *     responses:
 *       200:
 *         description: Branch images updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Branch'
 *                 message:
 *                   type: string
 *                   example: "Branch images updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/branches/{id}/open-status:
 *   get:
 *     summary: Check if branch is open
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Branch ID
 *       - in: query
 *         name: datetime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Date and time to check (ISO8601 format). If not provided, current time is used.
 *         example: "2025-06-09T14:30:00.000Z"
 *     responses:
 *       200:
 *         description: Branch status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/BranchOpenStatus'
 *                 message:
 *                   type: string
 *                   example: "Branch status retrieved successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
