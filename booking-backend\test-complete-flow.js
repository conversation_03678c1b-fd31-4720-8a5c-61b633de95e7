const axios = require('axios');

async function testCompletePublicBookingFlow() {
  console.log('🚀 Testing Complete Public Booking Flow\n');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Create a form first
    console.log('\n1️⃣ Creating a test form...');
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginRes.data.data.token;
    console.log('✅ Login successful');
    
    // Create form
    const timestamp = Date.now();
    const formData = {
      name: `Public Test Form - ${timestamp}`,
      serviceId: 9,
      branchId: 5,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: 'Welcome to our spa booking!'
      }
    };
    
    const createRes = await axios.post('http://localhost:3000/api/forms', formData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const form = createRes.data.data;
    const slug = form.slug;
    console.log(`✅ Form created with slug: ${slug}`);
    console.log(`🌐 Public URL: ${form.publicUrl}`);
    
    // Step 2: Test getting form by slug (public API)
    console.log('\n2️⃣ Testing public form retrieval...');
    const publicFormRes = await axios.get(`http://localhost:3000/api/public/forms/${slug}`);
    
    if (publicFormRes.data.success) {
      console.log('✅ Public form API works');
      console.log(`   Form: ${publicFormRes.data.data.name}`);
      console.log(`   Service: ${publicFormRes.data.data.service.name}`);
      console.log(`   Branch: ${publicFormRes.data.data.branch.name}`);
    } else {
      console.log('❌ Public form API failed');
      return;
    }
    
    // Step 3: Test booking submission
    console.log('\n3️⃣ Testing booking submission...');
    const bookingData = {
      formSlug: slug,
      customerName: 'John Customer',
      phoneNumber: '0123456789',
      emailAddress: `customer${timestamp}@test.com`,
      preferredDate: '2025-06-20',
      preferredTime: '10:00',
      specialRequests: 'This is a test booking from the public form'
    };
    
    const bookingRes = await axios.post('http://localhost:3000/api/public/bookings', bookingData);
    
    if (bookingRes.data.success) {
      console.log('✅ Booking submitted successfully!');
      const booking = bookingRes.data.data;
      console.log(`   Booking Code: ${booking.bookingCode}`);
      console.log(`   Customer: ${booking.customer.fullName}`);
      console.log(`   Service: ${booking.service.name}`);
      console.log(`   Date: ${booking.bookingDate}`);
      console.log(`   Time: ${booking.startTime} - ${booking.endTime}`);
      console.log(`   Status: ${booking.status}`);
      
      // Step 4: Final summary
      console.log('\n' + '=' .repeat(60));
      console.log('🎉 COMPLETE PUBLIC BOOKING FLOW TEST RESULTS');
      console.log('=' .repeat(60));
      console.log('✅ Form Creation: WORKING');
      console.log('✅ Public URL Generation: WORKING');
      console.log('✅ Public Form API: WORKING');
      console.log('✅ Public Booking API: WORKING');
      console.log('✅ Customer Auto-Creation: WORKING');
      console.log('✅ Booking Code Generation: WORKING');
      
      console.log('\n📋 FRONTEND INTEGRATION:');
      console.log(`   Visit: http://localhost:4000/book/${slug}`);
      console.log('   This URL should now work and allow customers to book!');
      
      console.log('\n🎯 NEXT STEPS:');
      console.log('   1. Start frontend: npm run dev in formBooker-frontend');
      console.log('   2. Visit the public URL to test the UI');
      console.log('   3. Submit a booking through the web interface');
      
    } else {
      console.log('❌ Booking submission failed:', bookingRes.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testCompletePublicBookingFlow();
