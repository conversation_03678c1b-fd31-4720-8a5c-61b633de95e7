/**
 * Customers Swagger Documentation
 * API documentation for customer management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       required:
 *         - userId
 *         - customerCode
 *         - membershipLevel
 *       properties:
 *         id:
 *           type: integer
 *           description: Customer ID
 *         userId:
 *           type: integer
 *           description: Reference to User table
 *         customerCode:
 *           type: string
 *           description: Unique customer code
 *           example: "CUST001"
 *         membershipLevel:
 *           type: string
 *           enum: [bronze, silver, gold, platinum, diamond]
 *           description: Customer membership level
 *           example: "gold"
 *         membershipStartDate:
 *           type: string
 *           format: date
 *           description: Membership start date
 *           example: "2023-01-15"
 *         membershipExpiryDate:
 *           type: string
 *           format: date
 *           description: Membership expiry date
 *           example: "2024-01-15"
 *         loyaltyPoints:
 *           type: integer
 *           description: Accumulated loyalty points
 *           example: 1250
 *         totalSpent:
 *           type: number
 *           format: decimal
 *           description: Total amount spent in VND
 *           example: 15000000
 *         totalBookings:
 *           type: integer
 *           description: Total number of bookings
 *           example: 25
 *         completedBookings:
 *           type: integer
 *           description: Number of completed bookings
 *           example: 22
 *         cancelledBookings:
 *           type: integer
 *           description: Number of cancelled bookings
 *           example: 2
 *         noShowBookings:
 *           type: integer
 *           description: Number of no-show bookings
 *           example: 1
 *         preferredBranchId:
 *           type: integer
 *           description: Preferred branch ID
 *           example: 1
 *         preferredServices:
 *           type: array
 *           items:
 *             type: integer
 *           description: Array of preferred service IDs
 *           example: [1, 3, 5]
 *         preferredEmployees:
 *           type: array
 *           items:
 *             type: integer
 *           description: Array of preferred employee IDs
 *           example: [2, 4]
 *         allergies:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of known allergies
 *           example: ["nuts", "shellfish", "latex"]
 *         medicalConditions:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of medical conditions
 *           example: ["hypertension", "diabetes"]
 *         skinType:
 *           type: string
 *           enum: [normal, dry, oily, combination, sensitive]
 *           description: Customer skin type
 *           example: "combination"
 *         preferences:
 *           type: object
 *           description: Customer preferences
 *           example:
 *             music: "classical"
 *             temperature: "warm"
 *             pressure: "medium"
 *         emergencyContact:
 *           type: object
 *           description: Emergency contact information
 *           example:
 *             name: "Jane Doe"
 *             relationship: "Sister"
 *             phone: "**********"
 *         notes:
 *           type: string
 *           description: Additional notes about customer
 *           example: "Prefers afternoon appointments"
 *         referralCode:
 *           type: string
 *           description: Customer referral code
 *           example: "REFCUST001123456"
 *         referredBy:
 *           type: integer
 *           description: Referred by customer ID
 *           example: 5
 *         referralCount:
 *           type: integer
 *           description: Number of successful referrals
 *           example: 3
 *         lastVisitDate:
 *           type: string
 *           format: date
 *           description: Last visit date
 *           example: "2023-12-15"
 *         averageRating:
 *           type: number
 *           format: decimal
 *           description: Average rating given by customer
 *           example: 4.5
 *         isVip:
 *           type: boolean
 *           description: VIP customer status
 *           example: true
 *         status:
 *           type: string
 *           enum: [active, inactive, suspended, blacklisted]
 *           description: Customer status
 *           example: "active"
 *         user:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *             phone:
 *               type: string
 *             gender:
 *               type: string
 *             dateOfBirth:
 *               type: string
 *               format: date
 *         preferredBranch:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *         referrer:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             customerCode:
 *               type: string
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     CustomerCreate:
 *       type: object
 *       required:
 *         - userId
 *         - customerCode
 *       properties:
 *         userId:
 *           type: integer
 *           example: 10
 *         customerCode:
 *           type: string
 *           example: "CUST001"
 *         membershipLevel:
 *           type: string
 *           enum: [bronze, silver, gold, platinum, diamond]
 *           example: "bronze"
 *         membershipStartDate:
 *           type: string
 *           format: date
 *           example: "2023-01-15"
 *         membershipExpiryDate:
 *           type: string
 *           format: date
 *           example: "2024-01-15"
 *         loyaltyPoints:
 *           type: integer
 *           example: 0
 *         preferredBranchId:
 *           type: integer
 *           example: 1
 *         preferredServices:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 3]
 *         preferredEmployees:
 *           type: array
 *           items:
 *             type: integer
 *           example: [2]
 *         allergies:
 *           type: array
 *           items:
 *             type: string
 *           example: ["nuts"]
 *         medicalConditions:
 *           type: array
 *           items:
 *             type: string
 *           example: []
 *         skinType:
 *           type: string
 *           enum: [normal, dry, oily, combination, sensitive]
 *           example: "normal"
 *         preferences:
 *           type: object
 *           example:
 *             music: "relaxing"
 *             temperature: "moderate"
 *         emergencyContact:
 *           type: object
 *           example:
 *             name: "John Doe"
 *             relationship: "Spouse"
 *             phone: "**********"
 *         notes:
 *           type: string
 *           example: "First time customer"
 *         referralCode:
 *           type: string
 *           example: "REFCUST001123456"
 *         referredBy:
 *           type: integer
 *           example: 5
 *     
 *     CustomerUpdate:
 *       type: object
 *       properties:
 *         customerCode:
 *           type: string
 *           example: "CUST002"
 *         membershipLevel:
 *           type: string
 *           enum: [bronze, silver, gold, platinum, diamond]
 *           example: "silver"
 *         membershipStartDate:
 *           type: string
 *           format: date
 *           example: "2023-01-15"
 *         membershipExpiryDate:
 *           type: string
 *           format: date
 *           example: "2024-01-15"
 *         loyaltyPoints:
 *           type: integer
 *           example: 500
 *         totalSpent:
 *           type: number
 *           format: decimal
 *           example: 5000000
 *         preferredBranchId:
 *           type: integer
 *           example: 2
 *         preferredServices:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 3, 5]
 *         preferredEmployees:
 *           type: array
 *           items:
 *             type: integer
 *           example: [2, 4]
 *         allergies:
 *           type: array
 *           items:
 *             type: string
 *           example: ["nuts", "shellfish"]
 *         medicalConditions:
 *           type: array
 *           items:
 *             type: string
 *           example: ["hypertension"]
 *         skinType:
 *           type: string
 *           enum: [normal, dry, oily, combination, sensitive]
 *           example: "sensitive"
 *         preferences:
 *           type: object
 *           example:
 *             music: "classical"
 *             temperature: "cool"
 *             pressure: "light"
 *         emergencyContact:
 *           type: object
 *           example:
 *             name: "Jane Smith"
 *             relationship: "Mother"
 *             phone: "**********"
 *         notes:
 *           type: string
 *           example: "Regular customer, prefers weekend appointments"
 *         referralCode:
 *           type: string
 *           example: "REFCUST002789012"
 *         isVip:
 *           type: boolean
 *           example: true
 *         status:
 *           type: string
 *           enum: [active, inactive, suspended, blacklisted]
 *           example: "active"
 *     
 *     CustomerStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of active customers
 *           example: 1250
 *         vip:
 *           type: integer
 *           description: Number of VIP customers
 *           example: 85
 *         newThisMonth:
 *           type: integer
 *           description: New customers this month
 *           example: 45
 *         totalRevenue:
 *           type: number
 *           format: decimal
 *           description: Total revenue from all customers
 *           example: 2500000000
 *         averageSpending:
 *           type: number
 *           format: decimal
 *           description: Average spending per customer
 *           example: 2000000
 *         totalLoyaltyPoints:
 *           type: integer
 *           description: Total loyalty points in circulation
 *           example: 125000
 *         byMembership:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "bronze": 650
 *             "silver": 350
 *             "gold": 180
 *             "platinum": 55
 *             "diamond": 15
 */

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Get all customers with pagination and filters
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - name: membershipLevel
 *         in: query
 *         schema:
 *           type: string
 *           enum: [bronze, silver, gold, platinum, diamond]
 *         description: Filter by membership level
 *       - name: status
 *         in: query
 *         schema:
 *           type: string
 *           enum: [active, inactive, suspended]
 *         description: Filter by customer status
 *       - name: isVip
 *         in: query
 *         schema:
 *           type: boolean
 *         description: Filter by VIP status
 *       - $ref: '#/components/parameters/SearchParam'
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Customer'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Customers retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create new customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerCreate'
 *     responses:
 *       201:
 *         description: Customer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *                 message:
 *                   type: string
 *                   example: "Customer created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/customers/stats:
 *   get:
 *     summary: Get customer statistics
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Customer statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/CustomerStats'
 *                 message:
 *                   type: string
 *                   example: "Customer statistics retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *                 message:
 *                   type: string
 *                   example: "Customer retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerUpdate'
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *                 message:
 *                   type: string
 *                   example: "Customer updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
