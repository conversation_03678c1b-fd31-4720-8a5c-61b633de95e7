/**
 * Test public booking endpoint to reproduce 500 error
 */

const axios = require('axios');

async function testPublicBooking() {
  console.log('🧪 Testing public booking endpoint...');
  
  try {    const bookingData = {
      formSlug: 'test-public-form', // sử dụng slug hợp lệ đã tạo
      customerName: '<PERSON>',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-20',
      preferredTime: '10:00',
      specialRequests: 'Test booking from script'
    };

    console.log('📝 Sending booking data:', JSON.stringify(bookingData, null, 2));

    const response = await axios.post('http://localhost:3000/api/public/bookings', bookingData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Booking created successfully!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Error occurred:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Status Text:', error.response.statusText);
      console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
    
    console.error('Full error:', error);
  }
}

// Run test
testPublicBooking();
