/**
 * Simple Registration Test
 * Quick test to verify registration fix
 */

const axios = require('axios');

const API_URL = 'http://localhost:3000/api/auth/register';

async function testRegistration() {
  try {
    console.log('🧪 Testing Registration with Phone Field...');
    
    const timestamp = Date.now();
    const testUser = {
      name: 'Test Business',
      email: `test_${timestamp}@example.com`,
      phone: '0123456789',
      password: 'password123',
      role: 'customer'
    };
    
    console.log('📤 Sending registration request...');
    console.log('   Data:', JSON.stringify(testUser, null, 2));
    
    const response = await axios.post(API_URL, testUser);
    
    console.log('✅ Registration successful!');
    console.log(`   Status: ${response.status}`);
    console.log(`   User ID: ${response.data.data.user.id}`);
    console.log(`   Email: ${response.data.data.user.email}`);
    console.log(`   Phone: ${response.data.data.user.phone}`);
    console.log(`   Has Access Token: ${!!response.data.data.accessToken}`);
    
    return true;
  } catch (error) {
    console.log('❌ Registration failed');
    console.log(`   Status: ${error.response?.status}`);
    console.log(`   Error Code: ${error.response?.data?.error?.code}`);
    console.log(`   Error Message: ${error.response?.data?.error?.message}`);
    
    if (error.response?.data?.error?.details) {
      console.log('   Validation Details:');
      error.response.data.error.details.forEach(detail => {
        console.log(`     - ${detail.field}: ${detail.message}`);
      });
    }
    
    return false;
  }
}

// Test rate limiting
async function testRateLimiting() {
  try {
    console.log('\n🧪 Testing Rate Limiting (should allow multiple in dev)...');
    
    let successCount = 0;
    let rateLimitCount = 0;
    
    for (let i = 0; i < 5; i++) {
      try {
        const timestamp = Date.now() + i;
        const testUser = {
          name: `Test Business ${i}`,
          email: `test_rate_${timestamp}@example.com`,
          phone: `012345678${i}`,
          password: 'password123',
          role: 'customer'
        };
        
        const response = await axios.post(API_URL, testUser);
        successCount++;
        console.log(`   Request ${i + 1}: ✅ Success (${response.status})`);
      } catch (error) {
        if (error.response?.status === 429) {
          rateLimitCount++;
          console.log(`   Request ${i + 1}: ⏱️  Rate limited (${error.response.status})`);
        } else {
          console.log(`   Request ${i + 1}: ❌ Failed (${error.response?.status}) - ${error.response?.data?.error?.message}`);
        }
      }
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log(`\n📊 Rate Limiting Results:`);
    console.log(`   Successful: ${successCount}/5`);
    console.log(`   Rate Limited: ${rateLimitCount}/5`);
    console.log(`   Other Errors: ${5 - successCount - rateLimitCount}/5`);
    
    // In development, we should allow more registrations
    return successCount >= 3;
  } catch (error) {
    console.log('❌ Rate limiting test failed');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Simple Registration Tests...\n');
  
  const test1 = await testRegistration();
  const test2 = await testRateLimiting();
  
  console.log('\n📋 Summary:');
  console.log(`Registration with Phone: ${test1 ? '✅' : '❌'}`);
  console.log(`Rate Limiting (Dev): ${test2 ? '✅' : '❌'}`);
  
  if (test1 && test2) {
    console.log('\n🎉 All tests passed! Registration fix is working.');
  } else {
    console.log('\n🔧 Some tests failed. Check the issues above.');
  }
}

runTests().catch(console.error);
