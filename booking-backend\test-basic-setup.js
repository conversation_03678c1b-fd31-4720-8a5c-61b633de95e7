const express = require('express');
const bcrypt = require('bcrypt');

console.log('🔧 Testing bcrypt module...');

async function testBcrypt() {
  try {
    const password = 'test123';
    const hashedPassword = await bcrypt.hash(password, 10);
    console.log('✅ bcrypt hash successful:', hashedPassword);
    
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('✅ bcrypt compare successful:', isValid);
  } catch (error) {
    console.error('❌ bcrypt error:', error);
  }
}

testBcrypt().then(() => {
  console.log('🧪 Testing simple server...');
  
  const app = express();
  app.use(express.json());
  
  app.get('/test', (req, res) => {
    res.json({ success: true, message: 'Server is working' });
  });
  
  const PORT = 3002; // Use different port to avoid conflicts
  
  app.listen(PORT, () => {
    console.log(`✅ Test server running on port ${PORT}`);
    
    // Test the endpoint
    setTimeout(() => {
      const http = require('http');
      const options = {
        hostname: 'localhost',
        port: PORT,
        path: '/test',
        method: 'GET'
      };
      
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          console.log('✅ Test endpoint response:', data);
          process.exit(0);
        });
      });
      
      req.on('error', (error) => {
        console.error('❌ Test request error:', error);
        process.exit(1);
      });
      
      req.end();
    }, 1000);
  });
});
