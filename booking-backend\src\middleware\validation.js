/**
 * Validation Middleware
 * Request validation using express-validator
 */

const { validationResult } = require('express-validator');
const { validationErrorResponse, formatValidationErrors } = require('../utils/response');
const { sanitizeInput } = require('../utils/validation');
const logger = require('../utils/logger');

/**
 * Main validation middleware
 * Checks validation results and returns formatted errors
 */
const validateRequest = (req, res, next) => {
  // Validate req and res parameters
  if (!req || !res || typeof res.status !== 'function') {
    const error = new Error('req or res is not defined in validateRequest');
    console.error('validateRequest error: req or res is undefined');
    if (next) next(error);
    return;
  }

  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors = formatValidationErrors(errors.array());

    logger.warn('Validation failed', {
      url: req.originalUrl,
      method: req.method,
      errors: formattedErrors,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userId: req.user ? req.user.id : null
    });

    return validationErrorResponse(res, formattedErrors);
  }

  next();
};

/**
 * Sanitize request data middleware
 * Cleans and sanitizes input data
 */
const sanitizeRequest = (req, res, next) => {
  try {
    // Sanitize body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeInput(req.body);
    }
    
    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeInput(req.query);
    }
    
    // Sanitize params
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeInput(req.params);
    }
    
    next();
  } catch (error) {
    logger.error('Request sanitization failed:', error);
    next(error);
  }
};

/**
 * Validate pagination parameters
 */
const validatePagination = (req, res, next) => {
  const { page = 1, limit = 10 } = req.query;
  
  // Convert to numbers and validate
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  
  if (isNaN(pageNum) || pageNum < 1) {
    // Validate res parameter before using
    if (!res || typeof res.status !== 'function') {
      const error = new Error('res is not defined in validatePagination');
      return next(error);
    }
    return validationErrorResponse(res, [{
      field: 'page',
      message: 'Page must be a positive integer',
      value: page
    }]);
  }
  
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    // Validate res parameter before using
    if (!res || typeof res.status !== 'function') {
      const error = new Error('res is not defined in validatePagination limit check');
      return next(error);
    }
    return validationErrorResponse(res, [{
      field: 'limit',
      message: 'Limit must be between 1 and 100',
      value: limit
    }]);
  }
  
  // Set validated values
  req.query.page = pageNum;
  req.query.limit = limitNum;
  
  next();
};

/**
 * Validate sort parameters
 */
const validateSort = (allowedFields = []) => {
  return (req, res, next) => {
    const { sort } = req.query;
    
    if (!sort) {
      return next();
    }
    
    const sortPairs = sort.split(',');
    const validatedSort = [];
    const errors = [];
    
    for (const pair of sortPairs) {
      const [field, direction = 'asc'] = pair.split(':');
      
      // Validate field
      if (allowedFields.length > 0 && !allowedFields.includes(field)) {
        errors.push({
          field: 'sort',
          message: `Invalid sort field: ${field}. Allowed fields: ${allowedFields.join(', ')}`,
          value: pair
        });
        continue;
      }
      
      // Validate direction
      if (!['asc', 'desc'].includes(direction.toLowerCase())) {
        errors.push({
          field: 'sort',
          message: `Invalid sort direction: ${direction}. Must be 'asc' or 'desc'`,
          value: pair
        });
        continue;
      }
      
      validatedSort.push(`${field}:${direction.toLowerCase()}`);
    }
    
    if (errors.length > 0) {
      return validationErrorResponse(res, errors);
    }
    
    req.query.sort = validatedSort.join(',');
    next();
  };
};

/**
 * Validate date range parameters
 */
const validateDateRange = (startField = 'startDate', endField = 'endDate') => {
  return (req, res, next) => {
    const startDate = req.query[startField];
    const endDate = req.query[endField];
    const errors = [];
    
    // Validate start date format
    if (startDate && !isValidDate(startDate)) {
      errors.push({
        field: startField,
        message: 'Invalid date format. Use YYYY-MM-DD',
        value: startDate
      });
    }
    
    // Validate end date format
    if (endDate && !isValidDate(endDate)) {
      errors.push({
        field: endField,
        message: 'Invalid date format. Use YYYY-MM-DD',
        value: endDate
      });
    }
    
    // Validate date range
    if (startDate && endDate && isValidDate(startDate) && isValidDate(endDate)) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start > end) {
        errors.push({
          field: endField,
          message: 'End date must be after start date',
          value: endDate
        });
      }
    }
    
    if (errors.length > 0) {
      return validationErrorResponse(res, errors);
    }
    
    next();
  };
};

/**
 * Validate file upload
 */
const validateFileUpload = (options = {}) => {
  const {
    required = false,
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
    fieldName = 'file'
  } = options;
  
  return (req, res, next) => {
    const file = req.file || req.files?.[fieldName];
    const errors = [];
    
    // Check if file is required
    if (required && !file) {
      errors.push({
        field: fieldName,
        message: 'File is required',
        value: null
      });
    }
    
    if (file) {
      // Check file size
      if (file.size > maxSize) {
        errors.push({
          field: fieldName,
          message: `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`,
          value: file.size
        });
      }
      
      // Check file type
      if (!allowedTypes.includes(file.mimetype)) {
        errors.push({
          field: fieldName,
          message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`,
          value: file.mimetype
        });
      }
    }
    
    if (errors.length > 0) {
      return validationErrorResponse(res, errors);
    }
    
    next();
  };
};

/**
 * Validate JSON body
 */
const validateJSON = (req, res, next) => {
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    const contentType = req.get('Content-Type');
    
    if (contentType && contentType.includes('application/json')) {
      if (!req.body || Object.keys(req.body).length === 0) {
        return validationErrorResponse(res, [{
          field: 'body',
          message: 'Request body is required',
          value: req.body
        }]);
      }
    }
  }
  
  next();
};

/**
 * Validate required fields
 */
const validateRequiredFields = (fields = []) => {
  return (req, res, next) => {
    const errors = [];
    
    fields.forEach(field => {
      const value = req.body[field];
      
      if (value === undefined || value === null || value === '') {
        errors.push({
          field,
          message: `${field} is required`,
          value
        });
      }
    });
    
    if (errors.length > 0) {
      return validationErrorResponse(res, errors);
    }
    
    next();
  };
};

/**
 * Validate ID parameter
 */
const validateIdParam = (paramName = 'id') => {
  return (req, res, next) => {
    const id = req.params[paramName];

    if (!id || isNaN(parseInt(id)) || parseInt(id) < 1) {
      return validationErrorResponse(res, [{
        field: paramName,
        message: 'Valid ID required',
        value: id
      }]);
    }

    req.params[paramName] = parseInt(id);
    next();
  };
};

/**
 * Helper function to validate date format
 */
const isValidDate = (dateString) => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
};

/**
 * Custom validation for business rules
 */
const validateBusinessRules = {
  // Validate booking date is in the future
  futureDate: (field = 'bookingDate') => {
    return (req, res, next) => {
      const dateValue = req.body[field];
      
      if (dateValue) {
        const inputDate = new Date(dateValue);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (inputDate < today) {
          return validationErrorResponse(res, [{
            field,
            message: 'Date must be in the future',
            value: dateValue
          }]);
        }
      }
      
      next();
    };
  },
  
  // Validate time is within business hours
  businessHours: (field = 'startTime') => {
    return (req, res, next) => {
      const timeValue = req.body[field];
      
      if (timeValue) {
        const [hours, minutes] = timeValue.split(':').map(Number);
        const timeInMinutes = hours * 60 + minutes;
        const startTime = 8 * 60; // 8:00 AM
        const endTime = 20 * 60; // 8:00 PM
        
        if (timeInMinutes < startTime || timeInMinutes > endTime) {
          return validationErrorResponse(res, [{
            field,
            message: 'Time must be within business hours (8:00 AM - 8:00 PM)',
            value: timeValue
          }]);
        }
      }
      
      next();
    };
  }
};

module.exports = {
  validateRequest,
  sanitizeRequest,
  validatePagination,
  validateSort,
  validateDateRange,
  validateFileUpload,
  validateJSON,
  validateRequiredFields,
  validateIdParam,
  validateBusinessRules
};
