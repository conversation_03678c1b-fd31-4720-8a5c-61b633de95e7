/**
 * Users Controller
 * HTTP request handlers for user management
 */

const UsersService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  paginatedResponse,
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const { getPaginationParams, buildSortOrder } = require('../../utils/pagination');
const logger = require('../../utils/logger');

class UsersController {
  /**
   * Get all users with pagination and filters
   * GET /api/users
   */
  async getUsers(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { page, limit } = getPaginationParams(req.query);
      const { role, isActive, search, branchId, sort } = req.query;

      const filters = { role, isActive, search, branchId };
      const pagination = { 
        page, 
        limit,
        order: buildSortOrder(sort, ['name', 'email', 'role', 'createdAt'], [['createdAt', 'DESC']])
      };

      const result = await UsersService.getUsers(filters, pagination);

      logger.info('Users retrieved successfully', {
        userId: req.user.id,
        filters,
        pagination: { page, limit },
        resultCount: result.data.length
      });

      return paginatedResponse(res, result.data, result.pagination, 'Users retrieved successfully');

    } catch (error) {
      logger.error('Get users controller error:', error);
      next(error);
    }
  }

  /**
   * Get current user profile
   * GET /api/users/profile
   */
  async getCurrentUserProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const user = await UsersService.getUserById(userId);

      logger.info('Current user profile retrieved successfully', {
        userId: userId
      });

      return successResponse(res, user, 'Profile retrieved successfully');
    } catch (error) {
      logger.error('Get current user profile failed', {
        userId: req.user?.id,
        error: error.message,
        stack: error.stack
      });
      next(error);
    }
  }

  /**
   * Get user by ID
   * GET /api/users/:id
   */
  async getUserById(req, res, next) {
    try {
      const { id } = req.params;
      const user = await UsersService.getUserById(parseInt(id));

      logger.info('User retrieved successfully', {
        userId: req.user.id,
        targetUserId: id
      });

      return successResponse(res, user, 'User retrieved successfully');

    } catch (error) {
      logger.error('Get user by ID controller error:', error);
      next(error);
    }
  }

  /**
   * Create new user
   * POST /api/users
   */
  async createUser(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const userData = req.body;
      const user = await UsersService.createUser(userData);

      logger.info('User created successfully', {
        userId: req.user.id,
        createdUserId: user.id,
        createdUserEmail: user.email,
        createdUserRole: user.role
      });

      return createdResponse(res, user, 'User created successfully');

    } catch (error) {
      logger.error('Create user controller error:', error);
      next(error);
    }
  }

  /**
   * Update user
   * PUT /api/users/:id
   */
  async updateUser(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const user = await UsersService.updateUser(parseInt(id), updateData);

      logger.info('User updated successfully', {
        userId: req.user.id,
        updatedUserId: id,
        updatedFields: Object.keys(updateData)
      });

      return successResponse(res, user, 'User updated successfully');

    } catch (error) {
      logger.error('Update user controller error:', error);
      next(error);
    }
  }

  /**
   * Delete user
   * DELETE /api/users/:id
   */
  async deleteUser(req, res, next) {
    try {
      const { id } = req.params;
      
      // Prevent self-deletion
      if (parseInt(id) === req.user.id) {
        return errorResponse(res, 'Cannot delete your own account', 400, 'SELF_DELETE_NOT_ALLOWED');
      }

      const result = await UsersService.deleteUser(parseInt(id));

      logger.info('User deleted successfully', {
        userId: req.user.id,
        deletedUserId: id
      });

      return successResponse(res, result, 'User deleted successfully');

    } catch (error) {
      logger.error('Delete user controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle user status (activate/deactivate)
   * PATCH /api/users/:id/status
   */
  async toggleUserStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isActive } = req.body;

      // Prevent self-deactivation
      if (parseInt(id) === req.user.id && !isActive) {
        return errorResponse(res, 'Cannot deactivate your own account', 400, 'SELF_DEACTIVATE_NOT_ALLOWED');
      }

      const user = await UsersService.toggleUserStatus(parseInt(id), isActive);

      logger.info('User status changed successfully', {
        userId: req.user.id,
        targetUserId: id,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return successResponse(res, user, `User ${isActive ? 'activated' : 'deactivated'} successfully`);

    } catch (error) {
      logger.error('Toggle user status controller error:', error);
      next(error);
    }
  }

  /**
   * Get users by role
   * GET /api/users/role/:role
   */
  async getUsersByRole(req, res, next) {
    try {
      const { role } = req.params;
      const { limit = 50 } = req.query;

      const users = await UsersService.getUsersByRole(role, { 
        limit: parseInt(limit),
        order: [['name', 'ASC']]
      });

      logger.info('Users by role retrieved successfully', {
        userId: req.user.id,
        role,
        resultCount: users.length
      });

      return successResponse(res, users, `${role} users retrieved successfully`);

    } catch (error) {
      logger.error('Get users by role controller error:', error);
      next(error);
    }
  }

  /**
   * Search users
   * GET /api/users/search
   */
  async searchUsers(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { q: searchTerm, limit = 20 } = req.query;

      const users = await UsersService.searchUsers(searchTerm, {
        limit: parseInt(limit),
        order: [['name', 'ASC']]
      });

      logger.info('User search completed', {
        userId: req.user.id,
        searchTerm,
        resultCount: users.length
      });

      return successResponse(res, users, 'Search completed successfully');

    } catch (error) {
      logger.error('Search users controller error:', error);
      next(error);
    }
  }

  /**
   * Get user statistics
   * GET /api/users/stats
   */
  async getUserStats(req, res, next) {
    try {
      const stats = await UsersService.getUserStats();

      logger.info('User stats retrieved successfully', {
        userId: req.user.id
      });

      return successResponse(res, stats, 'User statistics retrieved successfully');

    } catch (error) {
      logger.error('Get user stats controller error:', error);
      next(error);
    }
  }

  /**
   * Reset user password
   * POST /api/users/:id/reset-password
   */
  async resetUserPassword(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { newPassword } = req.body;

      const result = await UsersService.resetUserPassword(parseInt(id), newPassword);

      logger.info('User password reset by admin', {
        userId: req.user.id,
        targetUserId: id
      });

      return successResponse(res, result, 'Password reset successfully');

    } catch (error) {
      logger.error('Reset user password controller error:', error);
      next(error);
    }
  }

  /**
   * Unlock user account
   * POST /api/users/:id/unlock
   */
  async unlockUser(req, res, next) {
    try {
      const { id } = req.params;

      const result = await UsersService.unlockUser(parseInt(id));

      logger.info('User account unlocked', {
        userId: req.user.id,
        targetUserId: id
      });

      return successResponse(res, result, 'User account unlocked successfully');

    } catch (error) {
      logger.error('Unlock user controller error:', error);
      next(error);
    }
  }
}

module.exports = new UsersController();
