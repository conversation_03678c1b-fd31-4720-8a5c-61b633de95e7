/**
 * Next.js Middleware for Authentication
 * Handles route protection at the server level
 */

import { NextResponse } from 'next/server';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/forms',
  '/bookings',
  '/services',
  '/branches',
  '/settings'
];

// Define public routes that should redirect authenticated users
const publicRoutes = [
  '/login',
  '/register'
];

// Define routes that are always accessible
const alwaysAccessibleRoutes = [
  '/',
  '/api',
  '/_next',
  '/favicon.ico',
  '/apple-touch-icon.png',
  '/favicon-32x32.png',
  '/favicon-16x16.png',
  '/site.webmanifest',
  '/book' // Public booking forms
];

/**
 * Check if a path matches any of the route patterns
 */
function matchesRoute(pathname, routes) {
  return routes.some(route => {
    if (route === pathname) return true;
    if (route.endsWith('*')) {
      return pathname.startsWith(route.slice(0, -1));
    }
    return pathname.startsWith(route);
  });
}

/**
 * Check if user has valid authentication tokens
 * Note: Since middleware runs server-side, we can't access localStorage
 * We'll rely on client-side authentication checks for now
 */
function isAuthenticated(request) {
  // Check for token in cookies (if we implement cookie storage)
  const accessToken = request.cookies.get('accessToken')?.value;

  if (!accessToken) {
    // No token in cookies, let client-side handle authentication
    // This allows the app to work with localStorage tokens
    return null; // Unknown authentication state
  }

  try {
    // Basic JWT format validation
    const parts = accessToken.split('.');
    if (parts.length !== 3) return false;

    // Decode payload to check expiry
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired (with 30 second buffer)
    return payload.exp && payload.exp > (currentTime + 30);
  } catch (error) {
    console.error('Token validation error in middleware:', error);
    return false;
  }
}

export function middleware(request) {
  const { pathname } = request.nextUrl;

  // Skip middleware for always accessible routes
  if (matchesRoute(pathname, alwaysAccessibleRoutes)) {
    return NextResponse.next();
  }

  const authState = isAuthenticated(request);

  // Handle protected routes
  if (matchesRoute(pathname, protectedRoutes)) {
    // If we can't determine auth state (localStorage case), let client handle it
    if (authState === null) {
      return NextResponse.next();
    }

    if (!authState) {
      // Redirect to login with return URL
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
    return NextResponse.next();
  }

  // Handle public routes (login, register)
  if (matchesRoute(pathname, publicRoutes)) {
    // If we can't determine auth state, let client handle it
    if (authState === null) {
      return NextResponse.next();
    }

    if (authState) {
      // Redirect authenticated users to dashboard
      const dashboardUrl = new URL('/dashboard', request.url);
      return NextResponse.redirect(dashboardUrl);
    }
    return NextResponse.next();
  }

  // For all other routes, allow access
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
