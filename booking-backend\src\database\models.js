/**
 * Model Loader and Association Setup
 * Loads all models and sets up their associations
 */

const { sequelize } = require('./connection');

// Import all models safely
let User, Branch, Service, Employee, Customer, Booking, Payment, Notification, Forms;

try {
  User = require('../modules/auth/model');
  console.log('✅ User model loaded');
} catch (error) {
  console.error('❌ User model failed:', error.message);
}

try {
  Branch = require('../modules/branches/model');
  console.log('✅ Branch model loaded');
} catch (error) {
  console.error('❌ Branch model failed:', error.message);
}

try {
  Service = require('../modules/services/model');
  console.log('✅ Service model loaded');
} catch (error) {
  console.error('❌ Service model failed:', error.message);
}

try {
  Employee = require('../modules/employees/model');
  console.log('✅ Employee model loaded');
} catch (error) {
  console.error('❌ Employee model failed:', error.message);
}

try {
  Customer = require('../modules/customers/model');
  console.log('✅ Customer model loaded');
} catch (error) {
  console.error('❌ Customer model failed:', error.message);
}

try {
  Booking = require('../modules/bookings/model');
  console.log('✅ Booking model loaded');
} catch (error) {
  console.error('❌ Booking model failed:', error.message);
}

try {
  Payment = require('../modules/payments/model');
  console.log('✅ Payment model loaded');
} catch (error) {
  console.error('❌ Payment model failed:', error.message);
}

try {
  Notification = require('../modules/notifications/model');
  console.log('✅ Notification model loaded');
} catch (error) {
  console.error('❌ Notification model failed:', error.message);
}

try {
  Forms = require('../modules/forms/model');
  console.log('✅ Forms model loaded');
} catch (error) {
  console.error('❌ Forms model failed:', error.message);
}

// Create models object with only successfully loaded models
const models = {};

if (User) models.users = User;
if (Branch) models.branches = Branch;
if (Service) models.services = Service;
if (Employee) models.employees = Employee;
if (Customer) models.customers = Customer;
if (Booking) models.bookings = Booking;
if (Payment) models.payments = Payment;
if (Notification) models.notifications = Notification;
if (Forms) models.forms = Forms;

console.log(`📋 Successfully loaded ${Object.keys(models).length} models:`, Object.keys(models));

// Setup associations for each model that has an associate function
Object.keys(models).forEach(modelName => {
  const model = models[modelName];
  if (model && model.associate && typeof model.associate === 'function') {
    try {
      console.log(`Setting up associations for ${modelName}`);
      model.associate(models);
      console.log(`✅ Associations set up for ${modelName}`);
    } catch (error) {
      console.error(`❌ Failed to set up associations for ${modelName}:`, error.message);
    }
  }
});

// Add models to sequelize instance
sequelize.models = models;

// Export models and sequelize
module.exports = {
  sequelize,
  models,
  ...models
};
