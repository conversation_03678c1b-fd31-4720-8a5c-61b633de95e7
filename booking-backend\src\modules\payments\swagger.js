/**
 * Payments Swagger Documentation
 * API documentation for payment management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Payment:
 *       type: object
 *       required:
 *         - bookingId
 *         - customerId
 *         - amount
 *         - method
 *       properties:
 *         id:
 *           type: integer
 *           description: Payment ID
 *         paymentCode:
 *           type: string
 *           description: Unique payment code
 *           example: "PAY20240610001"
 *         bookingId:
 *           type: integer
 *           description: Reference to booking
 *           example: 1
 *         customerId:
 *           type: integer
 *           description: Reference to customer
 *           example: 1
 *         amount:
 *           type: number
 *           format: decimal
 *           description: Payment amount in VND
 *           example: 500000
 *         method:
 *           type: string
 *           enum: [cash, card, bank_transfer, e_wallet, credit]
 *           description: Payment method
 *           example: "card"
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled, refunded]
 *           description: Payment status
 *           example: "completed"
 *         transactionId:
 *           type: string
 *           description: External transaction ID
 *           example: "TXN123456789"
 *         description:
 *           type: string
 *           description: Payment description
 *           example: "Payment for Swedish Massage service"
 *         notes:
 *           type: string
 *           description: Additional notes
 *           example: "Customer paid in full"
 *         currency:
 *           type: string
 *           description: Currency code
 *           example: "VND"
 *         fees:
 *           type: number
 *           format: decimal
 *           description: Transaction fees
 *           example: 5000
 *         isPartialPayment:
 *           type: boolean
 *           description: Whether this is a partial payment
 *           example: false
 *         paymentDate:
 *           type: string
 *           format: date-time
 *           description: Payment date
 *         processedBy:
 *           type: integer
 *           description: Staff who processed the payment
 *           example: 5
 *         refundedBy:
 *           type: integer
 *           description: Staff who processed the refund
 *           example: null
 *         refundAmount:
 *           type: number
 *           format: decimal
 *           description: Refunded amount
 *           example: 0
 *         refundReason:
 *           type: string
 *           description: Reason for refund
 *           example: null
 *         refundDate:
 *           type: string
 *           format: date-time
 *           description: Refund date
 *           example: null
 *         metadata:
 *           type: object
 *           description: Additional payment metadata
 *           example: { "gateway": "vnpay", "reference": "REF123" }
 *         booking:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             bookingCode:
 *               type: string
 *             bookingDate:
 *               type: string
 *               format: date
 *             startTime:
 *               type: string
 *             totalAmount:
 *               type: number
 *         customer:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             customerCode:
 *               type: string
 *             user:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *                 phone:
 *                   type: string
 *         processedByUser:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *         refundedByUser:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     PaymentCreate:
 *       type: object
 *       required:
 *         - bookingId
 *         - customerId
 *         - amount
 *         - method
 *       properties:
 *         bookingId:
 *           type: integer
 *           example: 1
 *         customerId:
 *           type: integer
 *           example: 1
 *         amount:
 *           type: number
 *           format: decimal
 *           example: 500000
 *         method:
 *           type: string
 *           enum: [cash, card, bank_transfer, e_wallet, credit]
 *           example: "card"
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled, refunded]
 *           example: "pending"
 *         transactionId:
 *           type: string
 *           example: "TXN123456789"
 *         description:
 *           type: string
 *           example: "Payment for Swedish Massage service"
 *         notes:
 *           type: string
 *           example: "Customer paid in full"
 *         currency:
 *           type: string
 *           example: "VND"
 *         fees:
 *           type: number
 *           format: decimal
 *           example: 5000
 *         isPartialPayment:
 *           type: boolean
 *           example: false
 *     
 *     PaymentUpdate:
 *       type: object
 *       properties:
 *         amount:
 *           type: number
 *           format: decimal
 *           example: 550000
 *         method:
 *           type: string
 *           enum: [cash, card, bank_transfer, e_wallet, credit]
 *           example: "bank_transfer"
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled, refunded]
 *           example: "completed"
 *         transactionId:
 *           type: string
 *           example: "TXN987654321"
 *         description:
 *           type: string
 *           example: "Updated payment description"
 *         notes:
 *           type: string
 *           example: "Payment method changed"
 *     
 *     PaymentStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of payments
 *           example: 1250
 *         totalAmount:
 *           type: number
 *           format: decimal
 *           description: Total payment amount
 *           example: *********
 *         averageAmount:
 *           type: number
 *           format: decimal
 *           description: Average payment amount
 *           example: 100000
 *         byStatus:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "completed": 1100
 *             "pending": 80
 *             "failed": 45
 *             "refunded": 25
 *         byMethod:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "card": 650
 *             "cash": 400
 *             "bank_transfer": 150
 *             "e_wallet": 50
 *         monthlyRevenue:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               month:
 *                 type: string
 *               amount:
 *                 type: number
 *           example:
 *             - month: "2024-01"
 *               amount: ********
 *             - month: "2024-02"
 *               amount: ********
 */

/**
 * @swagger
 * /api/payments:
 *   get:
 *     summary: Get all payments with pagination and filtering
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - name: status
 *         in: query
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed, cancelled, refunded]
 *         description: Filter by payment status
 *       - name: method
 *         in: query
 *         schema:
 *           type: string
 *           enum: [cash, card, bank_transfer, e_wallet, credit]
 *         description: Filter by payment method
 *       - name: customerId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *       - name: bookingId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by booking ID
 *       - name: startDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter payments from this date
 *       - name: endDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter payments until this date
 *       - name: minAmount
 *         in: query
 *         schema:
 *           type: number
 *         description: Minimum payment amount
 *       - name: maxAmount
 *         in: query
 *         schema:
 *           type: number
 *         description: Maximum payment amount
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Payment'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Payments retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create a new payment
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaymentCreate'
 *     responses:
 *       201:
 *         description: Payment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *                 message:
 *                   type: string
 *                   example: "Payment created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/payments/stats:
 *   get:
 *     summary: Get payment statistics
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: startDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Statistics from this date
 *       - name: endDate
 *         in: query
 *         schema:
 *           type: string
 *           format: date
 *         description: Statistics until this date
 *       - name: method
 *         in: query
 *         schema:
 *           type: string
 *           enum: [cash, card, bank_transfer, e_wallet, credit]
 *         description: Filter by payment method
 *       - name: customerId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *     responses:
 *       200:
 *         description: Payment statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/PaymentStats'
 *                 message:
 *                   type: string
 *                   example: "Payment statistics retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/payments/{id}:
 *   get:
 *     summary: Get payment by ID
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     responses:
 *       200:
 *         description: Payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *                 message:
 *                   type: string
 *                   example: "Payment retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update payment
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaymentUpdate'
 *     responses:
 *       200:
 *         description: Payment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *                 message:
 *                   type: string
 *                   example: "Payment updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete payment (soft delete)
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     responses:
 *       200:
 *         description: Payment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/payments/{id}/process:
 *   patch:
 *     summary: Process payment (mark as completed)
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               transactionId:
 *                 type: string
 *                 example: "TXN123456789"
 *               notes:
 *                 type: string
 *                 example: "Payment processed successfully"
 *     responses:
 *       200:
 *         description: Payment processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *                 message:
 *                   type: string
 *                   example: "Payment processed successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/payments/{id}/refund:
 *   patch:
 *     summary: Refund payment
 *     tags: [Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               amount:
 *                 type: number
 *                 format: decimal
 *                 description: Refund amount (optional, defaults to full amount)
 *                 example: 250000
 *               reason:
 *                 type: string
 *                 description: Reason for refund
 *                 example: "Customer requested cancellation"
 *     responses:
 *       200:
 *         description: Payment refunded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *                 message:
 *                   type: string
 *                   example: "Payment refunded successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
