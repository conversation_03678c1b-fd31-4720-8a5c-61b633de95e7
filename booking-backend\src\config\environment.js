/**
 * Environment Configuration
 * Centralized configuration management for the application
 */

require('dotenv').config();

const config = {
  // Application Configuration
  app: {
    name: process.env.APP_NAME || 'Spa Booking System',
    version: process.env.API_VERSION || '1.0.0',
    port: parseInt(process.env.PORT) || 3000,
    url: process.env.APP_URL || 'http://localhost:3000',
    env: process.env.NODE_ENV || 'development'
  },

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    username: process.env.DB_USERNAME || 'spa_user',
    password: process.env.DB_PASSWORD || 'password123',
    database: process.env.DB_DATABASE || 'spa_booking_development',
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    
    // Connection Pool Configuration
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || 10,
      min: parseInt(process.env.DB_POOL_MIN) || 2,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE) || 10000
    },

    // Alter Table Configuration
    alter: {
      enabled: process.env.DB_ALTER_ENABLED === 'true',
      drop: process.env.DB_ALTER_DROP === 'true',
      force: process.env.DB_ALTER_FORCE === 'true'
    },

    // Sync Configuration
    sync: {
      force: process.env.DB_SYNC_FORCE === 'true',
      alter: process.env.DB_SYNC_ALTER === 'true'
    }
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'dev-jwt-secret-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret-key-change-in-production',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  // Security Configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 10,
    corsOrigin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000'],
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15 minutes
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
    filePath: process.env.LOG_FILE_PATH || './logs'
  },

  // Redis Configuration (Optional)
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0
  },

  // Email Configuration (Optional)
  email: {
    host: process.env.MAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.MAIL_PORT) || 587,
    user: process.env.MAIL_USER || '',
    pass: process.env.MAIL_PASS || '',
    from: process.env.MAIL_FROM || '"Spa Booking" <<EMAIL>>',
    secure: process.env.MAIL_SECURE === 'true'
  },

  // SMS Configuration (Optional)
  sms: {
    twilioAccountSid: process.env.TWILIO_ACCOUNT_SID || '',
    twilioAuthToken: process.env.TWILIO_AUTH_TOKEN || '',
    twilioPhoneNumber: process.env.TWILIO_PHONE_NUMBER || ''
  },

  // Payment Configuration (Optional)
  payment: {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
      currency: process.env.STRIPE_CURRENCY || 'VND'
    },
    momo: {
      partnerCode: process.env.MOMO_PARTNER_CODE || '',
      accessKey: process.env.MOMO_ACCESS_KEY || '',
      secretKey: process.env.MOMO_SECRET_KEY || '',
      endpoint: process.env.MOMO_ENDPOINT || 'https://test-payment.momo.vn'
    }
  }
};

// Validation function
const validateConfig = () => {
  const requiredFields = [
    'app.port',
    'database.host',
    'database.username',
    'database.password',
    'database.database',
    'jwt.secret'
  ];

  const missingFields = [];

  requiredFields.forEach(field => {
    const keys = field.split('.');
    let value = config;
    
    for (const key of keys) {
      value = value[key];
      if (value === undefined || value === null || value === '') {
        missingFields.push(field);
        break;
      }
    }
  });

  if (missingFields.length > 0) {
    throw new Error(`Missing required configuration fields: ${missingFields.join(', ')}`);
  }

  // Validate JWT secret in production
  if (config.app.env === 'production') {
    if (config.jwt.secret.includes('dev-') || config.jwt.secret.includes('change-in-production')) {
      throw new Error('JWT secret must be changed in production environment');
    }
  }
};

// Validate configuration on load
validateConfig();

module.exports = config;
