@echo off
echo ===================================
echo Testing Forms Functionality
echo ===================================

echo.
echo 1. Testing Environment Variables...
cd /d "d:\DUANNESTJS\booking\booking-backend"
node test-env-quick.js

echo.
echo 2. Testing Frontend Service Logic...
cd /d "d:\DUANNESTJS\booking\formBooker-frontend"  
node test-forms-service.js

echo.
echo 3. Backend Integration Test (requires server to be running)...
cd /d "d:\DUANNESTJS\booking\booking-backend"
echo Starting backend server in background...
start /b node start-server.js

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo Testing API...
node test-forms-api.js

echo.
echo ===================================
echo Test Summary Complete
echo ===================================
echo Check the output above for any issues.
echo If you see missing publicUrl or embedCode, 
echo the fixes should resolve the issue.
echo ===================================

pause
