require('dotenv').config();

console.log('Environment Variables Check:');
console.log('==========================');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('APP_PORT:', process.env.APP_PORT);
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_DATABASE:', process.env.DB_DATABASE);

// Test form URL generation
const formSlug = 'test-form-slug';
const publicUrl = `${process.env.FRONTEND_URL}/book/${formSlug}`;
const embedCode = `<iframe src="${process.env.FRONTEND_URL}/book/${formSlug}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`;

console.log('\nGenerated URLs:');
console.log('==============');
console.log('Public URL:', publicUrl);
console.log('Embed Code:', embedCode);
