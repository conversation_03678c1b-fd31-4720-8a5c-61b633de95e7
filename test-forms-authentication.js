/**
 * Test Forms Authentication Issue
 * This script tests the complete authentication flow for forms creation
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testFormsAuthentication() {
  console.log('🔍 Testing Forms Authentication Flow...\n');

  try {
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    console.log('Token:', token.substring(0, 50) + '...\n');

    // Step 2: Test forms endpoint without auth (should fail)
    console.log('2. Testing forms endpoint without authentication...');
    try {
      await axios.post(`${API_BASE}/forms`, {
        name: 'Test Form',
        serviceId: 1,
        branchId: 1
      });
    } catch (error) {
      console.log('❌ Expected error:', error.response?.data?.error?.message || error.message);
      console.log('Status:', error.response?.status, '\n');
    }

    // Step 3: Test forms endpoint with auth
    console.log('3. Testing forms endpoint with authentication...');
    try {
      const formsResponse = await axios.post(`${API_BASE}/forms`, {
        name: 'Test Form',
        serviceId: 1,
        branchId: 1
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Forms API call successful');
      console.log('Response:', formsResponse.data);
    } catch (error) {
      console.log('❌ Forms API error:', error.response?.data?.error?.message || error.message);
      console.log('Status:', error.response?.status);
      console.log('Full error:', error.response?.data);
    }

    // Step 4: Check user's services and branches
    console.log('\n4. Checking user services and branches...');
    try {
      const servicesResponse = await axios.get(`${API_BASE}/services`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('User services:', servicesResponse.data.data?.data?.length || 0);

      const branchesResponse = await axios.get(`${API_BASE}/branches`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('User branches:', branchesResponse.data.data?.data?.length || 0);

      if (servicesResponse.data.data?.data?.length > 0) {
        const service = servicesResponse.data.data.data[0];
        console.log('First service:', service.name, 'ID:', service.id);
      }

      if (branchesResponse.data.data?.data?.length > 0) {
        const branch = branchesResponse.data.data.data[0];
        console.log('First branch:', branch.name, 'ID:', branch.id);
      }

    } catch (error) {
      console.log('❌ Error checking services/branches:', error.response?.data?.error?.message || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testFormsAuthentication();
