/**
 * Authentication Service
 * Handles all authentication-related API calls
 */

import api, { endpoints, tokenManager } from '@/lib/api';

class AuthService {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @param {string} userData.email - User email
   * @param {string} userData.password - User password
   * @param {string} userData.name - User full name
   * @param {string} userData.phone - User phone number (optional)
   * @param {string} userData.role - User role (default: 'customer')
   * @returns {Promise<Object>} Registration response
   */
  async register(userData) {
    try {
      const response = await api.post(endpoints.auth.register, {
        email: userData.email,
        password: userData.password,
        name: userData.name,
        phone: userData.phone || '',
        role: userData.role || 'customer',
      });

      // Backend returns 'token' not 'accessToken'
      const { user, token: accessToken, refreshToken } = response.data.data;

      // Store tokens and user data
      tokenManager.setToken(accessToken);
      tokenManager.setRefreshToken(refreshToken);
      tokenManager.setUser(user);

      return {
        success: true,
        user,
        accessToken,
        refreshToken,
        message: response.data.message || 'Registration successful',
      };
    } catch (error) {
      console.error('Registration error:', error);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error?.message ||
                          error.response?.data?.error ||
                          'Registration failed. Please try again.';

      const validationErrors = error.response?.data?.errors || [];

      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
        code: error.response?.data?.error?.code,
      };
    }
  }

  /**
   * Login user
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise<Object>} Login response
   */
  async login(credentials) {
    try {
      const response = await api.post(endpoints.auth.login, {
        email: credentials.email,
        password: credentials.password,
      });

      // Backend returns 'token' not 'accessToken'
      const { user, token: accessToken, refreshToken } = response.data.data;

      // Store tokens and user data
      tokenManager.setToken(accessToken);
      tokenManager.setRefreshToken(refreshToken);
      tokenManager.setUser(user);

      return {
        success: true,
        user,
        accessToken,
        refreshToken,
        message: response.data.message || 'Login successful',
      };
    } catch (error) {
      console.error('Login error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          'Invalid email or password. Please try again.';
      
      const validationErrors = error.response?.data?.errors || [];
      
      throw {
        success: false,
        message: errorMessage,
        errors: validationErrors,
        status: error.response?.status,
      };
    }
  }

  /**
   * Logout user
   * @returns {Promise<Object>} Logout response
   */
  async logout() {
    try {
      // Call logout endpoint if token exists
      const token = tokenManager.getToken();
      if (token) {
        await api.post(endpoints.auth.logout);
      }
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Always clear local storage
      tokenManager.removeTokens();
    }

    return {
      success: true,
      message: 'Logged out successfully',
    };
  }

  /**
   * Get current user profile
   * @returns {Promise<Object>} User profile
   */
  async getProfile() {
    try {
      const response = await api.get(endpoints.auth.profile);
      const user = response.data.data;
      
      // Update stored user data
      tokenManager.setUser(user);
      
      return {
        success: true,
        user,
      };
    } catch (error) {
      console.error('Get profile error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to fetch user profile';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Refresh access token
   * @returns {Promise<Object>} Refresh response
   */
  async refreshToken() {
    try {
      const refreshToken = tokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post(endpoints.auth.refresh, {
        refreshToken,
      });

      // Backend might return different structure for refresh
      const responseData = response.data.data || response.data;
      const { accessToken, refreshToken: newRefreshToken, tokens } = responseData;

      // Handle different response formats
      const finalAccessToken = accessToken || tokens?.accessToken;
      const finalRefreshToken = newRefreshToken || tokens?.refreshToken;

      // Update stored tokens
      tokenManager.setToken(finalAccessToken);
      if (finalRefreshToken) {
        tokenManager.setRefreshToken(finalRefreshToken);
      }

      return {
        success: true,
        accessToken: finalAccessToken,
        refreshToken: finalRefreshToken,
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      
      // Clear tokens on refresh failure
      tokenManager.removeTokens();
      
      throw {
        success: false,
        message: 'Session expired. Please login again.',
        status: error.response?.status,
      };
    }
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const token = tokenManager.getToken();
    const user = tokenManager.getUser();
    const isTokenValid = tokenManager.isTokenValid();

    console.log('🔍 AuthService.isAuthenticated():', {
      hasToken: !!token,
      hasUser: !!user,
      isTokenValid: isTokenValid,
      tokenLength: token ? token.length : 0,
      userEmail: user ? user.email : null
    });

    // User is authenticated if they have a valid token and user data
    const result = !!(token && user && isTokenValid);
    console.log('🔍 AuthService.isAuthenticated() result:', result);
    return result;
  }

  /**
   * Get current user from storage
   * @returns {Object|null} Current user or null
   */
  getCurrentUser() {
    return tokenManager.getUser();
  }

  /**
   * Forgot password
   * @param {string} email - User email
   * @returns {Promise<Object>} Forgot password response
   */
  async forgotPassword(email) {
    try {
      const response = await api.post(endpoints.auth.forgotPassword, {
        email,
      });

      return {
        success: true,
        message: response.data.message || 'Password reset email sent',
      };
    } catch (error) {
      console.error('Forgot password error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to send password reset email';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }

  /**
   * Reset password
   * @param {Object} resetData - Reset password data
   * @param {string} resetData.token - Reset token
   * @param {string} resetData.password - New password
   * @returns {Promise<Object>} Reset password response
   */
  async resetPassword(resetData) {
    try {
      const response = await api.post(endpoints.auth.resetPassword, {
        token: resetData.token,
        password: resetData.password,
      });

      return {
        success: true,
        message: response.data.message || 'Password reset successful',
      };
    } catch (error) {
      console.error('Reset password error:', error);
      
      const errorMessage = error.response?.data?.message || 
                          'Failed to reset password';
      
      throw {
        success: false,
        message: errorMessage,
        status: error.response?.status,
      };
    }
  }
}

// Export singleton instance
export default new AuthService();
