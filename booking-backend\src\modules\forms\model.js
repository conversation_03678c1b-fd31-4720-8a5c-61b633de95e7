const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

/**
 * Forms Model
 * Defines the structure for booking forms
 */
const Forms = sequelize.define('forms', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Form name (e.g., Hair Cut Booking Form)'
    },
    slug: {
      type: DataTypes.STRING(150),
      allowNull: false,
      unique: true,
      comment: 'URL-friendly form identifier'
    },
    service_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'services',
        key: 'id'
      },
      comment: 'Associated service ID'
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'branches',
        key: 'id'
      },
      comment: 'Associated branch ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'Form owner (business user)'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'draft'),
      defaultValue: 'active',
      comment: 'Form status'
    },
    fields_config: {
      type: DataTypes.JSON,
      defaultValue: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      comment: 'Form fields configuration'
    },
    branding_config: {
      type: DataTypes.JSON,
      defaultValue: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      },
      comment: 'Form branding and customization'
    },
    booking_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Total bookings received through this form'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'forms',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['slug']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['service_id']
      },
      {
        fields: ['branch_id']
      },
      {
        fields: ['status']
      }
    ]
  });
// Associations
Forms.associate = (models) => {
  // Form belongs to a service
  Forms.belongsTo(models.services, {
    foreignKey: 'service_id',
    as: 'service'
  });

  // Form belongs to a branch
  Forms.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch'
  });

  // Form belongs to a user (business owner)
  Forms.belongsTo(models.users, {
    foreignKey: 'user_id',
    as: 'owner'
  });

  // Form has many bookings
  Forms.hasMany(models.bookings, {
    foreignKey: 'form_id',
    as: 'bookings'
  });
};

module.exports = Forms;
