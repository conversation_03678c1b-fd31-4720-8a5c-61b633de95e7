const { Op } = require('sequelize');
const { forms, services, branches, users } = require('../../database/models');
const { ValidationError, NotFoundError } = require('../../utils/errors');
const { generateSlug } = require('../../utils/helpers');

/**
 * Forms Service - Enhanced with Direct URL and Embed Code features
 * Handles all business logic for forms management
 */
class FormsService {
  /**
   * Create a new form
   */
  static async createForm(formData, userId) {
    try {
      // Validate required fields
      if (!formData.name) {
        throw new ValidationError('Form name is required');
      }
      if (!formData.serviceId) {
        throw new ValidationError('Service is required');
      }
      if (!formData.branchId) {
        throw new ValidationError('Branch is required');
      }

      // Verify service exists and belongs to user's branch
      const service = await services.findOne({
        where: { id: formData.serviceId, is_active: true },
        include: [{
          model: branches,
          as: 'branch',
          where: { manager_id: userId },
          required: true
        }]
      });
      if (!service) {
        throw new NotFoundError('Service not found or access denied');
      }

      // Verify branch exists and user is the manager
      const branch = await branches.findOne({
        where: { id: formData.branchId, manager_id: userId, is_active: true }
      });
      if (!branch) {
        throw new NotFoundError('Branch not found or access denied');
      }      // Generate unique slug
      const baseSlug = generateSlug(formData.name);
      let slug = baseSlug;
      let counter = 1;
      
      console.log('=== Forms Service - createForm Debug ===');
      console.log('Form name:', formData.name);
      console.log('Base slug generated:', baseSlug);
      
      while (await forms.findOne({ where: { slug } })) {
        slug = `${baseSlug}-${counter}`;
        console.log('Slug collision, trying:', slug);
        counter++;
      }
      
      console.log('Final slug:', slug);      // Create form
      const form = await forms.create({
        name: formData.name,
        slug,
        service_id: formData.serviceId,
        branch_id: formData.branchId,
        user_id: userId,
        status: formData.status || 'active',
        fields_config: formData.fieldsConfig || {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        branding_config: formData.brandingConfig || {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        }
      });

      console.log('Form created in database:');
      console.log('  - ID:', form.id);
      console.log('  - Name:', form.name);
      console.log('  - Slug:', form.slug);
      
      // Return form with associations
      const fullForm = await this.getFormById(form.id);
      console.log('Full form with associations:');
      console.log('  - ID:', fullForm.id);
      console.log('  - Slug:', fullForm.slug);
      console.log('  - Keys:', Object.keys(fullForm.toJSON()));
      console.log('=== End Service Debug ===');
      
      return fullForm;
    } catch (error) {
      console.error('Error creating form:', error);
      throw error;
    }
  }

  /**
   * Get all forms for a user
   */
  static async getUserForms(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        search
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = { user_id: userId };

      if (status) {
        whereClause.status = status;
      }

      if (search) {
        whereClause.name = {
          [Op.iLike]: `%${search}%`
        };
      }

      const { count, rows } = await forms.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: services,
            as: 'service',
            attributes: ['id', 'name', 'price', 'duration']
          },
          {
            model: branches,
            as: 'branch',
            attributes: ['id', 'name', 'address', 'phone']
          }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });

      return {
        forms: rows,
        pagination: {
          total: count,
          pages: Math.ceil(count / limit),
          currentPage: page,
          hasNext: page < Math.ceil(count / limit),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Error getting user forms:', error);
      throw error;
    }
  }

  /**
   * Get form by ID
   */
  static async getFormById(id) {
    try {
      const form = await forms.findByPk(id, {
        include: [
          {
            model: services,
            as: 'service',
            attributes: ['id', 'name', 'price', 'duration', 'description']
          },
          {
            model: branches,
            as: 'branch',
            attributes: ['id', 'name', 'address', 'phone', 'city']
          },
          {
            model: users,
            as: 'owner',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!form) {
        throw new NotFoundError('Form not found');
      }

      return form;
    } catch (error) {
      console.error('Error getting form by ID:', error);
      throw error;
    }
  }

  /**
   * Get form by slug (for public access)
   */
  static async getFormBySlug(slug) {
    try {
      const form = await forms.findOne({
        where: { slug, status: 'active' },
        include: [
          {
            model: services,
            as: 'service',
            attributes: ['id', 'name', 'price', 'duration', 'description']
          },
          {
            model: branches,
            as: 'branch',
            attributes: ['id', 'name', 'address', 'phone', 'city']
          },
          {
            model: users,
            as: 'owner',
            attributes: ['name']
          }
        ]
      });

      if (!form) {
        throw new NotFoundError('Form not found or inactive');
      }

      return form;
    } catch (error) {
      console.error('Error getting form by slug:', error);
      throw error;
    }
  }

  /**
   * Update form
   */
  static async updateForm(id, updateData, userId) {
    try {
      const form = await forms.findOne({
        where: { id, user_id: userId }
      });

      if (!form) {
        throw new NotFoundError('Form not found or access denied');
      }

      // Update slug if name changed
      if (updateData.name && updateData.name !== form.name) {
        const baseSlug = generateSlug(updateData.name);
        let slug = baseSlug;
        let counter = 1;
        
        while (await forms.findOne({ where: { slug, id: { [Op.ne]: id } } })) {
          slug = `${baseSlug}-${counter}`;
          counter++;
        }
        updateData.slug = slug;
      }

      // Validate service if provided
      if (updateData.serviceId) {
        const service = await services.findOne({
          where: { id: updateData.serviceId, is_active: true },
          include: [{
            model: branches,
            as: 'branch',
            where: { manager_id: userId },
            required: true
          }]
        });
        if (!service) {
          throw new NotFoundError('Service not found or access denied');
        }
        updateData.service_id = updateData.serviceId;
        delete updateData.serviceId;
      }

      // Validate branch if provided
      if (updateData.branchId) {
        const branch = await branches.findOne({
          where: { id: updateData.branchId, manager_id: userId, is_active: true }
        });
        if (!branch) {
          throw new NotFoundError('Branch not found or access denied');
        }
        updateData.branch_id = updateData.branchId;
        delete updateData.branchId;
      }

      await form.update(updateData);
      return await this.getFormById(id);
    } catch (error) {
      console.error('Error updating form:', error);
      throw error;
    }
  }

  /**
   * Delete form
   */
  static async deleteForm(id, userId) {
    try {
      const form = await forms.findOne({
        where: { id, user_id: userId }
      });

      if (!form) {
        throw new NotFoundError('Form not found or access denied');
      }

      await form.destroy();
      return { message: 'Form deleted successfully' };
    } catch (error) {
      console.error('Error deleting form:', error);
      throw error;
    }
  }

  /**
   * Increment booking count
   */
  static async incrementBookingCount(formId) {
    try {
      await forms.increment('booking_count', { where: { id: formId } });
    } catch (error) {
      console.error('Error incrementing booking count:', error);
      throw error;
    }
  }

  /**
   * Get form statistics
   */
  static async getFormStats(userId) {
    try {
      const totalForms = await forms.count({
        where: { user_id: userId }
      });

      const activeForms = await forms.count({
        where: { user_id: userId, status: 'active' }
      });

      const totalBookings = await forms.sum('booking_count', {
        where: { user_id: userId }
      }) || 0;

      return {
        totalForms,
        activeForms,
        totalBookings,
        averageBookingsPerForm: totalForms > 0 ? Math.round(totalBookings / totalForms) : 0
      };
    } catch (error) {
      console.error('Error getting form stats:', error);
      throw error;
    }
  }

  /**
   * Generate enhanced URLs and embed codes for a form
   */
  static generateFormUrls(slug, options = {}) {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4000';
    const baseUrl = `${frontendUrl}/book/${slug}`;
    
    // Generate various URL formats
    const urls = {
      // Basic URLs
      publicUrl: baseUrl,
      directUrl: baseUrl,
      
      // Social media URLs
      socialUrls: {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(baseUrl)}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(baseUrl)}&text=${encodeURIComponent('Book your appointment here:')}`,
        linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(baseUrl)}`,
        whatsapp: `https://wa.me/?text=${encodeURIComponent(`Book your appointment here: ${baseUrl}`)}`,
        telegram: `https://t.me/share/url?url=${encodeURIComponent(baseUrl)}&text=${encodeURIComponent('Book your appointment here')}`
      },
      
      // QR Code URL
      qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(baseUrl)}`,
      
      // Analytics tracking URLs
      trackingUrl: `${baseUrl}?ref=direct`,
      utmUrl: `${baseUrl}?utm_source=website&utm_medium=direct&utm_campaign=booking`
    };
    
    // Generate embed codes with different options
    const embedCodes = {
      // Standard embed code
      standard: `<iframe src="${baseUrl}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`,
      
      // Compact embed code
      compact: `<iframe src="${baseUrl}" width="100%" height="400" frameborder="0" style="border: none; border-radius: 4px;" allowtransparency="true" loading="lazy"></iframe>`,
      
      // Full height embed code
      fullHeight: `<iframe src="${baseUrl}" width="100%" height="800" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`,
      
      // Mobile optimized
      mobile: `<iframe src="${baseUrl}" width="100%" height="500" frameborder="0" style="border: none; border-radius: 6px; max-width: 100%; display: block; margin: 0 auto;" allowtransparency="true" loading="lazy"></iframe>`,
      
      // With shadow effect
      styled: `<iframe src="${baseUrl}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);" allowtransparency="true" loading="lazy"></iframe>`,
      
      // JavaScript embed (for dynamic loading)
      javascript: `<div id="formbooker-${slug}"></div>
<script>
(function() {
  var iframe = document.createElement('iframe');
  iframe.src = '${baseUrl}';
  iframe.width = '100%';
  iframe.height = '600';
  iframe.frameBorder = '0';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '8px';
  iframe.allowTransparency = true;
  iframe.loading = 'lazy';
  document.getElementById('formbooker-${slug}').appendChild(iframe);
})();
</script>`,
      
      // WordPress shortcode style
      shortcode: `[formbooker url="${baseUrl}" width="100%" height="600"]`,
      
      // Button embed (opens in popup)
      button: `<button onclick="window.open('${baseUrl}', 'booking', 'width=800,height=600,scrollbars=yes,resizable=yes')" style="background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">Book Appointment</button>`
    };
    
    return { urls, embedCodes };
  }

  /**
   * Generate social media sharing content
   */
  static generateSocialMediaContent(formName, serviceName, businessName) {
    return {
      title: `Book ${serviceName} - ${businessName}`,
      description: `Schedule your ${serviceName} appointment with ${businessName}. Quick and easy online booking.`,
      hashtags: ['booking', 'appointment', serviceName.toLowerCase().replace(/\s+/g, ''), businessName.toLowerCase().replace(/\s+/g, '')],
      
      // Platform-specific content
      facebook: {
        title: `Book your ${serviceName} appointment`,
        description: `Easy online booking with ${businessName}. Schedule your appointment now!`
      },
      twitter: {
        text: `Book your ${serviceName} appointment with ${businessName}`,
        hashtags: ['booking', 'appointment']
      },
      linkedin: {
        title: `Professional ${serviceName} booking`,
        summary: `Schedule your ${serviceName} appointment with ${businessName}`
      }
    };
  }
}

module.exports = FormsService;
