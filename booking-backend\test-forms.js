/**
 * Test Forms API
 * Simple test script for forms functionality
 */

const express = require('express');
const { sequelize, models } = require('./src/database/models');

async function testFormsAPI() {
  console.log('🧪 Testing Forms API...');
  
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Check if forms model is loaded
    console.log('📋 Available models:', Object.keys(models));
    
    if (models.forms) {
      console.log('✅ Forms model is available');
      
      // Test basic query
      const formsCount = await models.forms.count();
      console.log(`📊 Current forms count: ${formsCount}`);
      
      // Show table structure
      const tableInfo = await sequelize.query(
        'DESCRIBE forms',
        { type: sequelize.QueryTypes.SELECT }
      );
      
      console.log('🏗️ Forms table structure:');
      console.table(tableInfo);
      
    } else {
      console.log('❌ Forms model not found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
    console.log('👋 Database connection closed');
  }
}

testFormsAPI();
