# Phân Tích Chi Tiết Chức <PERSON>ng `/forms/new` - Tạo Form Booking

## 🎯 **Tổng Quan Chức Năng**

Chức năng `/forms/new` cho phép business owner t<PERSON><PERSON> các form booking tùy chỉnh để khách hàng có thể đặt lịch trực tuyến. Đ<PERSON><PERSON> là một tính năng quan trọng giúp tự động hóa quy trình nhận booking từ khách hàng.

## 🏗️ **Kiến Trúc Hệ <PERSON>hống**

### **Frontend (Next.js)**
- **File chính**: `formBooker-frontend/src/app/forms/new/page.js`
- **Service**: `formBooker-frontend/src/services/formsService.js`
- **API Client**: `formBooker-frontend/src/lib/api.js`

### **Backend (NestJS)**
- **Controller**: `booking-backend/src/modules/forms/controller.js`
- **Service**: `booking-backend/src/modules/forms/service.js`
- **Model**: `booking-backend/src/modules/forms/model.js`
- **Routes**: `booking-backend/src/modules/forms/route.js`
- **Migration**: `booking-backend/src/migrations/004-create-forms-table.js`

## 📊 **Database Schema**

```sql
CREATE TABLE forms (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,           -- Tên form (VD: "Hair Cut Booking")
  slug VARCHAR(150) UNIQUE NOT NULL,    -- URL slug (VD: "hair-cut-booking")
  service_id INTEGER REFERENCES services(id),
  branch_id INTEGER REFERENCES branches(id),
  user_id INTEGER REFERENCES users(id),
  status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
  fields_config JSON DEFAULT {          -- Cấu hình fields hiển thị
    "customerName": true,
    "phoneNumber": true,
    "emailAddress": true,
    "preferredDate": true,
    "preferredTime": true,
    "specialRequests": true
  },
  branding_config JSON DEFAULT {        -- Cấu hình giao diện
    "primaryColor": "#3b82f6",
    "logo": null,
    "customMessage": null
  },
  booking_count INTEGER DEFAULT 0,     -- Đếm số booking
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

## 🎨 **Giao Diện Frontend**

### **1. Layout Trang**
- **Header**: Tiêu đề "Create Booking Form" với nút Back
- **Layout**: 2 cột (Form builder bên trái, Live preview bên phải)
- **Responsive**: Trên mobile chuyển thành 1 cột

### **2. Form Builder (Cột Trái)**

#### **A. Form Settings**
```javascript
// State quản lý form data
const [formData, setFormData] = useState({
  name: '',                    // Tên form
  serviceId: '',              // ID service được chọn
  branchId: '',               // ID branch được chọn
  fields: {                   // Cấu hình fields
    customerName: true,
    phoneNumber: true,
    emailAddress: true,
    preferredDate: true,
    preferredTime: true,
    specialRequests: false
  }
});
```

#### **B. Input Fields**
1. **Form Name**: Text input bắt buộc
2. **Service Selection**: Dropdown load từ API `/services`
3. **Branch Selection**: Dropdown load từ API `/branches`

#### **C. Validation**
- Hiển thị warning box nếu thiếu thông tin bắt buộc
- Disable nút Preview và Save khi chưa đủ thông tin
- Real-time validation với UI feedback

### **3. Live Preview (Cột Phải)**
- Hiển thị preview form theo thời gian thực
- Cập nhật khi user thay đổi form settings
- Hiển thị thông tin service và branch được chọn
- Preview các fields sẽ xuất hiện trong form

### **4. Action Buttons**
- **Preview Form**: Mở modal preview full-screen
- **Create & Save Form**: Lưu form và hiển thị success modal

## 🔧 **Logic Backend**

### **1. API Endpoint**
```javascript
POST /api/forms
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Hair Cut Booking",
  "serviceId": 1,
  "branchId": 1,
  "status": "active",
  "fieldsConfig": {
    "customerName": true,
    "phoneNumber": true,
    "emailAddress": true,
    "preferredDate": true,
    "preferredTime": true,
    "specialRequests": true
  },
  "brandingConfig": {
    "primaryColor": "#3b82f6",
    "logo": null,
    "customMessage": null
  }
}
```

### **2. Validation Logic**
```javascript
// Backend validation trong FormsService.createForm()
if (!formData.name) {
  throw new ValidationError('Form name is required');
}
if (!formData.serviceId) {
  throw new ValidationError('Service is required');
}
if (!formData.branchId) {
  throw new ValidationError('Branch is required');
}

// Verify service exists và belongs to user's branch
const service = await services.findOne({
  where: { id: formData.serviceId, is_active: true },
  include: [{
    model: branches,
    as: 'branch',
    where: { manager_id: userId },
    required: true
  }]
});
```

### **3. Slug Generation**
```javascript
// Tự động tạo slug từ form name
const baseSlug = formData.name
  .toLowerCase()
  .replace(/[^a-z0-9\s-]/g, '')
  .replace(/\s+/g, '-')
  .trim();

// Đảm bảo slug unique
let slug = baseSlug;
let counter = 1;
while (await forms.findOne({ where: { slug } })) {
  slug = `${baseSlug}-${counter}`;
  counter++;
}
```

## 📱 **Luồng Hoạt Động**

### **1. Khởi Tạo Trang**
```mermaid
graph TD
    A[User truy cập /forms/new] --> B[Load services và branches]
    B --> C[Hiển thị form builder]
    C --> D[User nhập thông tin]
    D --> E[Real-time preview update]
```

### **2. Tạo Form**
```mermaid
graph TD
    A[User click 'Create & Save Form'] --> B[Frontend validation]
    B --> C[Call API POST /forms]
    C --> D[Backend validation]
    D --> E[Generate unique slug]
    E --> F[Save to database]
    F --> G[Return form data với publicUrl]
    G --> H[Hiển thị Success Modal]
```

### **3. Success Modal**
Sau khi tạo form thành công, hiển thị modal với:
- **Public URL**: `${FRONTEND_URL}/book/${form.slug}`
- **Embed Code**: iframe code để nhúng vào website
- **Actions**: View All Forms, Continue Editing, Create Another Form

## 🔗 **API Integration**

### **1. Frontend Service**
```javascript
// formsService.js
static async createForm(formData) {
  const response = await api.post('/forms', {
    name: formData.name,
    serviceId: parseInt(formData.serviceId),
    branchId: parseInt(formData.branchId),
    status: formData.status || 'active',
    fieldsConfig: formData.fields,
    brandingConfig: {
      primaryColor: '#3b82f6',
      logo: null,
      customMessage: null
    }
  });
  return response.data;
}
```

### **2. Backend Response**
```javascript
// Trả về form data kèm public URL và embed code
const responseData = {
  ...form.toJSON(),
  publicUrl: `${process.env.FRONTEND_URL}/book/${form.slug}`,
  embedCode: `<iframe src="${process.env.FRONTEND_URL}/book/${form.slug}"
              width="100%" height="600" frameborder="0"
              style="border: none; border-radius: 8px;"
              allowtransparency="true" loading="lazy"></iframe>`
};
```

## 🎯 **Tính Năng Đặc Biệt**

### **1. Real-time Preview**
- Live preview cập nhật ngay khi user thay đổi form settings
- Hiển thị chính xác giao diện form sẽ thấy bởi khách hàng
- Preview bao gồm service info, branch info, và form fields

### **2. Smart Validation**
- Visual feedback với warning box màu vàng
- Disable buttons khi thiếu thông tin
- Clear error messages cho từng field

### **3. Success Flow**
- Modal success với đầy đủ thông tin form đã tạo
- Copy-paste ready embed code
- Multiple action options sau khi tạo form

## 🔒 **Bảo Mật**

### **1. Authentication**
- Yêu cầu JWT token trong header
- Verify user ownership của service và branch

### **2. Authorization**
- Chỉ manager của branch mới có thể tạo form cho branch đó
- Validate service thuộc về branch của user

### **3. Input Validation**
- Sanitize form name để tạo slug an toàn
- Validate JSON structure cho fields_config và branding_config
- SQL injection protection thông qua Sequelize ORM

## 📈 **Performance**

### **1. Frontend Optimization**
- Lazy loading cho heavy components
- Debounced preview updates
- Efficient state management

### **2. Backend Optimization**
- Database indexes trên slug và user_id
- Efficient queries với proper includes
- Error handling và logging

## 🎨 **UI Components Chi Tiết**

### **1. Success Modal Component**
```javascript
const SuccessModal = () => {
  return (
    <div className="modal-overlay">
      <div className="modal-content max-w-2xl">
        {/* Header với icon success */}
        <div className="text-center p-6">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-text mb-2">
            Form Created Successfully!
          </h2>
        </div>

        {/* Form info */}
        <div className="px-6 pb-4">
          <div className="bg-neutral-50 rounded-lg p-4 space-y-3">
            <div>
              <label className="text-sm font-medium text-neutral-600">
                Public URL
              </label>
              <div className="flex items-center space-x-2 mt-1">
                <input
                  value={savedForm?.publicUrl}
                  readOnly
                  className="form-input flex-1"
                />
                <button className="btn-outline btn-sm">
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-neutral-600">
                Embed Code
              </label>
              <textarea
                value={savedForm?.embedCode}
                readOnly
                rows={3}
                className="form-textarea mt-1"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between p-6 border-t">
          <button className="btn-outline">View All Forms</button>
          <div className="flex space-x-3">
            <button className="btn-outline">Continue Editing</button>
            <button className="btn-primary">Create Another Form</button>
          </div>
        </div>
      </div>
    </div>
  );
};
```

### **2. Validation Warning Component**
```javascript
const ValidationWarning = ({ formData }) => {
  const missingFields = [];
  if (!formData.name) missingFields.push('Form name is required');
  if (!formData.serviceId) missingFields.push('Please select a service');
  if (!formData.branchId) missingFields.push('Please select a branch');

  if (missingFields.length === 0) return null;

  return (
    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-start">
        <div className="w-5 h-5 rounded-full bg-yellow-400 flex items-center justify-center">
          <span className="text-white text-xs font-bold">!</span>
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-yellow-800">
            Complete Required Fields
          </p>
          <ul className="mt-1 text-xs text-yellow-700 space-y-1">
            {missingFields.map((field, index) => (
              <li key={index}>• {field}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};
```

### **3. Live Preview Component**
```javascript
const LivePreview = ({ formData, selectedService, selectedBranch }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Live Preview</h3>
        <p className="text-sm text-neutral-500 mt-1">
          See how your form will look to customers
        </p>
      </div>

      <div className="card-body">
        <div className="border border-neutral-200 rounded-lg p-6 bg-white shadow-sm">
          {/* Form Header */}
          <div className="text-center mb-6">
            <h3 className="text-xl font-semibold mb-2">
              {formData.name || 'Your Booking Form'}
            </h3>

            {selectedService && (
              <div className="text-sm text-neutral-600 space-y-1 p-3 bg-primary-50 rounded-lg">
                <p className="font-medium text-primary-800">
                  Service: {selectedService.name}
                </p>
                <p>Duration: {selectedService.duration} minutes</p>
                <p>Price: ${selectedService.price}</p>
                {selectedBranch && (
                  <p>Location: {selectedBranch.name}</p>
                )}
              </div>
            )}
          </div>

          {/* Form Fields Preview */}
          <div className="space-y-4">
            <div>
              <label className="form-label">Full Name *</label>
              <input className="form-input" placeholder="Enter your full name" disabled />
            </div>

            <div>
              <label className="form-label">Phone Number *</label>
              <input className="form-input" placeholder="Enter your phone number" disabled />
            </div>

            <div>
              <label className="form-label">Email Address *</label>
              <input className="form-input" placeholder="Enter your email" disabled />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="form-label">Preferred Date *</label>
                <input type="date" className="form-input" disabled />
              </div>
              <div>
                <label className="form-label">Preferred Time *</label>
                <select className="form-select" disabled>
                  <option>Select time</option>
                </select>
              </div>
            </div>

            {formData.fields?.specialRequests && (
              <div>
                <label className="form-label">Special Requests</label>
                <textarea
                  className="form-textarea"
                  placeholder="Any special requests or notes..."
                  disabled
                />
              </div>
            )}

            <button className="btn-primary w-full" disabled>
              Book Appointment
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
```

## 🚨 **Error Handling**

### **1. Frontend Error Handling**
```javascript
// Loading states
const [isLoadingServices, setIsLoadingServices] = useState(true);
const [isLoadingBranches, setIsLoadingBranches] = useState(true);
const [isSaving, setIsSaving] = useState(false);

// Error states
const [loadError, setLoadError] = useState(null);
const [errors, setErrors] = useState({});

// Error handling trong useEffect
useEffect(() => {
  const loadInitialData = async () => {
    try {
      const [servicesResponse, branchesResponse] = await Promise.allSettled([
        ServicesService.getServices(),
        BranchesService.getBranches()
      ]);

      if (servicesResponse.status === 'rejected') {
        console.error('Failed to load services:', servicesResponse.reason);
        setLoadError('Failed to load services. Please refresh the page.');
      }

      if (branchesResponse.status === 'rejected') {
        console.error('Failed to load branches:', branchesResponse.reason);
        setLoadError('Failed to load branches. Please refresh the page.');
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      setLoadError('Failed to load form data. Please refresh the page.');
    }
  };

  loadInitialData();
}, []);

// Error handling khi save form
const handleSave = async () => {
  setIsSaving(true);

  try {
    if (!formData.name || !formData.serviceId || !formData.branchId) {
      alert('Please fill in all required fields');
      return;
    }

    const result = await FormsService.createForm(formData);

    if (result.success) {
      setSavedForm(result.data);
      setShowSuccessModal(true);
    } else {
      alert(`Error: ${result.error}`);
    }
  } catch (error) {
    console.error('Error saving form:', error);
    alert('Failed to save form. Please try again.');
  } finally {
    setIsSaving(false);
  }
};
```

### **2. Backend Error Handling**
```javascript
// FormsService.createForm() error handling
static async createForm(formData, userId) {
  try {
    // Validation errors
    if (!formData.name) {
      throw new ValidationError('Form name is required');
    }
    if (!formData.serviceId) {
      throw new ValidationError('Service is required');
    }
    if (!formData.branchId) {
      throw new ValidationError('Branch is required');
    }

    // Authorization errors
    const service = await services.findOne({
      where: { id: formData.serviceId, is_active: true },
      include: [{
        model: branches,
        as: 'branch',
        where: { manager_id: userId },
        required: true
      }]
    });

    if (!service) {
      throw new NotFoundError('Service not found or access denied');
    }

    // Database errors
    const form = await forms.create({...formData});
    return await this.getFormById(form.id);

  } catch (error) {
    console.error('Error creating form:', error);

    // Re-throw known errors
    if (error instanceof ValidationError || error instanceof NotFoundError) {
      throw error;
    }

    // Handle database errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new ValidationError('A form with this name already exists');
    }

    // Generic error
    throw new Error('Failed to create form. Please try again.');
  }
}
```

### **3. API Error Response Format**
```javascript
// Controller error handling
static async createForm(req, res, next) {
  try {
    const userId = req.user.id;
    const form = await FormsService.createForm(req.body, userId);

    const responseData = {
      ...form.toJSON(),
      publicUrl: `${process.env.FRONTEND_URL}/book/${form.slug}`,
      embedCode: `<iframe src="..." />`
    };

    res.status(201).json({
      success: true,
      data: responseData,
      message: 'Form created successfully'
    });

  } catch (error) {
    console.error('Error in createForm controller:', error);

    if (error instanceof ValidationError) {
      return res.status(400).json({
        success: false,
        error: error.message,
        code: 'VALIDATION_ERROR'
      });
    }

    if (error instanceof NotFoundError) {
      return res.status(404).json({
        success: false,
        error: error.message,
        code: 'NOT_FOUND'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
}
```

## 🛣️ **Routing & Navigation**

### **1. Frontend Routing**
```javascript
// Next.js App Router structure
formBooker-frontend/src/app/
├── forms/
│   ├── page.js              // /forms - Danh sách forms
│   ├── new/
│   │   └── page.js          // /forms/new - Tạo form mới
│   └── [id]/
│       └── edit/
│           └── page.js      // /forms/:id/edit - Chỉnh sửa form

// Navigation trong NewFormPage
const router = useRouter();

// Back button
<button onClick={() => router.back()}>
  <ArrowLeft className="h-4 w-4 mr-1" />
  Back
</button>

// Success modal actions
const handleViewAllForms = () => {
  router.push('/forms');
};

const handleCreateAnother = () => {
  // Reset form state và ở lại trang hiện tại
  setFormData(initialFormData);
  setSavedForm(null);
  setShowSuccessModal(false);
};
```

### **2. Backend Routing**
```javascript
// booking-backend/src/modules/forms/route.js
const express = require('express');
const router = express.Router();
const FormsController = require('./controller');
const { authenticate } = require('../../middleware/auth');

// Protected routes - yêu cầu authentication
router.use(authenticate);

/**
 * @swagger
 * /api/forms:
 *   post:
 *     summary: Create a new booking form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - serviceId
 *               - branchId
 */
router.post('/', FormsController.createForm);
router.get('/', FormsController.getForms);
router.get('/:id', FormsController.getForm);
router.put('/:id', FormsController.updateForm);
router.delete('/:id', FormsController.deleteForm);

// Public routes - không yêu cầu authentication
router.get('/public/:slug', FormsController.getPublicForm);

module.exports = router;
```

## 📊 **State Management Chi Tiết**

### **1. Component State Structure**
```javascript
const NewFormPage = () => {
  // Form data state
  const [formData, setFormData] = useState({
    name: '',                    // Tên form
    serviceId: '',              // ID service được chọn
    branchId: '',               // ID branch được chọn
    fields: {                   // Cấu hình fields (hiện tại cố định)
      customerName: true,
      phoneNumber: true,
      emailAddress: true,
      preferredDate: true,
      preferredTime: true,
      specialRequests: false
    }
  });

  // UI state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Data loading state
  const [services, setServices] = useState([]);
  const [branches, setBranches] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);

  // Error state
  const [loadError, setLoadError] = useState(null);
  const [errors, setErrors] = useState({});

  // Success state
  const [savedForm, setSavedForm] = useState(null);

  // Computed state
  const selectedService = services.find(s => s.id === parseInt(formData.serviceId));
  const selectedBranch = branches.find(b => b.id === parseInt(formData.branchId));

  // Form validation state
  const isFormValid = formData.name && formData.serviceId && formData.branchId;
};
```

### **2. State Update Handlers**
```javascript
// Input change handler
const handleInputChange = (e) => {
  const { name, value } = e.target;
  setFormData(prev => ({
    ...prev,
    [name]: value
  }));
};

// Service selection handler
const handleServiceChange = (e) => {
  const serviceId = e.target.value;
  setFormData(prev => ({
    ...prev,
    serviceId,
    // Reset branch khi đổi service (nếu cần)
    branchId: prev.branchId
  }));
};

// Branch selection handler
const handleBranchChange = (e) => {
  const branchId = e.target.value;
  setFormData(prev => ({
    ...prev,
    branchId
  }));
};

// Reset form state
const resetForm = () => {
  setFormData({
    name: '',
    serviceId: '',
    branchId: '',
    fields: {
      customerName: true,
      phoneNumber: true,
      emailAddress: true,
      preferredDate: true,
      preferredTime: true,
      specialRequests: false
    }
  });
  setSavedForm(null);
  setErrors({});
};
```

### **3. Side Effects Management**
```javascript
// Load initial data
useEffect(() => {
  const loadInitialData = async () => {
    try {
      // Load services và branches song song
      const [servicesResponse, branchesResponse] = await Promise.allSettled([
        ServicesService.getServices(),
        BranchesService.getBranches()
      ]);

      // Handle services response
      if (servicesResponse.status === 'fulfilled' && servicesResponse.value.success) {
        setServices(servicesResponse.value.data || []);
      } else {
        console.error('Failed to load services:', servicesResponse.reason);
        setLoadError('Failed to load services. Please refresh the page.');
      }
      setIsLoadingServices(false);

      // Handle branches response
      if (branchesResponse.status === 'fulfilled' && branchesResponse.value.success) {
        setBranches(branchesResponse.value.data || []);
      } else {
        console.error('Failed to load branches:', branchesResponse.reason);
        setLoadError('Failed to load branches. Please refresh the page.');
      }
      setIsLoadingBranches(false);

    } catch (error) {
      console.error('Error loading initial data:', error);
      setLoadError('Failed to load form data. Please refresh the page.');
      setIsLoadingServices(false);
      setIsLoadingBranches(false);
    }
  };

  loadInitialData();
}, []);

// Auto-save draft (nếu cần implement)
useEffect(() => {
  const saveDraft = () => {
    if (formData.name || formData.serviceId || formData.branchId) {
      localStorage.setItem('formDraft', JSON.stringify(formData));
    }
  };

  const timeoutId = setTimeout(saveDraft, 2000); // Debounce 2s
  return () => clearTimeout(timeoutId);
}, [formData]);

// Load draft on mount
useEffect(() => {
  const draft = localStorage.getItem('formDraft');
  if (draft) {
    try {
      const parsedDraft = JSON.parse(draft);
      setFormData(prev => ({ ...prev, ...parsedDraft }));
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  }
}, []);
```

## 🔄 **Data Flow**

### **1. Form Creation Flow**
```mermaid
graph TD
    A[User nhập form data] --> B[State update via handleInputChange]
    B --> C[Real-time validation]
    C --> D[Live preview update]
    D --> E[User click Save]
    E --> F[Frontend validation]
    F --> G[API call POST /forms]
    G --> H[Backend validation]
    H --> I[Database save]
    I --> J[Return form với publicUrl]
    J --> K[Update savedForm state]
    K --> L[Show success modal]
```

### **2. Error Flow**
```mermaid
graph TD
    A[API Error] --> B[Catch in handleSave]
    B --> C[Log error]
    C --> D[Show user-friendly message]
    D --> E[Reset loading state]

    F[Validation Error] --> G[Update errors state]
    G --> H[Show validation UI]
    H --> I[Disable submit button]
```

## 🧪 **Testing Strategy**

### **1. Unit Tests**
```javascript
// Test form validation
describe('Form Validation', () => {
  test('should require form name', () => {
    const formData = { name: '', serviceId: '1', branchId: '1' };
    expect(validateForm(formData)).toBe(false);
  });

  test('should require service selection', () => {
    const formData = { name: 'Test Form', serviceId: '', branchId: '1' };
    expect(validateForm(formData)).toBe(false);
  });
});

// Test slug generation
describe('Slug Generation', () => {
  test('should generate valid slug from form name', () => {
    const name = 'Hair Cut Booking Form!';
    const slug = generateSlug(name);
    expect(slug).toBe('hair-cut-booking-form');
  });
});
```

### **2. Integration Tests**
```javascript
// Test API endpoints
describe('Forms API', () => {
  test('POST /api/forms should create form', async () => {
    const formData = {
      name: 'Test Form',
      serviceId: 1,
      branchId: 1
    };

    const response = await request(app)
      .post('/api/forms')
      .set('Authorization', `Bearer ${token}`)
      .send(formData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.name).toBe('Test Form');
    expect(response.body.data.publicUrl).toContain('/book/');
  });
});
```

### **3. E2E Tests**
```javascript
// Test complete user journey
describe('Form Creation E2E', () => {
  test('should create form successfully', async () => {
    await page.goto('/forms/new');

    // Fill form
    await page.fill('[name="name"]', 'Hair Cut Booking');
    await page.selectOption('[name="serviceId"]', '1');
    await page.selectOption('[name="branchId"]', '1');

    // Preview form
    await page.click('text=Preview Form');
    await expect(page.locator('.modal')).toBeVisible();
    await page.click('text=Close');

    // Save form
    await page.click('text=Create & Save Form');

    // Verify success modal
    await expect(page.locator('text=Form Created Successfully')).toBeVisible();
    await expect(page.locator('[data-testid="public-url"]')).toContainText('/book/');
  });
});
```

## 💡 **Best Practices & Optimization**

### **1. Performance Optimization**
```javascript
// Debounced preview updates
const debouncedUpdatePreview = useMemo(
  () => debounce((formData) => {
    // Update preview logic
    updatePreview(formData);
  }, 300),
  []
);

// Memoized computed values
const selectedService = useMemo(
  () => services.find(s => s.id === parseInt(formData.serviceId)),
  [services, formData.serviceId]
);

// Lazy loading cho heavy components
const PreviewModal = lazy(() => import('./PreviewModal'));

// Virtual scrolling cho large lists (nếu có nhiều services/branches)
const VirtualizedSelect = ({ options, value, onChange }) => {
  return (
    <FixedSizeList
      height={200}
      itemCount={options.length}
      itemSize={35}
    >
      {({ index, style }) => (
        <div style={style}>
          <option value={options[index].id}>
            {options[index].name}
          </option>
        </div>
      )}
    </FixedSizeList>
  );
};
```

### **2. UX/UI Best Practices**
```javascript
// Loading skeletons
const FormSkeleton = () => (
  <div className="space-y-4">
    <div className="h-4 bg-neutral-200 rounded animate-pulse"></div>
    <div className="h-10 bg-neutral-200 rounded animate-pulse"></div>
    <div className="h-4 bg-neutral-200 rounded animate-pulse w-3/4"></div>
    <div className="h-10 bg-neutral-200 rounded animate-pulse"></div>
  </div>
);

// Progressive enhancement
const EnhancedFormInput = ({ name, value, onChange, ...props }) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div className={`form-group ${isFocused ? 'focused' : ''}`}>
      <input
        name={name}
        value={value}
        onChange={onChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className="form-input"
        {...props}
      />
      {isFocused && (
        <div className="form-hint">
          This will be displayed to your customers
        </div>
      )}
    </div>
  );
};

// Accessibility improvements
const AccessibleModal = ({ isOpen, onClose, children }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      // Focus trap
      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      focusableElements[0]?.focus();
    } else {
      document.body.style.overflow = 'unset';
    }
  }, [isOpen]);

  return (
    <div
      className="modal-overlay"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      {children}
    </div>
  );
};
```

### **3. Security Best Practices**
```javascript
// Input sanitization
const sanitizeFormName = (name) => {
  return name
    .trim()
    .replace(/[<>\"']/g, '') // Remove potentially dangerous characters
    .substring(0, 100); // Limit length
};

// CSRF protection
const CSRFToken = () => {
  const [token, setToken] = useState('');

  useEffect(() => {
    // Get CSRF token from meta tag or API
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    setToken(csrfToken);
  }, []);

  return <input type="hidden" name="_token" value={token} />;
};

// Rate limiting on frontend
const useRateLimit = (limit = 5, window = 60000) => {
  const [attempts, setAttempts] = useState([]);

  const canMakeRequest = () => {
    const now = Date.now();
    const recentAttempts = attempts.filter(time => now - time < window);
    return recentAttempts.length < limit;
  };

  const recordAttempt = () => {
    setAttempts(prev => [...prev, Date.now()]);
  };

  return { canMakeRequest, recordAttempt };
};
```

## 🔧 **Troubleshooting Guide**

### **1. Common Issues**

#### **A. Form không save được**
```javascript
// Debug steps:
1. Check browser console for errors
2. Verify API endpoint accessibility
3. Check authentication token
4. Validate form data structure

// Debug helper
const debugFormSave = async (formData) => {
  console.log('Form data being sent:', formData);
  console.log('Auth token:', localStorage.getItem('token'));
  console.log('API base URL:', process.env.NEXT_PUBLIC_API_URL);

  try {
    const response = await FormsService.createForm(formData);
    console.log('API response:', response);
  } catch (error) {
    console.error('API error details:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
  }
};
```

#### **B. Services/Branches không load**
```javascript
// Check API endpoints
const debugDataLoading = async () => {
  try {
    console.log('Loading services...');
    const services = await ServicesService.getServices();
    console.log('Services loaded:', services);

    console.log('Loading branches...');
    const branches = await BranchesService.getBranches();
    console.log('Branches loaded:', branches);
  } catch (error) {
    console.error('Data loading error:', error);
  }
};

// Fallback UI
const DataLoadingFallback = ({ error, retry }) => (
  <div className="text-center py-8">
    <div className="text-red-500 mb-4">
      {error || 'Failed to load data'}
    </div>
    <button onClick={retry} className="btn-primary">
      Try Again
    </button>
  </div>
);
```

#### **C. Preview không hiển thị đúng**
```javascript
// Check computed values
const debugPreview = () => {
  console.log('Form data:', formData);
  console.log('Selected service:', selectedService);
  console.log('Selected branch:', selectedBranch);
  console.log('Is form valid:', isFormValid);
};

// Preview error boundary
class PreviewErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Preview error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center py-8">
          <p className="text-red-500">Preview temporarily unavailable</p>
          <button
            onClick={() => this.setState({ hasError: false })}
            className="btn-outline mt-2"
          >
            Retry Preview
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### **2. Performance Issues**

#### **A. Slow form loading**
```javascript
// Optimize data loading
const useOptimizedDataLoading = () => {
  const [data, setData] = useState({ services: [], branches: [] });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      // Load from cache first
      const cachedData = getCachedData();
      if (cachedData) {
        setData(cachedData);
        setLoading(false);
      }

      // Then load fresh data
      try {
        const freshData = await loadFreshData();
        setData(freshData);
        setCachedData(freshData);
      } catch (error) {
        console.error('Failed to load fresh data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return { data, loading };
};
```

#### **B. Memory leaks**
```javascript
// Cleanup subscriptions
useEffect(() => {
  const subscription = someObservable.subscribe(data => {
    setData(data);
  });

  return () => {
    subscription.unsubscribe();
  };
}, []);

// Cleanup timeouts
useEffect(() => {
  const timeoutId = setTimeout(() => {
    // Some delayed action
  }, 1000);

  return () => {
    clearTimeout(timeoutId);
  };
}, []);
```

### **3. Monitoring & Logging**
```javascript
// Error tracking
const trackError = (error, context) => {
  // Send to error tracking service (Sentry, LogRocket, etc.)
  console.error('Form creation error:', {
    error: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  });
};

// Performance monitoring
const trackPerformance = (action, duration) => {
  // Send to analytics service
  console.log('Performance metric:', {
    action,
    duration,
    timestamp: new Date().toISOString()
  });
};

// Usage tracking
const trackFormCreation = (formData) => {
  // Track form creation events
  console.log('Form created:', {
    formName: formData.name,
    serviceId: formData.serviceId,
    branchId: formData.branchId,
    timestamp: new Date().toISOString()
  });
};
```

---

## 📝 **Tóm Tắt**

Chức năng `/forms/new` là một tính năng quan trọng cho phép business owners tạo các form booking tùy chỉnh. Hệ thống được thiết kế với:

- **Frontend**: React/Next.js với UI responsive và real-time preview
- **Backend**: NestJS với validation mạnh mẽ và error handling
- **Database**: PostgreSQL với schema được tối ưu
- **Security**: Authentication, authorization và input validation
- **Performance**: Optimized loading, caching và error boundaries
- **Testing**: Comprehensive test coverage từ unit đến E2E

Hệ thống đảm bảo trải nghiệm người dùng mượt mà từ việc tạo form đến việc khách hàng sử dụng form để đặt lịch.
