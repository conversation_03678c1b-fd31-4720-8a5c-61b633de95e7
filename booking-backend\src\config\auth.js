/**
 * Authentication Configuration
 * JWT and security settings
 */

const config = require('./environment');

const authConfig = {
  // JWT Configuration
  jwt: {
    secret: config.jwt.secret,
    expiresIn: config.jwt.expiresIn,
    refreshSecret: config.jwt.refreshSecret,
    refreshExpiresIn: config.jwt.refreshExpiresIn,
    
    // JWT Options
    options: {
      issuer: config.app.name,
      audience: config.app.url,
      algorithm: 'HS256'
    },
    
    // Refresh Token Options
    refreshOptions: {
      issuer: config.app.name,
      audience: config.app.url,
      algorithm: 'HS256'
    }
  },

  // Password Configuration
  password: {
    bcryptRounds: config.security.bcryptRounds,
    minLength: 6,
    maxLength: 128,
    
    // Password strength requirements
    requirements: {
      minLength: 6,
      requireUppercase: false,
      requireLowercase: false,
      requireNumbers: false,
      requireSpecialChars: false
    }
  },

  // Session Configuration
  session: {
    maxSessions: 5, // Maximum concurrent sessions per user
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    refreshThreshold: 15 * 60 * 1000 // Refresh token if expires in 15 minutes
  },

  // Rate Limiting for Auth Endpoints
  rateLimiting: {
    login: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per window
      message: 'Too many login attempts, please try again later'
    },
    
    register: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 registrations per hour per IP
      message: 'Too many registration attempts, please try again later'
    },
    
    forgotPassword: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 forgot password attempts per hour
      message: 'Too many password reset attempts, please try again later'
    }
  },

  // Account Security
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 30 * 60 * 1000, // 30 minutes
    
    // Password reset
    resetTokenExpiry: 60 * 60 * 1000, // 1 hour
    resetTokenLength: 32,
    
    // Email verification
    verificationTokenExpiry: 24 * 60 * 60 * 1000, // 24 hours
    verificationTokenLength: 32,
    
    // Two-factor authentication (future)
    twoFactorEnabled: false,
    twoFactorIssuer: config.app.name
  },

  // Role-based Access Control
  roles: {
    admin: {
      name: 'admin',
      permissions: ['*'], // All permissions
      description: 'System administrator with full access'
    },
    
    staff: {
      name: 'staff',
      permissions: [
        'bookings:read',
        'bookings:create',
        'bookings:update',
        'bookings:delete',
        'customers:read',
        'customers:create',
        'customers:update',
        'services:read',
        'employees:read',
        'branches:read',
        'payments:read',
        'payments:create',
        'reports:read'
      ],
      description: 'Staff member with booking and customer management access'
    },
    
    customer: {
      name: 'customer',
      permissions: [
        'bookings:read:own',
        'bookings:create:own',
        'bookings:update:own',
        'bookings:cancel:own',
        'services:read',
        'branches:read',
        'employees:read',
        'profile:read:own',
        'profile:update:own'
      ],
      description: 'Customer with limited access to own bookings and profile'
    }
  },

  // Permission definitions
  permissions: {
    'bookings:read': 'Read all bookings',
    'bookings:read:own': 'Read own bookings',
    'bookings:create': 'Create bookings for any customer',
    'bookings:create:own': 'Create own bookings',
    'bookings:update': 'Update any booking',
    'bookings:update:own': 'Update own bookings',
    'bookings:delete': 'Delete any booking',
    'bookings:cancel:own': 'Cancel own bookings',
    'customers:read': 'Read customer information',
    'customers:create': 'Create new customers',
    'customers:update': 'Update customer information',
    'services:read': 'Read service information',
    'employees:read': 'Read employee information',
    'branches:read': 'Read branch information',
    'payments:read': 'Read payment information',
    'payments:create': 'Process payments',
    'reports:read': 'Access reports',
    'profile:read:own': 'Read own profile',
    'profile:update:own': 'Update own profile'
  }
};

// Helper functions
authConfig.hasPermission = (userRole, permission) => {
  const role = authConfig.roles[userRole];
  if (!role) return false;
  
  // Admin has all permissions
  if (role.permissions.includes('*')) return true;
  
  // Check specific permission
  return role.permissions.includes(permission);
};

authConfig.getRolePermissions = (roleName) => {
  const role = authConfig.roles[roleName];
  return role ? role.permissions : [];
};

authConfig.validateRole = (roleName) => {
  return Object.keys(authConfig.roles).includes(roleName);
};

module.exports = authConfig;
