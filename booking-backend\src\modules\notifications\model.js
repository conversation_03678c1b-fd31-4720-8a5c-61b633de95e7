/**
 * Notification Model
 * Notification management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'Unique notification identifier'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who will receive the notification'
  },
  type: {
    type: DataTypes.ENUM('booking', 'payment', 'promotion', 'system', 'reminder', 'cancellation', 'confirmation'),
    allowNull: false,
    comment: 'Type of notification'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: 'Notification title'
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Notification message content'
  },
  data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional data for the notification'
  },
  isRead: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_read',
    comment: 'Whether the notification has been read'
  },
  readAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'read_at',
    comment: 'When the notification was read'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium',
    comment: 'Notification priority level'
  },
  channel: {
    type: DataTypes.ENUM('in_app', 'email', 'sms', 'push'),
    defaultValue: 'in_app',
    comment: 'Notification delivery channel'
  },
  scheduledAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'scheduled_at',
    comment: 'When the notification should be sent'
  },
  sentAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'sent_at',
    comment: 'When the notification was actually sent'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'expires_at',
    comment: 'When the notification expires'
  },
  actionUrl: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'action_url',
    comment: 'URL for notification action'
  },
  actionText: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'action_text',
    comment: 'Text for notification action button'
  },
  bookingId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'booking_id',
    references: {
      model: 'bookings',
      key: 'id'
    },
    comment: 'Related booking ID (if applicable)'
  },
  paymentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'payment_id',
    references: {
      model: 'payments',
      key: 'id'
    },
    comment: 'Related payment ID (if applicable)'
  },
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'customer_id',
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'Related customer ID (if applicable)'
  },
  employeeId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'employee_id',
    references: {
      model: 'employees',
      key: 'id'
    },
    comment: 'Related employee ID (if applicable)'
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Related branch ID (if applicable)'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'created_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who created the notification'
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'delivered', 'failed', 'cancelled'),
    defaultValue: 'pending',
    comment: 'Notification delivery status'
  },
  deliveryAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'delivery_attempts',
    comment: 'Number of delivery attempts'
  },
  lastAttemptAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'last_attempt_at',
    comment: 'Last delivery attempt timestamp'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message',
    comment: 'Error message if delivery failed'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata for the notification'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: 'Whether the notification is active'
  }
}, {
  tableName: 'notifications',
  timestamps: true,
  underscored: true,
  paranoid: true,
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['status']
    },
    {
      fields: ['scheduled_at']
    },
    {
      fields: ['expires_at']
    },
    {
      fields: ['booking_id']
    },
    {
      fields: ['payment_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['employee_id']
    },
    {
      fields: ['branch_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

// Define associations
Notification.associate = (models) => {
  // Notification belongs to User (recipient)
  Notification.belongsTo(models.users, {
    foreignKey: 'user_id',
    as: 'user'
  });

  // Notification belongs to User (creator)
  Notification.belongsTo(models.users, {
    foreignKey: 'created_by',
    as: 'createdByUser'
  });

  // Notification belongs to Booking (optional)
  Notification.belongsTo(models.bookings, {
    foreignKey: 'booking_id',
    as: 'booking'
  });

  // Notification belongs to Payment (optional)
  Notification.belongsTo(models.payments, {
    foreignKey: 'payment_id',
    as: 'payment'
  });

  // Notification belongs to Customer (optional)
  Notification.belongsTo(models.customers, {
    foreignKey: 'customer_id',
    as: 'customer'
  });

  // Notification belongs to Employee (optional)
  Notification.belongsTo(models.employees, {
    foreignKey: 'employee_id',
    as: 'employee'
  });

  // Notification belongs to Branch (optional)
  Notification.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch'
  });
};

// Instance methods
Notification.prototype.markAsRead = function() {
  return this.update({
    isRead: true,
    readAt: new Date()
  });
};

Notification.prototype.markAsSent = function() {
  return this.update({
    status: 'sent',
    sentAt: new Date()
  });
};

Notification.prototype.markAsDelivered = function() {
  return this.update({
    status: 'delivered'
  });
};

Notification.prototype.markAsFailed = function(errorMessage) {
  return this.update({
    status: 'failed',
    errorMessage,
    deliveryAttempts: this.deliveryAttempts + 1,
    lastAttemptAt: new Date()
  });
};

Notification.prototype.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

Notification.prototype.shouldBeSent = function() {
  const now = new Date();
  return this.status === 'pending' &&
         (!this.scheduledAt || this.scheduledAt <= now) &&
         (!this.expiresAt || this.expiresAt > now);
};

// Static methods
Notification.createBookingNotification = function(userId, bookingId, type, title, message, data = {}) {
  return this.create({
    userId,
    bookingId,
    type,
    title,
    message,
    data,
    priority: type === 'reminder' ? 'high' : 'medium'
  });
};

Notification.createPaymentNotification = function(userId, paymentId, type, title, message, data = {}) {
  return this.create({
    userId,
    paymentId,
    type,
    title,
    message,
    data,
    priority: 'medium'
  });
};

Notification.createSystemNotification = function(userId, title, message, priority = 'medium', data = {}) {
  return this.create({
    userId,
    type: 'system',
    title,
    message,
    data,
    priority
  });
};

module.exports = Notification;
