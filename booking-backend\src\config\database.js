/**
 * Database Configuration for Sequelize
 * Supports multiple environments and alter table functionality
 */

const config = require('./environment');

const databaseConfig = {
  development: {
    username: config.database.username,
    password: config.database.password,
    database: config.database.database,
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    logging: config.database.logging,
    
    // Connection Pool
    pool: config.database.pool,
    
    // Timezone
    timezone: '+07:00', // Vietnam timezone
    
    // Sequelize Options
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    
    // Alter Table Configuration
    alter: config.database.alter,
    
    // Sync Configuration
    sync: config.database.sync,
    
    // Query Options
    query: {
      raw: false,
      nest: true
    },
    
    // Retry Configuration
    retry: {
      max: 3,
      match: [
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'EHOSTUNREACH',
        'ER_LOCK_DEADLOCK',
        'ER_LOCK_WAIT_TIMEOUT'
      ]
    }
  },

  test: {
    username: config.database.username,
    password: config.database.password,
    database: config.database.database + '_test',
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    logging: false,
    
    pool: {
      max: 5,
      min: 1,
      acquire: 30000,
      idle: 10000
    },
    
    timezone: '+07:00',
    
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    
    // Disable alter table in test
    alter: {
      enabled: false,
      drop: false,
      force: false
    },
    
    sync: {
      force: true, // Always recreate tables in test
      alter: false
    }
  },

  production: {
    username: config.database.username,
    password: config.database.password,
    database: config.database.database,
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    logging: false,
    
    pool: {
      max: config.database.pool.max,
      min: config.database.pool.min,
      acquire: config.database.pool.acquire,
      idle: config.database.pool.idle
    },
    
    timezone: '+07:00',
    
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    
    // Disable alter table in production for safety
    alter: {
      enabled: false,
      drop: false,
      force: false
    },
    
    sync: {
      force: false,
      alter: false
    },
    
    // SSL Configuration for production
    dialectOptions: {
      ssl: process.env.DB_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false,
      charset: 'utf8mb4'
    },
    
    retry: {
      max: 5,
      match: [
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'EHOSTUNREACH',
        'ER_LOCK_DEADLOCK',
        'ER_LOCK_WAIT_TIMEOUT'
      ]
    }
  }
};

// Get current environment config
const currentEnv = config.app.env || 'development';
const currentConfig = databaseConfig[currentEnv];

if (!currentConfig) {
  throw new Error(`Database configuration not found for environment: ${currentEnv}`);
}

module.exports = {
  ...databaseConfig,
  current: currentConfig
};
