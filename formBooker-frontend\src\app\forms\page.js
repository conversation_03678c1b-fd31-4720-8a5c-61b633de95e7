'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DashboardLayout from '@/components/layout/DashboardLayout';
import FormsService from '@/services/formsService';
import { 
  Plus, 
  Edit, 
  Copy, 
  Settings, 
  BarChart3, 
  Link as LinkIcon,
  Code,
  MoreVertical,
  Eye,
  Trash2
} from 'lucide-react';

/**
 * Forms List Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function FormsPage() {
  const [copiedItem, setCopiedItem] = useState(null);
  const [forms, setForms] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load forms on component mount
  useEffect(() => {
    loadForms();
  }, []);

  const loadForms = async () => {
    try {
      setIsLoading(true);
      const result = await FormsService.getUserForms({ limit: 50 });
      
      if (result.success) {
        setForms(result.data.forms || []);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Error loading forms:', error);
      setError('Failed to load forms');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = async (text, type, formId) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(`${type}-${formId}`);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Active':
        return 'badge-success';
      case 'Draft':
        return 'badge-warning';
      case 'Inactive':
        return 'badge-neutral';
      default:
        return 'badge-neutral';
    }
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-text">Your Booking Forms</h1>
              <p className="mt-2 text-neutral-600">
                Create and manage your booking forms
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Link 
                href="/forms/new"
                className="btn-primary inline-flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Form
              </Link>
            </div>
          </div>

          {/* Forms List */}
          <div className="space-y-6">
            {forms.length > 0 ? (
              forms.map((form) => (
                <div key={form.id} className="card">
                  <div className="card-body">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      {/* Form Info */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-text mb-1">
                              {form.name}
                            </h3>
                            <div className="flex items-center space-x-4 text-sm text-neutral-600">
                              <span>Service: {form.service?.name || 'No service'}</span>
                              <span>•</span>
                              <span>Branch: {form.branch?.name || 'No branch'}</span>
                              <span>•</span>
                              <span>Created: {form.createdAt || form.created_at || 'Unknown'}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`badge ${getStatusBadge(form.status)}`}>
                              {form.status}
                            </span>
                            <Link 
                              href={`/forms/edit/${form.id}`}
                              className="btn-outline btn-sm"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Link>
                          </div>
                        </div>

                        {/* Enhanced Sharing Options */}
                        <div className="space-y-4 mb-4">
                          {/* Primary Sharing Options */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Direct Link */}
                            <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                              <div className="flex items-center">
                                <LinkIcon className="h-4 w-4 text-primary-600 mr-2" />
                                <span className="text-sm font-medium text-neutral-700">
                                  🔗 Direct Link
                                </span>
                              </div>
                              <button
                                onClick={() => handleCopy(form.publicUrl || form.standaloneLink, 'link', form.id)}
                                className="btn-outline btn-sm"
                              >
                                {copiedItem === `link-${form.id}` ? (
                                  <span className="text-secondary-600">Copied!</span>
                                ) : (
                                  <>
                                    <Copy className="h-4 w-4 mr-1" />
                                    Copy
                                  </>
                                )}
                              </button>
                            </div>

                            {/* Standard Embed Code */}
                            <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 text-accent-600 mr-2" />
                                <span className="text-sm font-medium text-neutral-700">
                                  📋 Embed Code
                                </span>
                              </div>
                              <button
                                onClick={() => handleCopy(form.embedCode, 'embed', form.id)}
                                className="btn-outline btn-sm"
                              >
                                {copiedItem === `embed-${form.id}` ? (
                                  <span className="text-secondary-600">Copied!</span>
                                ) : (
                                  <>
                                    <Copy className="h-4 w-4 mr-1" />
                                    Copy
                                  </>
                                )}
                              </button>
                            </div>
                          </div>

                          {/* QR Code and Social Sharing */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {/* QR Code */}
                            {form.qrCodeUrl && (
                              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                <div className="flex items-center">
                                  <div className="w-4 h-4 bg-purple-600 rounded mr-2"></div>
                                  <span className="text-sm font-medium text-purple-700">
                                    📱 QR Code
                                  </span>
                                </div>
                                <button
                                  onClick={() => window.open(form.qrCodeUrl, '_blank')}
                                  className="btn-outline btn-sm text-purple-600 border-purple-300 hover:bg-purple-50"
                                >
                                  View
                                </button>
                              </div>
                            )}

                            {/* Social Media Sharing */}
                            {form.socialUrls && (
                              <>
                                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                  <div className="flex items-center">
                                    <div className="w-4 h-4 bg-blue-600 rounded mr-2"></div>
                                    <span className="text-sm font-medium text-blue-700">
                                      📘 Facebook
                                    </span>
                                  </div>
                                  <button
                                    onClick={() => window.open(form.socialUrls.facebook, '_blank')}
                                    className="btn-outline btn-sm text-blue-600 border-blue-300 hover:bg-blue-50"
                                  >
                                    Share
                                  </button>
                                </div>

                                <div className="flex items-center justify-between p-3 bg-sky-50 rounded-lg">
                                  <div className="flex items-center">
                                    <div className="w-4 h-4 bg-sky-500 rounded mr-2"></div>
                                    <span className="text-sm font-medium text-sky-700">
                                      🐦 Twitter
                                    </span>
                                  </div>
                                  <button
                                    onClick={() => window.open(form.socialUrls.twitter, '_blank')}
                                    className="btn-outline btn-sm text-sky-600 border-sky-300 hover:bg-sky-50"
                                  >
                                    Tweet
                                  </button>
                                </div>
                              </>
                            )}
                          </div>

                          {/* Advanced Embed Options */}
                          {form.embedCodes && (
                            <div className="border-t border-neutral-200 pt-4">
                              <details className="group">
                                <summary className="flex items-center justify-between cursor-pointer text-sm font-medium text-neutral-700 hover:text-neutral-900">
                                  <span>⚙️ Advanced Embed Options</span>
                                  <span className="group-open:rotate-180 transition-transform">▼</span>
                                </summary>
                                <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>Compact (400px)</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.compact, 'compact-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `compact-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                  
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>Full Height (800px)</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.fullHeight, 'full-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `full-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                  
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>Mobile Optimized</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.mobile, 'mobile-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `mobile-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                  
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>Styled with Shadow</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.styled, 'styled-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `styled-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                  
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>JavaScript Dynamic</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.javascript, 'js-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `js-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                  
                                  <div className="flex items-center justify-between p-2 bg-neutral-50 rounded text-xs">
                                    <span>Popup Button</span>
                                    <button
                                      onClick={() => handleCopy(form.embedCodes.button, 'button-embed', form.id)}
                                      className="btn-outline btn-xs"
                                    >
                                      {copiedItem === `button-embed-${form.id}` ? 'Copied!' : 'Copy'}
                                    </button>
                                  </div>
                                </div>
                              </details>
                            </div>
                          )}
                        </div>

                        {/* Stats */}
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="flex items-center">
                            <BarChart3 className="h-4 w-4 text-secondary-600 mr-1" />
                            <span className="text-neutral-600">
                              📊 {form.bookingsThisMonth || 0} bookings this month
                            </span>
                          </div>
                          <div className="text-neutral-500">
                            Last booking: {form.lastBooking || 'No bookings yet'}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="mt-4 lg:mt-0 lg:ml-6 flex items-center space-x-2">
                        <Link
                          href={form.publicUrl || form.standaloneLink || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn-outline btn-sm"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Preview
                        </Link>
                        <button className="btn-ghost btn-sm p-2">
                          <Settings className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              /* Empty State */
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Plus className="h-12 w-12 text-neutral-400" />
                </div>
                <h3 className="text-xl font-semibold text-text mb-2">
                  No forms yet
                </h3>
                <p className="text-neutral-600 mb-6 max-w-md mx-auto">
                  Create your first booking form to start accepting appointments from customers.
                </p>
                <Link 
                  href="/forms/new"
                  className="btn-primary inline-flex items-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Form
                </Link>
              </div>
            )}
          </div>

          {/* Enhanced Quick Tips */}
          {forms.length > 0 && (
            <div className="mt-8">
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-semibold text-text">💡 Advanced Sharing Guide</h3>
                </div>
                <div className="card-body">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                    {/* Direct Links */}
                    <div>
                      <h4 className="font-medium text-text mb-3 flex items-center">
                        🔗 Direct Links
                      </h4>
                      <ul className="space-y-2 text-neutral-600">
                        <li>• Perfect for social media sharing</li>
                        <li>• Easy to share via email or SMS</li>
                        <li>• Works on any device instantly</li>
                        <li>• QR codes for mobile access</li>
                        <li>• Built-in tracking parameters</li>
                      </ul>
                    </div>
                    
                    {/* Embed Codes */}
                    <div>
                      <h4 className="font-medium text-text mb-3 flex items-center">
                        📋 Embed Codes
                      </h4>
                      <ul className="space-y-2 text-neutral-600">
                        <li>• Integrate directly into websites</li>
                        <li>• Multiple size options available</li>
                        <li>• Mobile-responsive designs</li>
                        <li>• JavaScript dynamic loading</li>
                        <li>• Popup button options</li>
                      </ul>
                    </div>
                    
                    {/* Social & Mobile */}
                    <div>
                      <h4 className="font-medium text-text mb-3 flex items-center">
                        📱 Social & Mobile
                      </h4>
                      <ul className="space-y-2 text-neutral-600">
                        <li>• One-click social media posting</li>
                        <li>• QR codes for instant scanning</li>
                        <li>• WhatsApp direct sharing</li>
                        <li>• LinkedIn professional networks</li>
                        <li>• Mobile-optimized experiences</li>
                      </ul>
                    </div>
                  </div>
                  
                  {/* Pro Tips */}
                  <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">🎯 Pro Tips</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                      <div>
                        <strong>For Websites:</strong> Use standard or styled embed codes for seamless integration
                      </div>
                      <div>
                        <strong>For Social Media:</strong> Use direct links with built-in social sharing buttons
                      </div>
                      <div>
                        <strong>For Print Materials:</strong> Generate QR codes for easy mobile access
                      </div>
                      <div>
                        <strong>For Email:</strong> Use compact embed or direct links for best compatibility
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
