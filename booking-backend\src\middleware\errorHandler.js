/**
 * Error Handling Middleware
 * Centralized error handling for the application
 */

const { ValidationError, DatabaseError, ConnectionError, TimeoutError } = require('sequelize');
const logger = require('../utils/logger');
const { ERROR_CODES, HTTP_STATUS } = require('../utils/constants');
const config = require('../config/environment');

/**
 * Main error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Ensure res object exists
  if (!res || typeof res.status !== 'function') {
    console.error('Error: res object is not defined or invalid in errorHandler');
    console.error('Error details:', err.message);
    return;
  }

  // Log error with context
  logger.errorWithContext(err, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user ? req.user.id : null,
    body: req.body,
    params: req.params,
    query: req.query
  });

  // Handle different types of errors
  if (err instanceof ValidationError) {
    return handleSequelizeValidationError(err, res);
  }

  if (err instanceof DatabaseError) {
    return handleSequelizeDatabaseError(err, res);
  }

  if (err instanceof ConnectionError || err instanceof TimeoutError) {
    return handleSequelizeConnectionError(err, res);
  }

  if (err.name === 'JsonWebTokenError') {
    return handleJWTError(err, res);
  }

  if (err.name === 'TokenExpiredError') {
    return handleJWTExpiredError(err, res);
  }

  if (err.name === 'MulterError') {
    return handleMulterError(err, res);
  }

  if (err.code === 'LIMIT_FILE_SIZE') {
    return handleFileSizeError(err, res);
  }

  if (err.type === 'entity.parse.failed') {
    return handleJSONParseError(err, res);
  }

  // Handle custom application errors
  if (err.isOperational) {
    return handleOperationalError(err, res);
  }

  // Handle unknown errors
  return handleUnknownError(err, res);
};

/**
 * Handle Sequelize validation errors
 */
const handleSequelizeValidationError = (err, res) => {
  const errors = err.errors.map(error => ({
    field: error.path,
    message: error.message,
    value: error.value,
    type: error.type
  }));

  return res.status(HTTP_STATUS.BAD_REQUEST).json({
    success: false,
    error: {
      code: ERROR_CODES.VALIDATION_ERROR,
      message: 'Validation failed',
      details: errors
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle Sequelize database errors
 */
const handleSequelizeDatabaseError = (err, res) => {
  let message = 'Database operation failed';
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;

  // Handle specific database errors
  if (err.name === 'SequelizeUniqueConstraintError') {
    message = 'Resource already exists';
    statusCode = HTTP_STATUS.CONFLICT;
  } else if (err.name === 'SequelizeForeignKeyConstraintError') {
    message = 'Referenced resource not found';
    statusCode = HTTP_STATUS.BAD_REQUEST;
  } else if (err.name === 'SequelizeValidationError') {
    return handleSequelizeValidationError(err, res);
  }

  return res.status(statusCode).json({
    success: false,
    error: {
      code: ERROR_CODES.DATABASE_ERROR,
      message: config.app.env === 'production' ? message : err.message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle Sequelize connection errors
 */
const handleSequelizeConnectionError = (err, res) => {
  return res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
    success: false,
    error: {
      code: ERROR_CODES.DATABASE_ERROR,
      message: 'Database connection failed'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle JWT errors
 */
const handleJWTError = (err, res) => {
  return res.status(HTTP_STATUS.UNAUTHORIZED).json({
    success: false,
    error: {
      code: ERROR_CODES.AUTHENTICATION_ERROR,
      message: 'Invalid authentication token'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle JWT expired errors
 */
const handleJWTExpiredError = (err, res) => {
  return res.status(HTTP_STATUS.UNAUTHORIZED).json({
    success: false,
    error: {
      code: ERROR_CODES.AUTHENTICATION_ERROR,
      message: 'Authentication token expired'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle Multer file upload errors
 */
const handleMulterError = (err, res) => {
  let message = 'File upload failed';

  if (err.code === 'LIMIT_FILE_SIZE') {
    message = 'File size too large';
  } else if (err.code === 'LIMIT_FILE_COUNT') {
    message = 'Too many files';
  } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    message = 'Unexpected file field';
  }

  return res.status(HTTP_STATUS.BAD_REQUEST).json({
    success: false,
    error: {
      code: ERROR_CODES.VALIDATION_ERROR,
      message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle file size errors
 */
const handleFileSizeError = (err, res) => {
  return res.status(HTTP_STATUS.BAD_REQUEST).json({
    success: false,
    error: {
      code: ERROR_CODES.VALIDATION_ERROR,
      message: 'File size exceeds limit'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle JSON parse errors
 */
const handleJSONParseError = (err, res) => {
  return res.status(HTTP_STATUS.BAD_REQUEST).json({
    success: false,
    error: {
      code: ERROR_CODES.VALIDATION_ERROR,
      message: 'Invalid JSON format'
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle operational errors (custom application errors)
 */
const handleOperationalError = (err, res) => {
  return res.status(err.statusCode || HTTP_STATUS.BAD_REQUEST).json({
    success: false,
    error: {
      code: err.code || ERROR_CODES.INTERNAL_ERROR,
      message: err.message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle unknown errors
 */
const handleUnknownError = (err, res) => {
  const message = config.app.env === 'production' 
    ? 'Something went wrong' 
    : err.message;

  return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
    success: false,
    error: {
      code: ERROR_CODES.INTERNAL_ERROR,
      message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Handle 404 Not Found
 */
const notFound = (req, res) => {
  return res.status(HTTP_STATUS.NOT_FOUND).json({
    success: false,
    error: {
      code: ERROR_CODES.NOT_FOUND,
      message: `Route ${req.originalUrl} not found`
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  });
};

/**
 * Custom error class for operational errors
 */
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

module.exports = {
  errorHandler,
  notFound,
  AppError
};
