{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.js", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}