/**
 * Create Test Data for Forms
 * Creates branch and service for the test user to enable form creation
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function createTestData() {
  console.log('🔧 Creating test data for forms...\n');

  try {
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const userId = loginResponse.data.data.user.id;
    console.log('✅ Login successful');
    console.log('User ID:', userId);
    console.log('Token:', token.substring(0, 50) + '...\n');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Get existing branches or create one
    console.log('2. Getting user branches...');
    try {
      const branchesResponse = await axios.get(`${API_BASE}/branches`, { headers });
      let branch;

      if (branchesResponse.data.data?.data?.length > 0) {
        branch = branchesResponse.data.data.data[0];
        console.log('✅ Using existing branch');
        console.log('Branch ID:', branch.id);
        console.log('Branch Name:', branch.name, '\n');
      } else {
        // Create new branch with unique name
        const branchData = {
          name: `Test Branch ${Date.now()}`,
          address: '123 Test Street',
          phone: '0999888777',
          city: 'Ho Chi Minh City',
          district: 'District 1',
          ward: 'Ward 1',
          description: 'Test branch for form creation'
        };

        const branchResponse = await axios.post(`${API_BASE}/branches`, branchData, { headers });
        branch = branchResponse.data.data;
        console.log('✅ Branch created successfully');
        console.log('Branch ID:', branch.id);
        console.log('Branch Name:', branch.name, '\n');
      }

      // Step 3: Create a service
      console.log('3. Creating service...');
      const serviceData = {
        name: 'Test Service',
        description: 'Test service for form creation',
        duration: 60,
        price: 100000,
        branchId: branch.id,
        category: 'hair_care'
      };

      const serviceResponse = await axios.post(`${API_BASE}/services`, serviceData, { headers });
      const service = serviceResponse.data.data;
      console.log('✅ Service created successfully');
      console.log('Service ID:', service.id);
      console.log('Service Name:', service.name, '\n');

      // Step 4: Test form creation
      console.log('4. Testing form creation...');
      const formData = {
        name: 'Test Booking Form',
        serviceId: service.id,
        branchId: branch.id,
        status: 'active',
        fieldsConfig: {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        brandingConfig: {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        }
      };

      const formResponse = await axios.post(`${API_BASE}/forms`, formData, { headers });
      const form = formResponse.data.data;
      console.log('✅ Form created successfully!');
      console.log('Form ID:', form.id);
      console.log('Form Name:', form.name);
      console.log('Form Slug:', form.slug);
      console.log('Public URL:', form.publicUrl);

      console.log('\n🎉 Test data created successfully!');
      console.log('You can now test form creation on the frontend with:');
      console.log(`- Branch ID: ${branch.id}`);
      console.log(`- Service ID: ${service.id}`);

    } catch (error) {
      console.log('❌ Error creating branch/service:', error.response?.data?.error?.message || error.message);
      console.log('Status:', error.response?.status);
      if (error.response?.data) {
        console.log('Full error:', error.response.data);
      }
    }

  } catch (error) {
    console.error('❌ Login failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the script
createTestData();
