/**
 * Payment Model
 * Payment management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Payment = sequelize.define('Payment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'Unique payment identifier'
  },
  paymentCode: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'payment_code',
    comment: 'Unique payment code for reference'
  },
  bookingId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'booking_id',
    references: {
      model: 'bookings',
      key: 'id'
    },
    comment: 'Associated booking ID'
  },
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'customer_id',
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'Customer who made the payment'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Payment amount'
  },
  method: {
    type: DataTypes.ENUM('cash', 'card', 'bank_transfer', 'e_wallet', 'credit'),
    allowNull: false,
    comment: 'Payment method used'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
    defaultValue: 'pending',
    comment: 'Payment status'
  },
  transactionId: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'transaction_id',
    comment: 'External transaction ID from payment gateway'
  },
  gatewayResponse: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'gateway_response',
    comment: 'Response from payment gateway'
  },
  paymentDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'payment_date',
    comment: 'When the payment was made'
  },
  dueDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'due_date',
    comment: 'Payment due date'
  },
  description: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Payment description'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional payment notes'
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    field: 'refund_amount',
    comment: 'Amount refunded'
  },
  refundReason: {
    type: DataTypes.STRING(500),
    allowNull: true,
    field: 'refund_reason',
    comment: 'Reason for refund'
  },
  refundedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'refunded_at',
    comment: 'When the refund was processed'
  },
  refundedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'refunded_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who processed the refund'
  },
  processedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'processed_by',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Staff who processed the payment'
  },
  processedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'processed_at',
    comment: 'When the payment was processed'
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'VND',
    comment: 'Payment currency'
  },
  fees: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    comment: 'Transaction fees'
  },
  netAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    field: 'net_amount',
    comment: 'Net amount after fees'
  },
  isPartialPayment: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'is_partial_payment',
    comment: 'Whether this is a partial payment'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata for payment'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: 'Whether the payment record is active'
  }
}, {
  tableName: 'payments',
  timestamps: true,
  underscored: true,
  paranoid: true,
  indexes: [
    {
      fields: ['booking_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['payment_code'],
      unique: true
    },
    {
      fields: ['status']
    },
    {
      fields: ['method']
    },
    {
      fields: ['payment_date']
    },
    {
      fields: ['transaction_id']
    }
  ]
});

// Define associations
Payment.associate = (models) => {
  // Payment belongs to Booking
  Payment.belongsTo(models.bookings, {
    foreignKey: 'booking_id',
    as: 'booking'
  });

  // Payment belongs to Customer
  Payment.belongsTo(models.customers, {
    foreignKey: 'customer_id',
    as: 'customer'
  });

  // Payment belongs to User (who processed)
  Payment.belongsTo(models.users, {
    foreignKey: 'processed_by',
    as: 'processedByUser'
  });

  // Payment belongs to User (who refunded)
  Payment.belongsTo(models.users, {
    foreignKey: 'refunded_by',
    as: 'refundedByUser'
  });

  // Payment has many Notifications
  Payment.hasMany(models.notifications, {
    foreignKey: 'payment_id',
    as: 'notifications'
  });
};

// Instance methods
Payment.prototype.generatePaymentCode = function() {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `PAY${date}${random}`;
};

Payment.prototype.canBeRefunded = function() {
  return ['completed'].includes(this.status) && this.refundAmount < this.amount;
};

Payment.prototype.canBeCancelled = function() {
  return ['pending', 'processing'].includes(this.status);
};

Payment.prototype.getRemainingAmount = function() {
  return this.amount - this.refundAmount;
};

// Static methods
Payment.generatePaymentCode = function() {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `PAY${date}${random}`;
};

module.exports = Payment;
