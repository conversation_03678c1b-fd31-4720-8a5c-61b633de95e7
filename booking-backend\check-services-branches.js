const axios = require('axios');

async function checkServicesAndBranches() {
  try {
    console.log('🔍 Checking Services and Branches for Admin User...\n');
    
    // Login first
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>', 
      password: 'admin123456'
    });
    
    if (!loginRes.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const token = loginRes.data.data.token;
    console.log('✅ Login successful\n');
    
    // Check services
    console.log('📋 Checking Services:');
    const servicesRes = await axios.get('http://localhost:3000/api/services', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const services = servicesRes.data.data || [];
    console.log(`Found ${services.length} services:`);
    services.forEach((service, index) => {
      console.log(`  ${index + 1}. ${service.name} (ID: ${service.id}) - Branch ID: ${service.branch_id}`);
    });
    
    // Check branches
    console.log('\n📋 Checking Branches:');
    const branchesRes = await axios.get('http://localhost:3000/api/branches', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const branches = branchesRes.data.data || [];
    console.log(`Found ${branches.length} branches:`);
    branches.forEach((branch, index) => {
      console.log(`  ${index + 1}. ${branch.name} (ID: ${branch.id}) - Manager: ${branch.manager_id}`);
    });
    
    // Find matching service and branch
    console.log('\n🔍 Finding compatible Service-Branch pairs:');
    let foundCompatible = false;
    
    for (const service of services) {
      const compatibleBranch = branches.find(b => b.id === service.branch_id);
      if (compatibleBranch) {
        console.log(`✅ Compatible pair found:`);
        console.log(`   Service: ${service.name} (ID: ${service.id})`);
        console.log(`   Branch: ${compatibleBranch.name} (ID: ${compatibleBranch.id})`);
        foundCompatible = true;
        break;
      }
    }
    
    if (!foundCompatible) {
      console.log('❌ No compatible service-branch pairs found');
      console.log('💡 You need to create services that belong to branches managed by this admin user');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

checkServicesAndBranches();
