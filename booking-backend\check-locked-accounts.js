/**
 * Check and Unlock User Accounts <PERSON><PERSON><PERSON>
 * Diagnose and fix 423 Locked authentication errors
 */

const { sequelize } = require('./src/database/connection');
const User = require('./src/modules/auth/model');

async function checkLockedAccounts() {
  try {
    console.log('🔍 Checking for locked user accounts...\n');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Get all users with their lock status
    const users = await User.findAll({
      attributes: [
        'id', 'email', 'name', 'role', 'isActive', 
        'loginAttempts', 'lockUntil', 'lastLogin', 'createdAt'
      ]
    });

    console.log(`\n📊 Found ${users.length} users in database:\n`);
    
    let lockedUsers = [];
    let usersWithAttempts = [];
    
    users.forEach((user, index) => {
      const isCurrentlyLocked = user.lockUntil && user.lockUntil > new Date();
      const hasLoginAttempts = user.loginAttempts > 0;
      
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Active: ${user.isActive}`);
      console.log(`   Login Attempts: ${user.loginAttempts}`);
      console.log(`   Lock Until: ${user.lockUntil ? user.lockUntil.toISOString() : 'Not locked'}`);
      console.log(`   Currently Locked: ${isCurrentlyLocked ? '🔒 YES' : '✅ No'}`);
      console.log(`   Last Login: ${user.lastLogin ? user.lastLogin.toISOString() : 'Never'}`);
      console.log('');
      
      if (isCurrentlyLocked) {
        lockedUsers.push(user);
      }
      
      if (hasLoginAttempts) {
        usersWithAttempts.push(user);
      }
    });

    // Summary
    console.log('📋 SUMMARY:');
    console.log(`   Total Users: ${users.length}`);
    console.log(`   Currently Locked: ${lockedUsers.length}`);
    console.log(`   Users with Failed Attempts: ${usersWithAttempts.length}`);

    if (lockedUsers.length > 0) {
      console.log('\n🔒 LOCKED ACCOUNTS:');
      lockedUsers.forEach(user => {
        const timeRemaining = Math.ceil((user.lockUntil - new Date()) / (1000 * 60));
        console.log(`   - ${user.email} (${user.role}) - Locked for ${timeRemaining} more minutes`);
      });
    }

    if (usersWithAttempts.length > 0) {
      console.log('\n⚠️  ACCOUNTS WITH FAILED ATTEMPTS:');
      usersWithAttempts.forEach(user => {
        console.log(`   - ${user.email} (${user.role}) - ${user.loginAttempts} attempts`);
      });
    }

    // Offer to unlock accounts
    if (lockedUsers.length > 0 || usersWithAttempts.length > 0) {
      console.log('\n🔧 UNLOCK OPTIONS:');
      console.log('1. Unlock all accounts and reset login attempts');
      console.log('2. Unlock specific account');
      console.log('3. Wait for automatic unlock (30 minutes from lock time)');
      
      // Auto-unlock all accounts for testing
      console.log('\n🚀 Auto-unlocking all accounts for testing...');
      
      const unlockPromises = users.map(async (user) => {
        if (user.loginAttempts > 0 || user.lockUntil) {
          await user.update({
            loginAttempts: 0,
            lockUntil: null
          });
          return user.email;
        }
        return null;
      });
      
      const unlockedEmails = (await Promise.all(unlockPromises)).filter(email => email);
      
      if (unlockedEmails.length > 0) {
        console.log('✅ Successfully unlocked accounts:');
        unlockedEmails.forEach(email => {
          console.log(`   - ${email}`);
        });
      } else {
        console.log('ℹ️  No accounts needed unlocking');
      }
    } else {
      console.log('\n✅ No locked accounts found - all accounts are accessible');
    }

    // Test credentials reminder
    console.log('\n📋 TEST CREDENTIALS:');
    console.log('Admin Account:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123456');
    console.log('  Role: admin');
    console.log('');
    console.log('Customer Account:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: password123');
    console.log('  Role: customer');
    console.log('');
    console.log('🔧 Account Locking Rules:');
    console.log('  - Max failed attempts: 5');
    console.log('  - Lock duration: 30 minutes');
    console.log('  - Automatic unlock after lock period expires');

  } catch (error) {
    console.error('❌ Error checking locked accounts:', error.message);
    console.error(error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run the check
checkLockedAccounts();
