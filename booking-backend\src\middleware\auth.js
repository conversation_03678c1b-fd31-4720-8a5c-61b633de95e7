/**
 * Authentication Middleware
 * JWT-based authentication and authorization
 */

const jwt = require('jsonwebtoken');
const { unauthorizedResponse, forbiddenResponse } = require('../utils/response');
const authConfig = require('../config/auth');
const logger = require('../utils/logger');

/**
 * Authentication middleware
 * Verifies JWT token and sets req.user
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (!token) {
      logger.auth('authentication_failed', null, false, {
        reason: 'missing_token',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return unauthorizedResponse(res, 'Access token required');
    }

    // Verify token
    const decoded = jwt.verify(token, authConfig.jwt.secret, authConfig.jwt.options);
    
    // TODO: Get user from database
    // For now, we'll use the decoded token data
    // In a real implementation, you would fetch the user from database
    // const user = await User.findByPk(decoded.id, {
    //   attributes: { exclude: ['password'] }
    // });
    
    // if (!user || !user.isActive) {
    //   logger.auth('authentication_failed', decoded.id, false, {
    //     reason: 'user_not_found_or_inactive',
    //     ip: req.ip
    //   });
    //   return unauthorizedResponse(res, 'Invalid or inactive user');
    // }

    // For now, create user object from token
    const user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      name: decoded.name,
      isActive: true
    };

    req.user = user;
    
    logger.auth('authentication_success', user.id, true, {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    logger.auth('authentication_failed', null, false, {
      reason: 'invalid_token',
      error: error.message,
      ip: req.ip
    });

    if (error.name === 'TokenExpiredError') {
      return unauthorizedResponse(res, 'Authentication token expired');
    } else if (error.name === 'JsonWebTokenError') {
      return unauthorizedResponse(res, 'Invalid authentication token');
    } else {
      return unauthorizedResponse(res, 'Authentication failed');
    }
  }
};

/**
 * Authorization middleware factory
 * Checks if user has required roles or permissions
 * @param {Array|string} roles - Required roles
 * @param {Array|string} permissions - Required permissions (optional)
 * @returns {Function} Middleware function
 */
const authorize = (roles = [], permissions = []) => {
  return (req, res, next) => {
    if (!req.user) {
      logger.security('authorization_failed', {
        reason: 'no_user_in_request',
        ip: req.ip,
        url: req.originalUrl
      });
      return unauthorizedResponse(res, 'Authentication required');
    }

    const userRole = req.user.role;
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

    // Check roles
    if (requiredRoles.length > 0 && !requiredRoles.includes(userRole)) {
      logger.security('authorization_failed', {
        userId: req.user.id,
        userRole,
        requiredRoles,
        reason: 'insufficient_role',
        ip: req.ip,
        url: req.originalUrl
      });
      return forbiddenResponse(res, 'Insufficient permissions');
    }

    // Check permissions (if specified)
    if (requiredPermissions.length > 0) {
      const hasPermission = requiredPermissions.some(permission => 
        authConfig.hasPermission(userRole, permission)
      );

      if (!hasPermission) {
        logger.security('authorization_failed', {
          userId: req.user.id,
          userRole,
          requiredPermissions,
          reason: 'insufficient_permissions',
          ip: req.ip,
          url: req.originalUrl
        });
        return forbiddenResponse(res, 'Insufficient permissions');
      }
    }

    logger.security('authorization_success', {
      userId: req.user.id,
      userRole,
      requiredRoles,
      requiredPermissions,
      ip: req.ip,
      url: req.originalUrl
    });

    next();
  };
};

/**
 * Optional authentication middleware
 * Sets req.user if token is valid, but doesn't require authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (token) {
      const decoded = jwt.verify(token, authConfig.jwt.secret, authConfig.jwt.options);
      
      // TODO: Get user from database
      const user = {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
        name: decoded.name,
        isActive: true
      };

      req.user = user;
    }

    next();
  } catch (error) {
    // Ignore authentication errors in optional auth
    next();
  }
};

/**
 * Resource ownership middleware
 * Checks if user owns the resource or has admin/staff role
 * @param {string} resourceIdParam - Parameter name for resource ID
 * @param {string} ownerField - Field name for owner ID (default: 'userId')
 * @returns {Function} Middleware function
 */
const checkResourceOwnership = (resourceIdParam = 'id', ownerField = 'userId') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return unauthorizedResponse(res, 'Authentication required');
      }

      const userRole = req.user.role;
      const userId = req.user.id;

      // Admin and staff can access any resource
      if (userRole === 'admin' || userRole === 'staff') {
        return next();
      }

      // For customers, check ownership
      const resourceId = req.params[resourceIdParam];
      
      // TODO: Implement actual resource ownership check
      // This would require fetching the resource from database
      // and checking if the ownerField matches the current user ID
      
      // For now, just check if the resource ID matches user ID
      // This is a simplified implementation
      if (resourceIdParam === 'id' && resourceId == userId) {
        return next();
      }

      logger.security('resource_access_denied', {
        userId,
        userRole,
        resourceId,
        resourceIdParam,
        ownerField,
        ip: req.ip,
        url: req.originalUrl
      });

      return forbiddenResponse(res, 'Access denied to this resource');
    } catch (error) {
      logger.error('Resource ownership check failed:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Authorization check failed'
        }
      });
    }
  };
};

/**
 * Rate limiting for authentication endpoints
 * @param {Object} options - Rate limiting options
 * @returns {Function} Middleware function
 */
const authRateLimit = (options = {}) => {
  const rateLimit = require('express-rate-limit');
  
  const defaultOptions = {
    windowMs: authConfig.rateLimiting.login.windowMs,
    max: authConfig.rateLimiting.login.max,
    message: {
      success: false,
      error: {
        code: 'TOO_MANY_REQUESTS',
        message: authConfig.rateLimiting.login.message
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.security('rate_limit_exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl
      });
      
      res.status(429).json(options.message || defaultOptions.message);
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
};

/**
 * Generate JWT token
 * @param {Object} payload - Token payload
 * @param {Object} options - Token options
 * @returns {string} JWT token
 */
const generateToken = (payload, options = {}) => {
  const tokenOptions = {
    ...authConfig.jwt.options,
    expiresIn: options.expiresIn || authConfig.jwt.expiresIn,
    ...options
  };

  return jwt.sign(payload, authConfig.jwt.secret, tokenOptions);
};

/**
 * Generate refresh token
 * @param {Object} payload - Token payload
 * @returns {string} Refresh token
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(
    payload, 
    authConfig.jwt.refreshSecret, 
    {
      ...authConfig.jwt.refreshOptions,
      expiresIn: authConfig.jwt.refreshExpiresIn
    }
  );
};

/**
 * Verify refresh token
 * @param {string} token - Refresh token
 * @returns {Object} Decoded token
 */
const verifyRefreshToken = (token) => {
  return jwt.verify(token, authConfig.jwt.refreshSecret, authConfig.jwt.refreshOptions);
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  checkResourceOwnership,
  authRateLimit,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken
};
