/**
 * Main Application File
 * Express.js application setup and configuration
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');

// Import configurations
const config = require('./config/environment');
const { swaggerSpec } = require('./config/swagger');

// Import middleware
const { errorHandler, notFound } = require('./middleware/errorHandler');
const { generalLimiter } = require('./middleware/rateLimiter');
const { sanitizeRequest } = require('./middleware/validation');

// Import utilities
const logger = require('./utils/logger');
const { sequelize, testConnection } = require('./database/connection');
// Load models and setup associations
require('./database/models');

// Create Express application
const app = express();

// Trust proxy (for accurate IP addresses behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: config.security.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

// Rate limiting
app.use('/api/', generalLimiter);

// Body parsing middleware
app.use(express.json({ 
  limit: '10mb',
  type: 'application/json'
}));
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb',
  type: 'application/x-www-form-urlencoded'
}));

// Compression middleware
app.use(compression());

// Request sanitization
app.use(sanitizeRequest);

// Logging middleware
if (config.app.env === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Custom request logging
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.request(req, res, duration);
  });
  
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check database connection
    const dbHealth = await sequelize.authenticate()
      .then(() => ({ status: 'healthy', timestamp: new Date().toISOString() }))
      .catch(error => ({ status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() }));

    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.env,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: dbHealth
    };

    // Set status code based on overall health
    const statusCode = dbHealth.status === 'healthy' ? 200 : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: config.app.name,
    version: config.app.version,
    environment: config.app.env,
    documentation: `${config.app.url}/api-docs`,
    endpoints: {
      health: '/health',
      docs: '/api-docs',
      api: '/api'
    }
  });
});

// Swagger documentation (only in non-production)
if (config.app.env !== 'production') {
  const swaggerUi = require('swagger-ui-express');
  
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: `${config.app.name} API Documentation`,
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true
    }
  }));
  
  // Swagger JSON endpoint
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
}

// Static files (for uploads, etc.)
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api/auth', require('./modules/auth/route'));
app.use('/api/users', require('./modules/users/route'));
app.use('/api/branches', require('./modules/branches/route'));
app.use('/api/services', require('./modules/services/route'));
app.use('/api/employees', require('./modules/employees/route'));
app.use('/api/customers', require('./modules/customers/route'));
app.use('/api/bookings', require('./modules/bookings/route'));
app.use('/api/forms', require('./modules/forms/route'));
app.use('/api/payments', require('./modules/payments/route'));
app.use('/api/notifications', require('./modules/notifications/route'));

// Public routes (no authentication required)
app.use('/api/public', require('./routes/public'));

// TODO: Import and use other route modules
// app.use('/api/settings', require('./modules/settings/route'));
// app.use('/api/reports', require('./modules/reports/route'));

// Temporary placeholder routes
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working!',
    timestamp: new Date().toISOString()
  });
});

// 404 handler for undefined routes
app.use(notFound);

// Global error handler
app.use(errorHandler);

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close database connection
    await sequelize.close();
    logger.info('Database connection closed');
    
    // Close server
    if (app.server) {
      app.server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

module.exports = app;
