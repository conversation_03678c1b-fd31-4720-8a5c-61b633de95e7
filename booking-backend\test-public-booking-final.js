const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testPublicBooking() {
  console.log('🧪 Testing public booking endpoint...');
  
  const bookingData = {
    formSlug: 'test-public-form',
    customerName: '<PERSON>',
    phoneNumber: '0123456789',
    emailAddress: '<EMAIL>',
    preferredDate: '2025-06-20',
    preferredTime: '10:00',
    specialRequests: 'Test booking from script'
  };

  console.log('📝 Sending booking data:', JSON.stringify(bookingData, null, 2));

  try {
    const response = await axios.post(`${BASE_URL}/api/public/bookings`, bookingData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ SUCCESS:', response.status);
    console.log('📄 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ ERROR:', error.response?.status || 'No status');
    console.log('📄 Error response:', JSON.stringify(error.response?.data || error.message, null, 2));
    console.log('📄 Full error:', error.message);
  }
}

// Test form existence first
async function checkFormExists() {
  console.log('🔍 Checking if test form exists...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/public/forms/test-public-form`);
    console.log('✅ Form exists:', response.status);
    console.log('📄 Form data:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log('❌ Form not found:', error.response?.status || 'No status');
    console.log('📄 Error response:', JSON.stringify(error.response?.data || error.message, null, 2));
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting complete public booking test...\n');
  
  // First check if form exists
  const formExists = await checkFormExists();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  if (formExists) {
    await testPublicBooking();
  } else {
    console.log('⚠️ Cannot test booking - form does not exist');
  }
}

runTests().catch(console.error);
