/**
 * Server Configuration
 * HTTP server setup and startup
 */

const app = require('./app');
const config = require('./config/environment');
const logger = require('./utils/logger');
const { connectWithRetry } = require('./database/connection');
const DatabaseSyncService = require('./database/sync');

/**
 * Start the server
 */
const startServer = async () => {
  try {
    logger.info('Starting Spa Booking System Backend...');
    
    // Test database connection with retry
    logger.info('Connecting to database...');
    await connectWithRetry(5, 5000);
    logger.info('Database connected successfully');
    
    // Sync database (if enabled)
    if (config.database.sync.alter || config.database.sync.force) {
      logger.info('Syncing database...');
      await DatabaseSyncService.syncDatabase();
      logger.info('Database sync completed');
    }
    
    // Start HTTP server
    const port = config.app.port;
    const server = app.listen(port, () => {
      logger.info(`Server running on port ${port}`);
      logger.info(`Environment: ${config.app.env}`);
      logger.info(`API URL: ${config.app.url}`);
      
      if (config.app.env !== 'production') {
        logger.info(`API Documentation: ${config.app.url}/api-docs`);
        logger.info(`Health Check: ${config.app.url}/health`);
      }
    });
    
    // Store server instance for graceful shutdown
    app.server = server;
    
    // Server error handling
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }
      
      const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
      
      switch (error.code) {
        case 'EACCES':
          logger.error(`${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`${bind} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
    
    // Server listening event
    server.on('listening', () => {
      const addr = server.address();
      const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
      logger.info(`Server listening on ${bind}`);
    });
    
    return server;
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
