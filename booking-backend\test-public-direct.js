const axios = require('axios');

async function testPublicFormDirectly() {
  console.log('🧪 Testing Public Form API Directly...\n');
  
  try {
    const slug = 'final-test-form-1750059607031'; // Known slug from previous test
    
    console.log(`Testing public API with slug: ${slug}`);
    const publicRes = await axios.get(`http://localhost:3000/api/public/forms/${slug}`);
    
    if (publicRes.data.success) {
      console.log('✅ Public API works!');
      console.log('Form data structure:');
      console.log('  - id:', publicRes.data.data.id);
      console.log('  - name:', publicRes.data.data.name);
      console.log('  - businessName:', publicRes.data.data.businessName);
      console.log('  - service:', publicRes.data.data.service?.name);
      console.log('  - branch:', publicRes.data.data.branch?.name);
      console.log('  - fields:', Object.keys(publicRes.data.data.fields || {}));
      console.log('  - branding:', Object.keys(publicRes.data.data.branding || {}));
      
      console.log(`\n🌐 Frontend URL should work: http://localhost:4000/book/${slug}`);
      console.log('✅ The public booking form should now be accessible!');
    } else {
      console.log('❌ Public API failed:', publicRes.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testPublicFormDirectly();
