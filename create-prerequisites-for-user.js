/**
 * Create Prerequisites for User
 * Creates branch and service for the user to enable form creation
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function createPrerequisites() {
  console.log('🔧 Creating prerequisites for form creation...\n');

  try {
    // Step 1: Login
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log('✅ Login successful');
    console.log('User ID:', user.id);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Create a branch
    console.log('\n2. Creating branch...');
    const branchData = {
      name: `User Branch ${Date.now()}`,
      address: '123 Main Street',
      phone: '0987654321',
      city: 'Ho Chi Minh City',
      district: 'District 1',
      ward: 'Ward 1',
      description: 'User branch for form creation testing'
    };

    try {
      const branchResponse = await axios.post(`${API_BASE}/branches`, branchData, { headers });
      const branch = branchResponse.data.data;
      console.log('✅ Branch created successfully');
      console.log('Branch ID:', branch.id);
      console.log('Branch Name:', branch.name);

      // Step 3: Create a service for this branch
      console.log('\n3. Creating service...');
      const serviceData = {
        name: `Hair Service ${Date.now()}`,
        description: 'Professional hair cutting and styling service',
        duration: 60,
        price: 150000,
        category: 'hair_care',
        branchId: branch.id  // Link service to the branch
      };

      try {
        const serviceResponse = await axios.post(`${API_BASE}/services`, serviceData, { headers });
        const service = serviceResponse.data.data;
        console.log('✅ Service created successfully');
        console.log('Service ID:', service.id);
        console.log('Service Name:', service.name);
        console.log('Service Branch ID:', service.branchId || service.branch_id);

        // Step 4: Test form creation with the new data
        console.log('\n4. Testing form creation with new prerequisites...');
        const formData = {
          name: `Test Form ${Date.now()}`,
          serviceId: service.id,
          branchId: branch.id,
          status: 'active',
          fieldsConfig: {
            customerName: true,
            phoneNumber: true,
            emailAddress: true,
            preferredDate: true,
            preferredTime: true,
            specialRequests: true
          },
          brandingConfig: {
            primaryColor: '#3b82f6',
            logo: null,
            customMessage: null
          }
        };

        try {
          const formResponse = await axios.post(`${API_BASE}/forms`, formData, { headers });
          const form = formResponse.data.data;
          console.log('✅ Form created successfully!');
          console.log('Form ID:', form.id);
          console.log('Form Name:', form.name);
          console.log('Form Slug:', form.slug);
          console.log('Public URL:', form.publicUrl);

          console.log('\n🎉 All prerequisites created successfully!');
          console.log('\n📋 Summary for frontend testing:');
          console.log(`- User Email: <EMAIL>`);
          console.log(`- Branch ID: ${branch.id} (${branch.name})`);
          console.log(`- Service ID: ${service.id} (${service.name})`);
          console.log(`- Form ID: ${form.id} (${form.name})`);
          console.log('\n✅ User can now create forms on the frontend!');

        } catch (formError) {
          console.log('❌ Form creation still failed:', formError.response?.data?.error?.message || formError.message);
          console.log('Status:', formError.response?.status);
          console.log('Full error:', formError.response?.data);
        }

      } catch (serviceError) {
        console.log('❌ Service creation failed:', serviceError.response?.data?.error?.message || serviceError.message);
        console.log('Status:', serviceError.response?.status);
        console.log('Full error:', serviceError.response?.data);
      }

    } catch (branchError) {
      console.log('❌ Branch creation failed:', branchError.response?.data?.error?.message || branchError.message);
      console.log('Status:', branchError.response?.status);
      console.log('Full error:', branchError.response?.data);
      
      // If branch already exists, try to get existing branches
      if (branchError.response?.status === 400) {
        console.log('\n🔄 Trying to get existing branches...');
        try {
          const existingBranchesResponse = await axios.get(`${API_BASE}/branches`, { headers });
          const existingBranches = existingBranchesResponse.data.data?.data || [];
          
          if (existingBranches.length > 0) {
            const existingBranch = existingBranches[0];
            console.log('✅ Found existing branch');
            console.log('Branch ID:', existingBranch.id);
            console.log('Branch Name:', existingBranch.name);
            
            // Try to create service with existing branch
            console.log('\n3. Creating service with existing branch...');
            const serviceData = {
              name: `Hair Service ${Date.now()}`,
              description: 'Professional hair cutting and styling service',
              duration: 60,
              price: 150000,
              category: 'hair_care',
              branchId: existingBranch.id
            };
            
            const serviceResponse = await axios.post(`${API_BASE}/services`, serviceData, { headers });
            const service = serviceResponse.data.data;
            console.log('✅ Service created with existing branch');
            console.log('Service ID:', service.id);
            console.log('Service Name:', service.name);
          }
        } catch (getError) {
          console.log('❌ Could not get existing branches:', getError.message);
        }
      }
    }

  } catch (error) {
    console.error('❌ Prerequisites creation failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the script
createPrerequisites();
