const axios = require('axios');

async function testPublicFormAPI() {
  console.log('🧪 Testing Public Form API...\n');
  
  try {
    // First, let's check if we have any forms with their slugs
    console.log('1. Testing login to get a form slug...');
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (!loginRes.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    console.log('✅ Login successful');
    const token = loginRes.data.data.token;
    
    // Get user's forms to find a slug
    console.log('\n2. Getting user forms to find slug...');
    const formsRes = await axios.get('http://localhost:3000/api/forms', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (formsRes.data.success && formsRes.data.data.length > 0) {
      const firstForm = formsRes.data.data[0];
      const slug = firstForm.slug;
      console.log(`✅ Found form with slug: ${slug}`);
      
      // Now test the public API
      console.log('\n3. Testing public API...');
      const publicRes = await axios.get(`http://localhost:3000/api/public/forms/${slug}`);
      
      if (publicRes.data.success) {
        console.log('✅ Public API works!');
        console.log('Response data:', JSON.stringify(publicRes.data.data, null, 2));
        
        // Test the URL that was failing
        console.log(`\n4. The URL that should work: http://localhost:4000/book/${slug}`);
        console.log('✅ This should now work in the frontend!');
      } else {
        console.log('❌ Public API failed');
      }
    } else {
      console.log('❌ No forms found');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    }
  }
}

testPublicFormAPI();
