const express = require('express');
const router = express.Router();
const FormsController = require('./controller');
const { authenticate } = require('../../middleware/auth');
const { validateRequest } = require('../../middleware/validation');

/**
 * Forms Routes
 * Handles all form-related endpoints
 */

// Protected routes (require authentication)
router.use(authenticate);

/**
 * @swagger
 * /api/forms:
 *   post:
 *     summary: Create a new booking form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - serviceId
 *               - branchId
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Hair Cut Booking Form"
 *               serviceId:
 *                 type: integer
 *                 example: 1
 *               branchId:
 *                 type: integer
 *                 example: 1
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *                 default: active
 *               fieldsConfig:
 *                 type: object
 *                 properties:
 *                   customerName:
 *                     type: boolean
 *                     default: true
 *                   phoneNumber:
 *                     type: boolean
 *                     default: true
 *                   emailAddress:
 *                     type: boolean
 *                     default: true
 *                   preferredDate:
 *                     type: boolean
 *                     default: true
 *                   preferredTime:
 *                     type: boolean
 *                     default: true
 *                   specialRequests:
 *                     type: boolean
 *                     default: true
 *               brandingConfig:
 *                 type: object
 *                 properties:
 *                   primaryColor:
 *                     type: string
 *                     default: "#3b82f6"
 *                   logo:
 *                     type: string
 *                     nullable: true
 *                   customMessage:
 *                     type: string
 *                     nullable: true
 *     responses:
 *       201:
 *         description: Form created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', FormsController.createForm);

/**
 * @swagger
 * /api/forms:
 *   get:
 *     summary: Get all forms for authenticated user
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, draft]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Forms retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', FormsController.getUserForms);

/**
 * @swagger
 * /api/forms/stats:
 *   get:
 *     summary: Get form statistics for authenticated user
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Form statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/stats', FormsController.getFormStats);

/**
 * @swagger
 * /api/forms/{id}:
 *   get:
 *     summary: Get form by ID
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Form retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Form not found
 */
router.get('/:id', FormsController.getFormById);

/**
 * @swagger
 * /api/forms/{id}:
 *   put:
 *     summary: Update form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               serviceId:
 *                 type: integer
 *               branchId:
 *                 type: integer
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *               fieldsConfig:
 *                 type: object
 *               brandingConfig:
 *                 type: object
 *     responses:
 *       200:
 *         description: Form updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Form not found
 */
router.put('/:id', FormsController.updateForm);

/**
 * @swagger
 * /api/forms/{id}:
 *   delete:
 *     summary: Delete form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Form deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Form not found
 */
router.delete('/:id', FormsController.deleteForm);

module.exports = router;
