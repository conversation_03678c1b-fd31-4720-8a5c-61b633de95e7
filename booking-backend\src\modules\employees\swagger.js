/**
 * Employees Swagger Documentation
 * API documentation for employee management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Employee:
 *       type: object
 *       required:
 *         - userId
 *         - employeeCode
 *         - position
 *         - startDate
 *         - contractType
 *         - branchId
 *       properties:
 *         id:
 *           type: integer
 *           description: Employee ID
 *         userId:
 *           type: integer
 *           description: Reference to User table
 *         employeeCode:
 *           type: string
 *           description: Unique employee code
 *           example: "EMP001"
 *         position:
 *           type: string
 *           description: Employee position/title
 *           example: "Senior Massage Therapist"
 *         department:
 *           type: string
 *           description: Department name
 *           example: "Massage Therapy"
 *         specializations:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of service specializations
 *           example: ["swedish_massage", "deep_tissue", "hot_stone"]
 *         qualifications:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of qualifications and certifications
 *           example: ["Certified Massage Therapist", "Aromatherapy Certificate"]
 *         experience:
 *           type: string
 *           description: Work experience description
 *           example: "5 years of experience in luxury spa environments"
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of skills and expertise
 *           example: ["Customer service", "Stress management", "Pain relief"]
 *         languages:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of spoken languages
 *           example: ["Vietnamese", "English", "Japanese"]
 *         workingHours:
 *           type: object
 *           description: Weekly working schedule
 *           example:
 *             monday: { isWorking: true, startTime: "08:00", endTime: "17:00" }
 *             tuesday: { isWorking: true, startTime: "08:00", endTime: "17:00" }
 *         hourlyRate:
 *           type: number
 *           format: decimal
 *           description: Hourly rate in VND
 *           example: 150000
 *         commissionRate:
 *           type: number
 *           format: decimal
 *           description: Commission rate percentage
 *           example: 15.5
 *         startDate:
 *           type: string
 *           format: date
 *           description: Employment start date
 *           example: "2023-01-15"
 *         endDate:
 *           type: string
 *           format: date
 *           description: Employment end date
 *           example: null
 *         contractType:
 *           type: string
 *           enum: [full_time, part_time, contract, intern]
 *           description: Employment contract type
 *           example: "full_time"
 *         status:
 *           type: string
 *           enum: [active, inactive, on_leave, terminated]
 *           description: Employee status
 *           example: "active"
 *         branchId:
 *           type: integer
 *           description: Primary branch assignment
 *           example: 1
 *         managerId:
 *           type: integer
 *           description: Direct manager employee ID
 *           example: 5
 *         emergencyContact:
 *           type: object
 *           description: Emergency contact information
 *           example:
 *             name: "John Doe"
 *             relationship: "Spouse"
 *             phone: "0987654321"
 *         notes:
 *           type: string
 *           description: Additional notes about employee
 *           example: "Excellent customer service skills"
 *         rating:
 *           type: number
 *           format: decimal
 *           description: Average customer rating
 *           example: 4.8
 *         totalBookings:
 *           type: integer
 *           description: Total number of completed bookings
 *           example: 150
 *         isAvailable:
 *           type: boolean
 *           description: Current availability status
 *           example: true
 *         user:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             email:
 *               type: string
 *             phone:
 *               type: string
 *         branch:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             city:
 *               type: string
 *             address:
 *               type: string
 *         manager:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             employeeCode:
 *               type: string
 *             position:
 *               type: string
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     
 *     EmployeeCreate:
 *       type: object
 *       required:
 *         - userId
 *         - employeeCode
 *         - position
 *         - startDate
 *         - contractType
 *         - branchId
 *       properties:
 *         userId:
 *           type: integer
 *           example: 10
 *         employeeCode:
 *           type: string
 *           example: "EMP001"
 *         position:
 *           type: string
 *           example: "Senior Massage Therapist"
 *         department:
 *           type: string
 *           example: "Massage Therapy"
 *         specializations:
 *           type: array
 *           items:
 *             type: string
 *           example: ["swedish_massage", "deep_tissue"]
 *         qualifications:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Certified Massage Therapist"]
 *         experience:
 *           type: string
 *           example: "5 years of experience in luxury spa environments"
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Customer service", "Stress management"]
 *         languages:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Vietnamese", "English"]
 *         workingHours:
 *           type: object
 *           example:
 *             monday: { isWorking: true, startTime: "08:00", endTime: "17:00" }
 *             tuesday: { isWorking: true, startTime: "08:00", endTime: "17:00" }
 *         hourlyRate:
 *           type: number
 *           format: decimal
 *           example: 150000
 *         commissionRate:
 *           type: number
 *           format: decimal
 *           example: 15.5
 *         startDate:
 *           type: string
 *           format: date
 *           example: "2023-01-15"
 *         endDate:
 *           type: string
 *           format: date
 *           example: null
 *         contractType:
 *           type: string
 *           enum: [full_time, part_time, contract, intern]
 *           example: "full_time"
 *         branchId:
 *           type: integer
 *           example: 1
 *         managerId:
 *           type: integer
 *           example: 5
 *         emergencyContact:
 *           type: object
 *           example:
 *             name: "John Doe"
 *             relationship: "Spouse"
 *             phone: "0987654321"
 *         notes:
 *           type: string
 *           example: "Excellent customer service skills"
 *     
 *     EmployeeUpdate:
 *       type: object
 *       properties:
 *         employeeCode:
 *           type: string
 *           example: "EMP002"
 *         position:
 *           type: string
 *           example: "Lead Massage Therapist"
 *         department:
 *           type: string
 *           example: "Wellness Department"
 *         specializations:
 *           type: array
 *           items:
 *             type: string
 *           example: ["swedish_massage", "deep_tissue", "hot_stone"]
 *         qualifications:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Certified Massage Therapist", "Advanced Certification"]
 *         experience:
 *           type: string
 *           example: "7 years of experience in luxury spa environments"
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Customer service", "Team leadership"]
 *         languages:
 *           type: array
 *           items:
 *             type: string
 *           example: ["Vietnamese", "English", "Japanese"]
 *         workingHours:
 *           type: object
 *           example:
 *             monday: { isWorking: true, startTime: "09:00", endTime: "18:00" }
 *         hourlyRate:
 *           type: number
 *           format: decimal
 *           example: 180000
 *         commissionRate:
 *           type: number
 *           format: decimal
 *           example: 20.0
 *         startDate:
 *           type: string
 *           format: date
 *           example: "2023-01-15"
 *         endDate:
 *           type: string
 *           format: date
 *           example: null
 *         contractType:
 *           type: string
 *           enum: [full_time, part_time, contract, intern]
 *           example: "full_time"
 *         status:
 *           type: string
 *           enum: [active, inactive, on_leave, terminated]
 *           example: "active"
 *         branchId:
 *           type: integer
 *           example: 2
 *         managerId:
 *           type: integer
 *           example: 3
 *         emergencyContact:
 *           type: object
 *           example:
 *             name: "Jane Doe"
 *             relationship: "Sister"
 *             phone: "0123456789"
 *         notes:
 *           type: string
 *           example: "Promoted to lead position"
 *         isAvailable:
 *           type: boolean
 *           example: true
 *     
 *     EmployeeStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of active employees
 *           example: 45
 *         available:
 *           type: integer
 *           description: Number of available employees
 *           example: 38
 *         averageRating:
 *           type: number
 *           format: decimal
 *           description: Average employee rating
 *           example: 4.6
 *         byStatus:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "active": 45
 *             "on_leave": 3
 *             "inactive": 2
 *         byBranch:
 *           type: object
 *           additionalProperties:
 *             type: integer
 *           example:
 *             "Spa Relax Nguyen Hue": 20
 *             "Spa Relax District 7": 15
 *             "Spa Relax Binh Thanh": 10
 */

/**
 * @swagger
 * /api/employees:
 *   get:
 *     summary: Get all employees with pagination and filters
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SortParam'
 *       - name: branchId
 *         in: query
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - name: status
 *         in: query
 *         schema:
 *           type: string
 *           enum: [active, inactive, on_leave, terminated]
 *         description: Filter by employee status
 *       - name: position
 *         in: query
 *         schema:
 *           type: string
 *         description: Filter by position
 *       - name: specialization
 *         in: query
 *         schema:
 *           type: string
 *         description: Filter by specialization
 *       - name: isAvailable
 *         in: query
 *         schema:
 *           type: boolean
 *         description: Filter by availability
 *       - $ref: '#/components/parameters/SearchParam'
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 message:
 *                   type: string
 *                   example: "Employees retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create new employee
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmployeeCreate'
 *     responses:
 *       201:
 *         description: Employee created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *                 message:
 *                   type: string
 *                   example: "Employee created successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/employees/stats:
 *   get:
 *     summary: Get employee statistics
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Employee statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/EmployeeStats'
 *                 message:
 *                   type: string
 *                   example: "Employee statistics retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/employees/available:
 *   get:
 *     summary: Get available employees
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: limit
 *         in: query
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 200
 *         description: Maximum number of employees to return
 *     responses:
 *       200:
 *         description: Available employees retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *                 message:
 *                   type: string
 *                   example: "Available employees retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/employees/{id}:
 *   get:
 *     summary: Get employee by ID
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *                 message:
 *                   type: string
 *                   example: "Employee retrieved successfully"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update employee
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmployeeUpdate'
 *     responses:
 *       200:
 *         description: Employee updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *                 message:
 *                   type: string
 *                   example: "Employee updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete employee (soft delete)
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/employees/{id}/status:
 *   patch:
 *     summary: Update employee status
 *     tags: [Employees]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, inactive, on_leave, terminated]
 *                 example: "active"
 *     responses:
 *       200:
 *         description: Employee status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *                 message:
 *                   type: string
 *                   example: "Employee status updated successfully"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

module.exports = {};
