/**
 * Customers Controller
 * HTTP request handlers for customer management
 */

const CustomersService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  paginatedResponse,
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const { getPaginationParams, buildSortOrder } = require('../../utils/pagination');
const logger = require('../../utils/logger');

class CustomersController {
  /**
   * Get all customers with pagination and filters
   * GET /api/customers
   */
  async getCustomers(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { page, limit } = getPaginationParams(req.query);
      const { membershipLevel, status, isVip, preferredBranchId, search, minSpent, maxSpent, sort } = req.query;

      const filters = { membershipLevel, status, isVip, preferredBranchId, search, minSpent, maxSpent };
      const pagination = { 
        page, 
        limit,
        order: buildSortOrder(sort, ['customerCode', 'membershipLevel', 'totalSpent', 'loyaltyPoints', 'createdAt'], [['customerCode', 'ASC']])
      };

      const result = await CustomersService.getCustomers(filters, pagination);

      logger.info('Customers retrieved successfully', {
        userId: req.user.id,
        filters,
        pagination: { page, limit },
        resultCount: result.data.length
      });

      return paginatedResponse(res, result.data, result.pagination, 'Customers retrieved successfully');

    } catch (error) {
      logger.error('Get customers controller error:', error);
      next(error);
    }
  }

  /**
   * Get customer by ID
   * GET /api/customers/:id
   */
  async getCustomerById(req, res, next) {
    try {
      const { id } = req.params;
      const customer = await CustomersService.getCustomerById(parseInt(id));

      logger.info('Customer retrieved successfully', {
        userId: req.user.id,
        customerId: id
      });

      return successResponse(res, customer, 'Customer retrieved successfully');

    } catch (error) {
      logger.error('Get customer by ID controller error:', error);
      next(error);
    }
  }

  /**
   * Create new customer
   * POST /api/customers
   */
  async createCustomer(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const customerData = req.body;
      const customer = await CustomersService.createCustomer(customerData);

      logger.info('Customer created successfully', {
        userId: req.user.id,
        customerId: customer.id,
        customerCode: customer.customerCode,
        membershipLevel: customer.membershipLevel
      });

      return createdResponse(res, customer, 'Customer created successfully');

    } catch (error) {
      logger.error('Create customer controller error:', error);
      next(error);
    }
  }

  /**
   * Update customer
   * PUT /api/customers/:id
   */
  async updateCustomer(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const customer = await CustomersService.updateCustomer(parseInt(id), updateData);

      logger.info('Customer updated successfully', {
        userId: req.user.id,
        customerId: id,
        updatedFields: Object.keys(updateData)
      });

      return successResponse(res, customer, 'Customer updated successfully');

    } catch (error) {
      logger.error('Update customer controller error:', error);
      next(error);
    }
  }

  /**
   * Delete customer
   * DELETE /api/customers/:id
   */
  async deleteCustomer(req, res, next) {
    try {
      const { id } = req.params;
      const result = await CustomersService.deleteCustomer(parseInt(id));

      logger.info('Customer deleted successfully', {
        userId: req.user.id,
        customerId: id
      });

      return successResponse(res, result, 'Customer deleted successfully');

    } catch (error) {
      logger.error('Delete customer controller error:', error);
      next(error);
    }
  }

  /**
   * Update customer status
   * PATCH /api/customers/:id/status
   */
  async updateCustomerStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { status } = req.body;

      const customer = await CustomersService.updateCustomerStatus(parseInt(id), status);

      logger.info('Customer status updated successfully', {
        userId: req.user.id,
        customerId: id,
        newStatus: status
      });

      return successResponse(res, customer, `Customer status updated to ${status} successfully`);

    } catch (error) {
      logger.error('Update customer status controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle VIP status
   * PATCH /api/customers/:id/vip
   */
  async toggleVipStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isVip } = req.body;

      const customer = await CustomersService.toggleVipStatus(parseInt(id), isVip);

      logger.info('Customer VIP status changed successfully', {
        userId: req.user.id,
        customerId: id,
        isVip
      });

      return successResponse(res, customer, `Customer ${isVip ? 'promoted to VIP' : 'removed from VIP'} successfully`);

    } catch (error) {
      logger.error('Toggle VIP status controller error:', error);
      next(error);
    }
  }

  /**
   * Get customers by membership level
   * GET /api/customers/membership/:level
   */
  async getCustomersByMembership(req, res, next) {
    try {
      const { level } = req.params;
      const { limit = 100 } = req.query;

      const customers = await CustomersService.getCustomersByMembership(level, { 
        limit: parseInt(limit)
      });

      logger.info('Customers by membership retrieved successfully', {
        userId: req.user.id,
        membershipLevel: level,
        resultCount: customers.length
      });

      return successResponse(res, customers, `${level} customers retrieved successfully`);

    } catch (error) {
      logger.error('Get customers by membership controller error:', error);
      next(error);
    }
  }

  /**
   * Get VIP customers
   * GET /api/customers/vip
   */
  async getVipCustomers(req, res, next) {
    try {
      const { limit = 50 } = req.query;
      const customers = await CustomersService.getVipCustomers({ 
        limit: parseInt(limit) 
      });

      logger.info('VIP customers retrieved successfully', {
        userId: req.user.id,
        resultCount: customers.length
      });

      return successResponse(res, customers, 'VIP customers retrieved successfully');

    } catch (error) {
      logger.error('Get VIP customers controller error:', error);
      next(error);
    }
  }

  /**
   * Get high value customers
   * GET /api/customers/high-value
   */
  async getHighValueCustomers(req, res, next) {
    try {
      const { limit = 50 } = req.query;
      const customers = await CustomersService.getHighValueCustomers({ 
        limit: parseInt(limit) 
      });

      logger.info('High value customers retrieved successfully', {
        userId: req.user.id,
        resultCount: customers.length
      });

      return successResponse(res, customers, 'High value customers retrieved successfully');

    } catch (error) {
      logger.error('Get high value customers controller error:', error);
      next(error);
    }
  }

  /**
   * Get frequent customers
   * GET /api/customers/frequent
   */
  async getFrequentCustomers(req, res, next) {
    try {
      const { limit = 50 } = req.query;
      const customers = await CustomersService.getFrequentCustomers({ 
        limit: parseInt(limit) 
      });

      logger.info('Frequent customers retrieved successfully', {
        userId: req.user.id,
        resultCount: customers.length
      });

      return successResponse(res, customers, 'Frequent customers retrieved successfully');

    } catch (error) {
      logger.error('Get frequent customers controller error:', error);
      next(error);
    }
  }

  /**
   * Search customers
   * GET /api/customers/search
   */
  async searchCustomers(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { q: searchTerm, limit = 20 } = req.query;

      const customers = await CustomersService.searchCustomers(searchTerm, {
        limit: parseInt(limit)
      });

      logger.info('Customer search completed', {
        userId: req.user.id,
        searchTerm,
        resultCount: customers.length
      });

      return successResponse(res, customers, 'Search completed successfully');

    } catch (error) {
      logger.error('Search customers controller error:', error);
      next(error);
    }
  }

  /**
   * Get customer statistics
   * GET /api/customers/stats
   */
  async getCustomerStats(req, res, next) {
    try {
      const stats = await CustomersService.getCustomerStats();

      logger.info('Customer stats retrieved successfully', {
        userId: req.user.id
      });

      return successResponse(res, stats, 'Customer statistics retrieved successfully');

    } catch (error) {
      logger.error('Get customer stats controller error:', error);
      next(error);
    }
  }

  /**
   * Add loyalty points
   * POST /api/customers/:id/loyalty-points/add
   */
  async addLoyaltyPoints(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { points } = req.body;

      const customer = await CustomersService.addLoyaltyPoints(parseInt(id), points);

      logger.info('Loyalty points added successfully', {
        userId: req.user.id,
        customerId: id,
        pointsAdded: points
      });

      return successResponse(res, customer, `${points} loyalty points added successfully`);

    } catch (error) {
      logger.error('Add loyalty points controller error:', error);
      next(error);
    }
  }

  /**
   * Redeem loyalty points
   * POST /api/customers/:id/loyalty-points/redeem
   */
  async redeemLoyaltyPoints(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { points } = req.body;

      const customer = await CustomersService.redeemLoyaltyPoints(parseInt(id), points);

      logger.info('Loyalty points redeemed successfully', {
        userId: req.user.id,
        customerId: id,
        pointsRedeemed: points
      });

      return successResponse(res, customer, `${points} loyalty points redeemed successfully`);

    } catch (error) {
      logger.error('Redeem loyalty points controller error:', error);
      next(error);
    }
  }
}

module.exports = new CustomersController();
