@echo off
echo ========================================
echo Spa Booking Backend API Test Suite
echo ========================================
echo.

echo Checking if server is running...
curl -s http://localhost:3000/health > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Server is not running at http://localhost:3000
    echo Please start the server first with: npm run dev
    echo.
    pause
    exit /b 1
)

echo Server is running! Starting tests...
echo.

echo ========================================
echo Running Quick Test...
echo ========================================
node quick-test.js
echo.

echo ========================================
echo Installing axios for comprehensive test...
echo ========================================
npm install axios
echo.

echo ========================================
echo Running Comprehensive Test...
echo ========================================
node test-api.js
echo.

echo ========================================
echo Test Results
echo ========================================
echo Check the following files for detailed results:
echo - test.md (Formatted report)
echo - test-results.json (Raw data)
echo.

if exist test.md (
    echo Opening test report...
    start test.md
)

pause
