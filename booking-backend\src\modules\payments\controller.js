/**
 * Payment Controller
 * Handles HTTP requests for payment management
 */

const PaymentService = require('./service');
const { successResponse, errorResponse } = require('../../utils/response');
const logger = require('../../utils/logger');

class PaymentController {
  /**
   * Create a new payment
   * POST /api/payments
   */
  static async createPayment(req, res, next) {
    try {
      const paymentData = req.body;
      const userId = req.user.id;

      const payment = await PaymentService.createPayment(paymentData, userId);

      logger.info('Payment created successfully', {
        paymentId: payment.id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment created successfully', 201);
    } catch (error) {
      logger.error('Create payment failed', {
        error: error.message,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Get all payments with pagination and filtering
   * GET /api/payments
   */
  static async getAllPayments(req, res, next) {
    try {
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        method: req.query.method,
        customerId: req.query.customerId,
        bookingId: req.query.bookingId,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        minAmount: req.query.minAmount,
        maxAmount: req.query.maxAmount,
        search: req.query.search
      };

      const result = await PaymentService.getAllPayments(options);

      logger.info('Payments retrieved successfully', {
        userId: req.user.id,
        count: result.payments.length,
        page: options.page
      });

      return successResponse(res, result.payments, 'Payments retrieved successfully', 200, {
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Get all payments failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get payment by ID
   * GET /api/payments/:id
   */
  static async getPaymentById(req, res, next) {
    try {
      const { id } = req.params;
      const payment = await PaymentService.getPaymentById(parseInt(id));

      logger.info('Payment retrieved successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment retrieved successfully');
    } catch (error) {
      logger.error('Get payment by ID failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Get payment by payment code
   * GET /api/payments/code/:code
   */
  static async getPaymentByCode(req, res, next) {
    try {
      const { code } = req.params;
      const payment = await PaymentService.getPaymentByCode(code);

      logger.info('Payment retrieved by code successfully', {
        paymentCode: code,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment retrieved successfully');
    } catch (error) {
      logger.error('Get payment by code failed', {
        error: error.message,
        paymentCode: req.params.code,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Update payment
   * PUT /api/payments/:id
   */
  static async updatePayment(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      const payment = await PaymentService.updatePayment(parseInt(id), updateData, userId);

      logger.info('Payment updated successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment updated successfully');
    } catch (error) {
      logger.error('Update payment failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Process payment
   * PATCH /api/payments/:id/process
   */
  static async processPayment(req, res, next) {
    try {
      const { id } = req.params;
      const processingData = req.body;
      const userId = req.user.id;

      const payment = await PaymentService.processPayment(parseInt(id), processingData, userId);

      logger.info('Payment processed successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment processed successfully');
    } catch (error) {
      logger.error('Process payment failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Refund payment
   * PATCH /api/payments/:id/refund
   */
  static async refundPayment(req, res, next) {
    try {
      const { id } = req.params;
      const refundData = req.body;
      const userId = req.user.id;

      const payment = await PaymentService.refundPayment(parseInt(id), refundData, userId);

      logger.info('Payment refunded successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment refunded successfully');
    } catch (error) {
      logger.error('Refund payment failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Cancel payment
   * PATCH /api/payments/:id/cancel
   */
  static async cancelPayment(req, res, next) {
    try {
      const { id } = req.params;
      const cancellationData = req.body;
      const userId = req.user.id;

      const payment = await PaymentService.cancelPayment(parseInt(id), cancellationData, userId);

      logger.info('Payment cancelled successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, payment, 'Payment cancelled successfully');
    } catch (error) {
      logger.error('Cancel payment failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });
      next(error);
    }
  }

  /**
   * Get current customer's payment history
   * GET /api/payments/my-history
   */
  static async getMyPaymentHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const options = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        method: req.query.method,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        minAmount: req.query.minAmount,
        maxAmount: req.query.maxAmount,
        search: req.query.search
      };

      const result = await PaymentService.getCustomerPaymentHistory(userId, options);

      logger.info('Customer payment history retrieved successfully', {
        userId: req.user.id,
        count: result.payments.length,
        page: options.page
      });

      return successResponse(res, result.payments, 'Payment history retrieved successfully', 200, {
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Get customer payment history failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get payment statistics
   * GET /api/payments/stats
   */
  static async getPaymentStats(req, res, next) {
    try {
      const options = {
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        method: req.query.method,
        customerId: req.query.customerId
      };

      const stats = await PaymentService.getPaymentStats(options);

      logger.info('Payment statistics retrieved successfully', {
        userId: req.user.id,
        options
      });

      return successResponse(res, stats, 'Payment statistics retrieved successfully');
    } catch (error) {
      logger.error('Get payment stats failed', {
        error: error.message,
        userId: req.user?.id,
        query: req.query
      });
      next(error);
    }
  }

  /**
   * Get payments by booking ID
   * GET /api/payments/booking/:bookingId
   */
  static async getPaymentsByBookingId(req, res, next) {
    try {
      const { bookingId } = req.params;

      const payments = await PaymentService.getPaymentsByBookingId(parseInt(bookingId));

      logger.info('Payments by booking retrieved successfully', {
        bookingId,
        userId: req.user.id,
        count: payments.length
      });

      return successResponse(res, payments, 'Payments retrieved successfully');
    } catch (error) {
      logger.error('Get payments by booking failed', {
        error: error.message,
        bookingId: req.params.bookingId,
        userId: req.user?.id
      });
      next(error);
    }
  }

  /**
   * Delete payment
   * DELETE /api/payments/:id
   */
  static async deletePayment(req, res, next) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const result = await PaymentService.deletePayment(parseInt(id), userId);

      logger.info('Payment deleted successfully', {
        paymentId: id,
        userId: req.user.id
      });

      return successResponse(res, result, 'Payment deleted successfully');
    } catch (error) {
      logger.error('Delete payment failed', {
        error: error.message,
        paymentId: req.params.id,
        userId: req.user?.id
      });
      next(error);
    }
  }
}

module.exports = PaymentController;
