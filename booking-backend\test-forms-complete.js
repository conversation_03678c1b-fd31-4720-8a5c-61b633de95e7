const { exec, spawn } = require('child_process');
const axios = require('axios');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

async function testFormsAPIWithServerStart() {
  console.log('🚀 Starting comprehensive Forms API test...\n');
  
  let serverProcess = null;
  
  try {
    // 1. Start the server
    console.log('1. Starting server...');
    const serverPath = path.join(__dirname, 'start-server.js');
    serverProcess = spawn('node', [serverPath], {
      stdio: 'pipe',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    let serverReady = false;
    
    // Monitor server output
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Server:', output.trim());
      if (output.includes('Ready to accept connections')) {
        serverReady = true;
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      console.error('Server Error:', data.toString().trim());
    });
    
    // Wait for server to be ready
    console.log('Waiting for server to be ready...');
    let attempts = 0;
    while (!serverReady && attempts < 30) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
    
    if (!serverReady) {
      throw new Error('Server failed to start within 30 seconds');
    }
    
    // 2. Test health endpoint first
    console.log('\n2. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.status);
    
    // 3. Test environment
    console.log('\n3. Environment check:');
    console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
    
    // 4. Login first
    console.log('\n4. Attempting login...');
    let loginResponse;
    try {
      loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
    } catch (loginError) {
      console.log('Login failed, trying to create admin user...');
      
      // Try to register admin
      try {
        const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'admin123',
          role: 'admin'
        });
        console.log('✅ Admin user created');
        
        // Login again
        loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
          email: '<EMAIL>',
          password: 'admin123'
        });
      } catch (registerError) {
        console.error('❌ Failed to create admin user:', registerError.response?.data || registerError.message);
        throw registerError;
      }
    }
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');
    
    // 5. Test form creation
    console.log('\n5. Testing form creation...');
    const formData = {
      name: 'Test API Form',
      serviceId: 1,
      branchId: 1,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };
    
    const createResponse = await axios.post(`${BASE_URL}/api/forms`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Form creation response status:', createResponse.status);
    console.log('Response data structure:', Object.keys(createResponse.data));
    
    if (createResponse.data.data) {
      const formResponse = createResponse.data.data;
      console.log('Form data keys:', Object.keys(formResponse));
      
      // Check specific fields
      console.log('\n6. Checking form response fields:');
      console.log('- Form ID:', formResponse.id);
      console.log('- Form name:', formResponse.name);
      console.log('- Form slug:', formResponse.slug);
      console.log('- Public URL:', formResponse.publicUrl || '❌ MISSING');
      console.log('- Embed Code:', formResponse.embedCode ? '✅ Present' : '❌ MISSING');
      
      if (formResponse.publicUrl) {
        console.log('✅ publicUrl is present:', formResponse.publicUrl);
      } else {
        console.log('❌ publicUrl is missing from response');
      }
      
      if (formResponse.embedCode) {
        console.log('✅ embedCode is present (length:', formResponse.embedCode.length, ')');
      } else {
        console.log('❌ embedCode is missing from response');
      }
    } else {
      console.log('❌ No form data in response');
      console.log('Full response:', JSON.stringify(createResponse.data, null, 2));
    }
    
    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    }
  } finally {
    // Clean up server process
    if (serverProcess) {
      console.log('\n🛑 Shutting down server...');
      serverProcess.kill('SIGINT');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

testFormsAPIWithServerStart();
