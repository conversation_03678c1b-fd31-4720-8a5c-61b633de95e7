import './globals.css'
import { Inter } from 'next/font/google'
import { AuthProvider } from '@/contexts/AuthContext'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata = {
  title: 'FormBooker - Create Booking Forms in Minutes',
  description: 'Simple booking form creation with embeddable widgets and standalone pages. Perfect for businesses to streamline their booking process.',
  keywords: 'booking forms, form builder, appointment booking, business forms, embeddable forms',
  authors: [{ name: 'FormBooker Team' }],
  creator: '<PERSON>Book<PERSON>',
  publisher: 'FormBooker',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3001'),
  openGraph: {
    title: 'FormBooker - Create Booking Forms in Minutes',
    description: 'Simple booking form creation with embeddable widgets and standalone pages',
    url: '/',
    siteName: 'FormBooker',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FormBooker - Create Booking Forms in Minutes',
    description: 'Simple booking form creation with embeddable widgets and standalone pages',
    creator: '@formbooker',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#3b82f6' },
    { media: '(prefers-color-scheme: dark)', color: '#3b82f6' },
  ],
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <AuthProvider>
          <div id="root">
            {children}
          </div>
          <div id="modal-root"></div>
        </AuthProvider>
      </body>
    </html>
  )
}
