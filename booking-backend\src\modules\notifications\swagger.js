/**
 * Notification Swagger Documentation
 * OpenAPI/Swagger documentation for notification endpoints
 */

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Notification management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     NotificationBase:
 *       type: object
 *       required:
 *         - type
 *         - title
 *         - message
 *       properties:
 *         recipientId:
 *           type: integer
 *           description: ID of the user who will receive the notification
 *           example: 1
 *         type:
 *           type: string
 *           enum: [booking, payment, system, promotion, reminder]
 *           description: Type of notification
 *           example: booking
 *         title:
 *           type: string
 *           minLength: 1
 *           maxLength: 255
 *           description: Notification title
 *           example: "Booking Confirmed"
 *         message:
 *           type: string
 *           minLength: 1
 *           maxLength: 1000
 *           description: Notification message content
 *           example: "Your booking for Facial Treatment has been confirmed for tomorrow at 2:00 PM"
 *         data:
 *           type: object
 *           description: Additional data for the notification
 *           example: { "bookingId": 123, "serviceId": 456 }
 *         channel:
 *           type: string
 *           enum: [in_app, email, sms, push]
 *           default: in_app
 *           description: Delivery channel for the notification
 *           example: in_app
 *         priority:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *           default: medium
 *           description: Priority level of the notification
 *           example: medium
 *         category:
 *           type: string
 *           maxLength: 100
 *           description: Category for grouping notifications
 *           example: "booking_updates"
 *         templateId:
 *           type: string
 *           maxLength: 100
 *           description: ID of the template used for this notification
 *           example: "booking_confirmation_template"
 *         scheduledAt:
 *           type: string
 *           format: date-time
 *           description: When the notification is scheduled to be sent
 *           example: "2024-01-15T14:00:00Z"
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: When the notification expires
 *           example: "2024-01-20T14:00:00Z"
 * 
 *     NotificationResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/NotificationBase'
 *         - type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: Unique identifier for the notification
 *               example: 1
 *             status:
 *               type: string
 *               enum: [pending, sent, delivered, failed]
 *               default: pending
 *               description: Delivery status of the notification
 *               example: sent
 *             isRead:
 *               type: boolean
 *               default: false
 *               description: Whether the notification has been read
 *               example: false
 *             readAt:
 *               type: string
 *               format: date-time
 *               nullable: true
 *               description: When the notification was read
 *               example: null
 *             sentAt:
 *               type: string
 *               format: date-time
 *               nullable: true
 *               description: When the notification was sent
 *               example: "2024-01-15T10:30:00Z"
 *             createdBy:
 *               type: integer
 *               description: ID of the user who created the notification
 *               example: 2
 *             createdAt:
 *               type: string
 *               format: date-time
 *               description: When the notification was created
 *               example: "2024-01-15T10:00:00Z"
 *             updatedAt:
 *               type: string
 *               format: date-time
 *               description: When the notification was last updated
 *               example: "2024-01-15T10:30:00Z"
 *             recipient:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 name:
 *                   type: string
 *                   example: "John Doe"
 *                 email:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 phone:
 *                   type: string
 *                   example: "+1234567890"
 *             createdByUser:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 2
 *                 name:
 *                   type: string
 *                   example: "Admin User"
 *                 email:
 *                   type: string
 *                   example: "<EMAIL>"
 * 
 *     NotificationStats:
 *       type: object
 *       properties:
 *         totalNotifications:
 *           type: integer
 *           description: Total number of notifications
 *           example: 150
 *         statusStats:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 example: "sent"
 *               count:
 *                 type: string
 *                 example: "75"
 *         typeStats:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 example: "booking"
 *               count:
 *                 type: string
 *                 example: "50"
 *         readStats:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               isRead:
 *                 type: boolean
 *                 example: true
 *               count:
 *                 type: string
 *                 example: "100"
 * 
 *     NotificationCreate:
 *       type: object
 *       required:
 *         - type
 *         - title
 *         - message
 *       properties:
 *         recipientId:
 *           type: integer
 *           description: ID of the user who will receive the notification
 *           example: 1
 *         type:
 *           type: string
 *           enum: [booking, payment, system, promotion, reminder]
 *           description: Type of notification
 *           example: booking
 *         title:
 *           type: string
 *           minLength: 1
 *           maxLength: 255
 *           description: Notification title
 *           example: "Booking Confirmed"
 *         message:
 *           type: string
 *           minLength: 1
 *           maxLength: 1000
 *           description: Notification message content
 *           example: "Your booking has been confirmed"
 *         data:
 *           type: object
 *           description: Additional data for the notification
 *           example: { "bookingId": 123 }
 *         channel:
 *           type: string
 *           enum: [in_app, email, sms, push]
 *           default: in_app
 *           description: Delivery channel for the notification
 *           example: in_app
 *         priority:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *           default: medium
 *           description: Priority level of the notification
 *           example: medium
 *         category:
 *           type: string
 *           maxLength: 100
 *           description: Category for grouping notifications
 *           example: "booking_updates"
 *         templateId:
 *           type: string
 *           maxLength: 100
 *           description: ID of the template used for this notification
 *           example: "booking_template"
 *         scheduledAt:
 *           type: string
 *           format: date-time
 *           description: When the notification is scheduled to be sent
 *           example: "2024-01-15T14:00:00Z"
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: When the notification expires
 *           example: "2024-01-20T14:00:00Z"
 * 
 *     NotificationUpdate:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           minLength: 1
 *           maxLength: 255
 *           description: Notification title
 *           example: "Updated Booking Confirmed"
 *         message:
 *           type: string
 *           minLength: 1
 *           maxLength: 1000
 *           description: Notification message content
 *           example: "Your booking has been updated and confirmed"
 *         data:
 *           type: object
 *           description: Additional data for the notification
 *           example: { "bookingId": 123, "updated": true }
 *         priority:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *           description: Priority level of the notification
 *           example: high
 *         category:
 *           type: string
 *           maxLength: 100
 *           description: Category for grouping notifications
 *           example: "booking_updates"
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: When the notification expires
 *           example: "2024-01-25T14:00:00Z"
 * 
 *     PaginatedNotifications:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Notifications retrieved successfully"
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/NotificationResponse'
 *         pagination:
 *           $ref: '#/components/schemas/PaginationInfo'
 * 
 *     PaginationInfo:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *           example: 1
 *         limit:
 *           type: integer
 *           example: 10
 *         total:
 *           type: integer
 *           example: 50
 *         totalPages:
 *           type: integer
 *           example: 5
 *         hasNext:
 *           type: boolean
 *           example: true
 *         hasPrev:
 *           type: boolean
 *           example: false
 *         nextPage:
 *           type: integer
 *           nullable: true
 *           example: 2
 *         prevPage:
 *           type: integer
 *           nullable: true
 *           example: null
 */

module.exports = {};
