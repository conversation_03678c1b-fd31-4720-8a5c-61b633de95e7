{"name": "formbooker-frontend", "version": "1.0.0", "description": "FormBooker - SAAS Booking Form Generator Platform", "private": true, "scripts": {"dev": "next dev -p 4000 --turbopack", "dev:turbo": "next dev -p 4000 --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "analyze": "ANALYZE=true npm run build", "test:auth": "node test-auth-flow.js", "test:auth-persistence": "node test-auth-persistence.js", "test:token-validation": "node test-token-validation.js", "test:api": "node test-api-fix.js"}, "dependencies": {"axios": "^1.9.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "next": "^15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.3.3", "postcss": "^8.0.0", "prettier": "^3.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}