/**
 * Response Utilities
 * Standardized API response helpers
 */

const config = require('../config/environment');

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code
 * @param {Object} pagination - Pagination info
 * @returns {Object} Response object
 */
const successResponse = (res, data, message = 'Success', statusCode = 200, pagination = null) => {
  // Validate res parameter
  if (!res || typeof res.status !== 'function') {
    throw new Error('res is not defined or invalid in successResponse');
  }

  const response = {
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  };

  if (pagination) {
    response.pagination = pagination;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {string} code - Error code
 * @param {*} details - Error details
 * @returns {Object} Response object
 */
const errorResponse = (res, message, statusCode = 400, code = null, details = null) => {
  // Validate res parameter
  if (!res || typeof res.status !== 'function') {
    throw new Error('res is not defined or invalid in errorResponse');
  }

  const response = {
    success: false,
    error: {
      code: code || getErrorCodeFromStatus(statusCode),
      message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: config.app.version
    }
  };

  if (details) {
    response.error.details = details;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send validation error response
 * @param {Object} res - Express response object
 * @param {Array} errors - Validation errors array
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const validationErrorResponse = (res, errors, message = 'Validation failed') => {
  return errorResponse(res, message, 400, 'VALIDATION_ERROR', errors);
};

/**
 * Send unauthorized response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const unauthorizedResponse = (res, message = 'Authentication required') => {
  return errorResponse(res, message, 401, 'UNAUTHORIZED');
};

/**
 * Send forbidden response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const forbiddenResponse = (res, message = 'Insufficient permissions') => {
  return errorResponse(res, message, 403, 'FORBIDDEN');
};

/**
 * Send not found response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const notFoundResponse = (res, message = 'Resource not found') => {
  return errorResponse(res, message, 404, 'NOT_FOUND');
};

/**
 * Send conflict response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const conflictResponse = (res, message = 'Resource conflict') => {
  return errorResponse(res, message, 409, 'CONFLICT');
};

/**
 * Send server error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @returns {Object} Response object
 */
const serverErrorResponse = (res, message = 'Internal server error') => {
  const errorMessage = config.app.env === 'production' 
    ? 'Something went wrong' 
    : message;
  
  return errorResponse(res, errorMessage, 500, 'INTERNAL_ERROR');
};

/**
 * Send paginated response
 * @param {Object} res - Express response object
 * @param {Array} data - Response data array
 * @param {Object} pagination - Pagination info
 * @param {string} message - Success message
 * @returns {Object} Response object
 */
const paginatedResponse = (res, data, pagination, message = 'Data retrieved successfully') => {
  return successResponse(res, data, message, 200, pagination);
};

/**
 * Send created response
 * @param {Object} res - Express response object
 * @param {*} data - Created resource data
 * @param {string} message - Success message
 * @returns {Object} Response object
 */
const createdResponse = (res, data, message = 'Resource created successfully') => {
  return successResponse(res, data, message, 201);
};

/**
 * Send updated response
 * @param {Object} res - Express response object
 * @param {*} data - Updated resource data
 * @param {string} message - Success message
 * @returns {Object} Response object
 */
const updatedResponse = (res, data, message = 'Resource updated successfully') => {
  return successResponse(res, data, message, 200);
};

/**
 * Send deleted response
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @returns {Object} Response object
 */
const deletedResponse = (res, message = 'Resource deleted successfully') => {
  return successResponse(res, null, message, 200);
};

/**
 * Send no content response
 * @param {Object} res - Express response object
 * @returns {Object} Response object
 */
const noContentResponse = (res) => {
  return res.status(204).send();
};

/**
 * Get error code from HTTP status code
 * @param {number} statusCode - HTTP status code
 * @returns {string} Error code
 */
const getErrorCodeFromStatus = (statusCode) => {
  const statusCodes = {
    400: 'BAD_REQUEST',
    401: 'UNAUTHORIZED',
    403: 'FORBIDDEN',
    404: 'NOT_FOUND',
    409: 'CONFLICT',
    422: 'UNPROCESSABLE_ENTITY',
    429: 'TOO_MANY_REQUESTS',
    500: 'INTERNAL_ERROR',
    502: 'BAD_GATEWAY',
    503: 'SERVICE_UNAVAILABLE'
  };
  
  return statusCodes[statusCode] || 'UNKNOWN_ERROR';
};

/**
 * Format validation errors from express-validator
 * @param {Array} errors - Validation errors from express-validator
 * @returns {Array} Formatted errors
 */
const formatValidationErrors = (errors) => {
  return errors.map(error => ({
    field: error.param || error.path,
    message: error.msg || error.message,
    value: error.value,
    location: error.location
  }));
};

/**
 * Create pagination object
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {Object} Pagination object
 */
const createPagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

module.exports = {
  successResponse,
  errorResponse,
  validationErrorResponse,
  unauthorizedResponse,
  forbiddenResponse,
  notFoundResponse,
  conflictResponse,
  serverErrorResponse,
  paginatedResponse,
  createdResponse,
  updatedResponse,
  deletedResponse,
  noContentResponse,
  getErrorCodeFromStatus,
  formatValidationErrors,
  createPagination
};
