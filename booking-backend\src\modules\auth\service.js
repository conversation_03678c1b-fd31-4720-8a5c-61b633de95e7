/**
 * Auth Service
 * Business logic for authentication and authorization
 */

const crypto = require('crypto');
const User = require('./model');
const { generateToken, generateRefreshToken, verifyRefreshToken } = require('../../middleware/auth');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');
const config = require('../../config/environment');

class AuthService {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} User and tokens
   */
  async register(userData) {
    try {
      const { email, password, name, phone, role = 'customer' } = userData;
      
      // Check if user already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        throw new AppError('Email already registered', 409, 'EMAIL_EXISTS');
      }
      
      // Check phone uniqueness if provided
      if (phone) {
        const existingPhone = await User.findOne({ where: { phone } });
        if (existingPhone) {
          throw new AppError('Phone number already registered', 409, 'PHONE_EXISTS');
        }
      }
      
      // Generate email verification token
      const emailVerificationToken = crypto.randomBytes(32).toString('hex');
      const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      
      // Create user
      const user = await User.create({
        email: email.toLowerCase(),
        password,
        name,
        phone,
        role,
        emailVerificationToken,
        emailVerificationExpires
      });
      
      // Generate tokens
      const tokenPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name
      };
      
      const accessToken = generateToken(tokenPayload);
      const refreshToken = generateRefreshToken(tokenPayload);
      
      logger.business('user_registered', {
        userId: user.id,
        email: user.email,
        role: user.role
      });
      
      // TODO: Send verification email
      // await EmailService.sendVerificationEmail(user.email, emailVerificationToken);
      
      return {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.jwt.expiresIn
        }
      };
      
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }
  
  /**
   * Login user
   * @param {Object} credentials - Login credentials
   * @returns {Promise<Object>} User and tokens
   */
  async login(credentials) {
    try {
      const { email, password } = credentials;
      
      // Find user by email
      const user = await User.findByEmail(email);
      if (!user) {
        throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
      }
      
      // Check if account is locked
      if (user.isLocked()) {
        throw new AppError('Account temporarily locked due to too many failed login attempts', 423, 'ACCOUNT_LOCKED');
      }
      
      // Check if account is active
      if (!user.isActive) {
        throw new AppError('Account is deactivated', 401, 'ACCOUNT_DEACTIVATED');
      }
      
      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        await user.incLoginAttempts();
        throw new AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
      }
      
      // Reset login attempts on successful login
      if (user.loginAttempts > 0) {
        await user.resetLoginAttempts();
      }
      
      // Update last login
      await user.update({ lastLogin: new Date() });
      
      // Generate tokens
      const tokenPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name
      };
      
      const accessToken = generateToken(tokenPayload);
      const refreshToken = generateRefreshToken(tokenPayload);
      
      logger.business('user_logged_in', {
        userId: user.id,
        email: user.email,
        role: user.role
      });
      
      return {
        user: user.toJSON(),
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: config.jwt.expiresIn
        }
      };
      
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }
  
  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} New tokens
   */
  async refreshToken(refreshToken) {
    try {
      // Verify refresh token
      const decoded = verifyRefreshToken(refreshToken);
      
      // Find user
      const user = await User.findByPk(decoded.id);
      if (!user || !user.isActive) {
        throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN');
      }
      
      // Generate new tokens
      const tokenPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name
      };
      
      const newAccessToken = generateToken(tokenPayload);
      const newRefreshToken = generateRefreshToken(tokenPayload);
      
      logger.business('token_refreshed', {
        userId: user.id,
        email: user.email
      });
      
      return {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: config.jwt.expiresIn
        }
      };
      
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN');
    }
  }
  
  /**
   * Logout user (invalidate tokens)
   * @param {number} userId - User ID
   * @returns {Promise<void>}
   */
  async logout(userId) {
    try {
      // TODO: Implement token blacklisting if using Redis
      // For now, just log the logout
      
      logger.business('user_logged_out', {
        userId
      });
      
      return { message: 'Logged out successfully' };
      
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }
  
  /**
   * Verify email address
   * @param {string} token - Email verification token
   * @returns {Promise<Object>} Success message
   */
  async verifyEmail(token) {
    try {
      const user = await User.findByEmailVerificationToken(token);
      if (!user) {
        throw new AppError('Invalid or expired verification token', 400, 'INVALID_VERIFICATION_TOKEN');
      }
      
      await user.update({
        isEmailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      });
      
      logger.business('email_verified', {
        userId: user.id,
        email: user.email
      });
      
      return { message: 'Email verified successfully' };
      
    } catch (error) {
      logger.error('Email verification failed:', error);
      throw error;
    }
  }
  
  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} Success message
   */
  async requestPasswordReset(email) {
    try {
      const user = await User.findByEmail(email);
      if (!user) {
        // Don't reveal if email exists
        return { message: 'If the email exists, a password reset link has been sent' };
      }
      
      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      
      await user.update({
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires
      });
      
      logger.business('password_reset_requested', {
        userId: user.id,
        email: user.email
      });
      
      // TODO: Send password reset email
      // await EmailService.sendPasswordResetEmail(user.email, resetToken);
      
      return { message: 'If the email exists, a password reset link has been sent' };
      
    } catch (error) {
      logger.error('Password reset request failed:', error);
      throw error;
    }
  }
  
  /**
   * Reset password
   * @param {string} token - Password reset token
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Success message
   */
  async resetPassword(token, newPassword) {
    try {
      const user = await User.findByPasswordResetToken(token);
      if (!user) {
        throw new AppError('Invalid or expired reset token', 400, 'INVALID_RESET_TOKEN');
      }
      
      await user.update({
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        loginAttempts: 0,
        lockUntil: null
      });
      
      logger.business('password_reset_completed', {
        userId: user.id,
        email: user.email
      });
      
      return { message: 'Password reset successfully' };
      
    } catch (error) {
      logger.error('Password reset failed:', error);
      throw error;
    }
  }
  
  /**
   * Change password
   * @param {number} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Success message
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }
      
      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new AppError('Current password is incorrect', 400, 'INVALID_CURRENT_PASSWORD');
      }
      
      // Update password
      await user.update({ password: newPassword });
      
      logger.business('password_changed', {
        userId: user.id,
        email: user.email
      });
      
      return { message: 'Password changed successfully' };
      
    } catch (error) {
      logger.error('Password change failed:', error);
      throw error;
    }
  }
  
  /**
   * Get user profile
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User profile
   */
  async getProfile(userId) {
    try {
      const user = await User.scope('withoutSensitive').findByPk(userId, {
        include: [
          {
            association: 'branch',
            attributes: ['id', 'name', 'address']
          }
        ]
      });
      
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }
      
      return user;
      
    } catch (error) {
      logger.error('Get profile failed:', error);
      throw error;
    }
  }
  
  /**
   * Update user profile
   * @param {number} userId - User ID
   * @param {Object} updateData - Profile update data
   * @returns {Promise<Object>} Updated user profile
   */
  async updateProfile(userId, updateData) {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', 404, 'USER_NOT_FOUND');
      }
      
      // Fields that can be updated
      const allowedFields = ['name', 'phone', 'dateOfBirth', 'gender', 'avatar'];
      const filteredData = {};
      
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      });
      
      await user.update(filteredData);
      
      logger.business('profile_updated', {
        userId: user.id,
        email: user.email,
        updatedFields: Object.keys(filteredData)
      });
      
      return user.toJSON();
      
    } catch (error) {
      logger.error('Profile update failed:', error);
      throw error;
    }
  }
}

module.exports = new AuthService();
