// Quick test for environment variables and form URL generation

require('dotenv').config();

console.log('=== Environment Variables Test ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('DB_HOST:', process.env.DB_HOST);

console.log('\n=== Form URL Generation Test ===');
const testSlug = 'test-form-123';
const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4000';

const publicUrl = `${frontendUrl}/book/${testSlug}`;
const embedCode = `<iframe src="${frontendUrl}/book/${testSlug}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`;

console.log('Test slug:', testSlug);
console.log('Frontend URL:', frontendUrl);
console.log('Generated public URL:', publicUrl);
console.log('Generated embed code length:', embedCode.length);
console.log('Generated embed code preview:', embedCode.substring(0, 100) + '...');

console.log('\n=== Test Results ===');
console.log('✅ Environment loading:', process.env.FRONTEND_URL ? 'SUCCESS' : 'FAILED');
console.log('✅ URL generation:', publicUrl.includes('book') ? 'SUCCESS' : 'FAILED');
console.log('✅ Embed code generation:', embedCode.includes('iframe') ? 'SUCCESS' : 'FAILED');
