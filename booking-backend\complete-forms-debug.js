/**
 * Complete Forms Debug Test
 * A comprehensive test to debug the forms creation issue
 */

const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(colors[color] + message + colors.reset);
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function runFormsDebugTest() {
  colorLog('cyan', '🚀 Starting Complete Forms Debug Test...\n');
  
  let serverProcess = null;
  
  try {
    // Step 1: Environment Check
    colorLog('blue', '📋 Step 1: Environment Check');
    require('dotenv').config();
    
    console.log('Environment Variables:');
    console.log('  NODE_ENV:', process.env.NODE_ENV || 'undefined');
    console.log('  FRONTEND_URL:', process.env.FRONTEND_URL || 'undefined');
    console.log('  DB_HOST:', process.env.DB_HOST || 'undefined');
    console.log('  DB_DATABASE:', process.env.DB_DATABASE || 'undefined');
    
    if (!process.env.FRONTEND_URL) {
      colorLog('red', '❌ FRONTEND_URL is not set in environment');
      colorLog('yellow', '⚠️  This might be the cause of the issue');
    } else {
      colorLog('green', '✅ FRONTEND_URL is set: ' + process.env.FRONTEND_URL);
    }
    
    // Step 2: Start Server
    colorLog('blue', '\n📋 Step 2: Starting Backend Server');
    serverProcess = spawn('node', ['start-server.js'], {
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: path.resolve(__dirname),
      env: { ...process.env }
    });
    
    let serverReady = false;
    let serverOutput = '';
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      serverOutput += output;
      if (output.includes('Ready to accept connections')) {
        serverReady = true;
        colorLog('green', '✅ Server is ready');
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      colorLog('red', 'Server Error: ' + error);
    });
    
    // Wait for server
    colorLog('yellow', '⏳ Waiting for server to start...');
    let attempts = 0;
    while (!serverReady && attempts < 30) {
      await sleep(1000);
      attempts++;
      if (attempts % 5 === 0) {
        colorLog('yellow', `Still waiting... (${attempts}/30)`);
      }
    }
    
    if (!serverReady) {
      colorLog('red', '❌ Server failed to start within 30 seconds');
      console.log('Server output:', serverOutput);
      return;
    }
    
    // Step 3: Health Check
    colorLog('blue', '\n📋 Step 3: API Health Check');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      colorLog('green', '✅ API Health: ' + healthResponse.data.status);
      console.log('Database Health:', healthResponse.data.database?.status || 'unknown');
    } catch (healthError) {
      colorLog('red', '❌ Health check failed: ' + healthError.message);
      return;
    }
    
    // Step 4: Authentication
    colorLog('blue', '\n📋 Step 4: Authentication Test');
    let token;
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123456'
      });
      
      if (loginResponse.data.success) {
        token = loginResponse.data.data.token;
        colorLog('green', '✅ Authentication successful');
      } else {
        colorLog('red', '❌ Authentication failed: ' + loginResponse.data.message);
        colorLog('yellow', '💡 Try running: node reset-admin-password.js');
        return;
      }
    } catch (authError) {
      colorLog('red', '❌ Authentication error: ' + (authError.response?.data?.message || authError.message));
      return;
    }
    
    // Step 5: Prerequisites Check
    colorLog('blue', '\n📋 Step 5: Prerequisites Check');
    try {
      const [servicesRes, branchesRes] = await Promise.all([
        axios.get(`${BASE_URL}/api/services`, { headers: { 'Authorization': `Bearer ${token}` } }),
        axios.get(`${BASE_URL}/api/branches`, { headers: { 'Authorization': `Bearer ${token}` } })
      ]);
      
      const services = servicesRes.data.data || [];
      const branches = branchesRes.data.data || [];
      
      console.log(`Services: ${services.length} available`);
      console.log(`Branches: ${branches.length} available`);
      
      if (services.length === 0 || branches.length === 0) {
        colorLog('red', '❌ Insufficient data: Need at least 1 service and 1 branch');
        return;
      }
      
      colorLog('green', '✅ Prerequisites check passed');
    } catch (prereqError) {
      colorLog('red', '❌ Prerequisites check failed: ' + (prereqError.response?.data?.message || prereqError.message));
      return;
    }
    
    // Step 6: Forms Creation Test - THE MAIN TEST
    colorLog('blue', '\n📋 Step 6: Forms Creation Test (Main Debug)');
    colorLog('bright', '🔍 This is where we expect to see the issue...\n');
    
    const formData = {
      name: 'Debug Test Form - ' + Date.now(),
      serviceId: 1,
      branchId: 1,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };
    
    console.log('Sending form data:', JSON.stringify(formData, null, 2));
    
    try {
      const createResponse = await axios.post(`${BASE_URL}/api/forms`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      colorLog('green', '✅ Form creation API call successful');
      console.log('Response status:', createResponse.status);
      console.log('Response data structure:', Object.keys(createResponse.data));
      
      if (createResponse.data.data) {
        const form = createResponse.data.data;
        console.log('\n📊 Form Response Analysis:');
        console.log('Form keys:', Object.keys(form));
        console.log('  - id:', form.id);
        console.log('  - name:', form.name);
        console.log('  - slug:', form.slug || '❌ MISSING');
        console.log('  - publicUrl:', form.publicUrl || '❌ MISSING');
        console.log('  - embedCode:', form.embedCode ? `✅ Present (${form.embedCode.length} chars)` : '❌ MISSING');
        
        // The main check
        if (form.publicUrl && form.embedCode) {
          colorLog('green', '\n🎉 SUCCESS! Both publicUrl and embedCode are present');
          console.log('Public URL:', form.publicUrl);
          console.log('Embed Code Preview:', form.embedCode.substring(0, 100) + '...');
        } else {
          colorLog('red', '\n❌ ISSUE CONFIRMED: Missing publicUrl or embedCode');
          
          if (!form.publicUrl) {
            colorLog('red', '  - publicUrl is missing');
          }
          if (!form.embedCode) {
            colorLog('red', '  - embedCode is missing');
          }
          
          // Additional debugging
          console.log('\n🔍 Additional Debug Info:');
          console.log('Full form object:', JSON.stringify(form, null, 2));
        }
        
      } else {
        colorLog('red', '❌ No form data in response');
        console.log('Full response:', JSON.stringify(createResponse.data, null, 2));
      }
      
    } catch (createError) {
      colorLog('red', '❌ Form creation failed');
      console.log('Error status:', createError.response?.status);
      console.log('Error message:', createError.response?.data?.message || createError.message);
      if (createError.response?.data) {
        console.log('Error details:', JSON.stringify(createError.response.data, null, 2));
      }
    }
    
    colorLog('cyan', '\n🏁 Test completed!');
    
  } catch (error) {
    colorLog('red', '❌ Test failed with error: ' + error.message);
    console.error(error);
  } finally {
    // Cleanup
    if (serverProcess) {
      colorLog('yellow', '\n🛑 Shutting down server...');
      serverProcess.kill('SIGINT');
      await sleep(2000);
    }
  }
}

// Run the test
runFormsDebugTest().catch(console.error);
