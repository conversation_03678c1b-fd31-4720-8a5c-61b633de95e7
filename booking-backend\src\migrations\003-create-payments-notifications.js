/**
 * Migration: Create Payments and Notifications Tables
 * Creates the final tables for payments and notifications
 */

const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create Payments table
    await queryInterface.createTable('payments', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      payment_code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true
      },
      booking_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'bookings',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      customer_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false
      },
      currency: {
        type: DataTypes.STRING(3),
        allowNull: false,
        defaultValue: 'VND'
      },
      payment_method: {
        type: DataTypes.ENUM('cash', 'card', 'bank_transfer', 'e_wallet', 'crypto'),
        allowNull: false
      },
      payment_provider: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      transaction_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      reference_number: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
        allowNull: false,
        defaultValue: 'pending'
      },
      payment_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      due_date: {
        type: DataTypes.DATE,
        allowNull: true
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      gateway_response: {
        type: DataTypes.JSON,
        allowNull: true
      },
      refund_amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      refund_reason: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      refunded_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      refunded_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      processed_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      processed_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: DataTypes.DATE,
        allowNull: true
      }
    });

    // Create Notifications table
    await queryInterface.createTable('notifications', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      recipient_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: DataTypes.ENUM('booking', 'payment', 'system', 'promotion', 'reminder'),
        allowNull: false
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      data: {
        type: DataTypes.JSON,
        allowNull: true
      },
      channel: {
        type: DataTypes.ENUM('in_app', 'email', 'sms', 'push'),
        allowNull: false,
        defaultValue: 'in_app'
      },
      priority: {
        type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'medium'
      },
      category: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM('pending', 'sent', 'delivered', 'failed'),
        allowNull: false,
        defaultValue: 'pending'
      },
      is_read: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      read_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      sent_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      scheduled_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      expires_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      template_id: {
        type: DataTypes.STRING(100),
        allowNull: true
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: DataTypes.DATE,
        allowNull: true
      }
    });

    // Create indexes for better performance (with IF NOT EXISTS check)
    try {
      await queryInterface.addIndex('payments', ['booking_id'], { name: 'idx_payments_booking_id' });
      await queryInterface.addIndex('payments', ['customer_id'], { name: 'idx_payments_customer_id' });
      await queryInterface.addIndex('payments', ['status'], { name: 'idx_payments_status' });
      await queryInterface.addIndex('payments', ['payment_method'], { name: 'idx_payments_payment_method' });
      await queryInterface.addIndex('payments', ['payment_date'], { name: 'idx_payments_payment_date' });

      await queryInterface.addIndex('notifications', ['recipient_id'], { name: 'idx_notifications_recipient_id' });
      await queryInterface.addIndex('notifications', ['type'], { name: 'idx_notifications_type' });
      await queryInterface.addIndex('notifications', ['status'], { name: 'idx_notifications_status' });
      await queryInterface.addIndex('notifications', ['is_read'], { name: 'idx_notifications_is_read' });
      await queryInterface.addIndex('notifications', ['created_at'], { name: 'idx_notifications_created_at' });

      // Create indexes for other tables
      await queryInterface.addIndex('bookings', ['customer_id'], { name: 'idx_bookings_customer_id' });
      await queryInterface.addIndex('bookings', ['service_id'], { name: 'idx_bookings_service_id' });
      await queryInterface.addIndex('bookings', ['branch_id'], { name: 'idx_bookings_branch_id' });
      await queryInterface.addIndex('bookings', ['employee_id'], { name: 'idx_bookings_employee_id' });
      await queryInterface.addIndex('bookings', ['booking_date'], { name: 'idx_bookings_booking_date' });
      await queryInterface.addIndex('bookings', ['status'], { name: 'idx_bookings_status' });

      await queryInterface.addIndex('employees', ['branch_id'], { name: 'idx_employees_branch_id' });
      await queryInterface.addIndex('employees', ['status'], { name: 'idx_employees_status' });

      await queryInterface.addIndex('customers', ['customer_code'], { name: 'idx_customers_customer_code' });
      await queryInterface.addIndex('customers', ['membership_level'], { name: 'idx_customers_membership_level' });

      await queryInterface.addIndex('users', ['email'], { name: 'idx_users_email' });
      await queryInterface.addIndex('users', ['role'], { name: 'idx_users_role' });
      await queryInterface.addIndex('users', ['status'], { name: 'idx_users_status' });

      await queryInterface.addIndex('branches', ['code'], { name: 'idx_branches_code' });
      await queryInterface.addIndex('branches', ['status'], { name: 'idx_branches_status' });

      await queryInterface.addIndex('services', ['code'], { name: 'idx_services_code' });
      await queryInterface.addIndex('services', ['category'], { name: 'idx_services_category' });
      await queryInterface.addIndex('services', ['status'], { name: 'idx_services_status' });
    } catch (error) {
      console.log('⚠️  Some indexes may already exist, continuing...');
    }

    console.log('✅ Created Payments and Notifications tables with indexes');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notifications');
    await queryInterface.dropTable('payments');
    console.log('✅ Dropped Payments and Notifications tables');
  }
};
