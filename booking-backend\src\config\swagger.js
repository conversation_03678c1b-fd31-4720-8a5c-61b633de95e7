/**
 * Swagger Configuration
 * API Documentation setup
 */

const swaggerJsdoc = require('swagger-jsdoc');
const config = require('./environment');

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Spa Booking System API',
    version: config.app.version,
    description: 'RESTful API for Spa Booking Management System',
    contact: {
      name: 'API Support',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: config.app.url,
      description: `${config.app.env} server`
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT Authorization header using the Bearer scheme'
      }
    },
    schemas: {
      Error: {
        type: 'object',
        required: ['success', 'error'],
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              code: {
                type: 'string',
                example: 'VALIDATION_ERROR'
              },
              message: {
                type: 'string',
                example: 'Validation failed'
              },
              details: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    field: {
                      type: 'string'
                    },
                    message: {
                      type: 'string'
                    },
                    value: {
                      type: 'string'
                    }
                  }
                }
              }
            }
          },
          meta: {
            type: 'object',
            properties: {
              timestamp: {
                type: 'string',
                format: 'date-time'
              },
              version: {
                type: 'string'
              }
            }
          }
        }
      },
      Success: {
        type: 'object',
        required: ['success', 'data', 'message'],
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          data: {
            type: 'object',
            description: 'Response data'
          },
          message: {
            type: 'string',
            example: 'Operation successful'
          },
          pagination: {
            type: 'object',
            properties: {
              page: {
                type: 'integer',
                example: 1
              },
              limit: {
                type: 'integer',
                example: 10
              },
              total: {
                type: 'integer',
                example: 100
              },
              totalPages: {
                type: 'integer',
                example: 10
              }
            }
          },
          meta: {
            type: 'object',
            properties: {
              timestamp: {
                type: 'string',
                format: 'date-time'
              },
              version: {
                type: 'string'
              }
            }
          }
        }
      },
      Pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            minimum: 1,
            default: 1,
            description: 'Page number'
          },
          limit: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            default: 10,
            description: 'Number of items per page'
          },
          total: {
            type: 'integer',
            description: 'Total number of items'
          },
          totalPages: {
            type: 'integer',
            description: 'Total number of pages'
          }
        }
      }
    },
    parameters: {
      PageParam: {
        name: 'page',
        in: 'query',
        description: 'Page number',
        required: false,
        schema: {
          type: 'integer',
          minimum: 1,
          default: 1
        }
      },
      LimitParam: {
        name: 'limit',
        in: 'query',
        description: 'Number of items per page',
        required: false,
        schema: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
          default: 10
        }
      },
      SortParam: {
        name: 'sort',
        in: 'query',
        description: 'Sort field and direction (e.g., "createdAt:desc")',
        required: false,
        schema: {
          type: 'string',
          example: 'createdAt:desc'
        }
      },
      SearchParam: {
        name: 'search',
        in: 'query',
        description: 'Search term',
        required: false,
        schema: {
          type: 'string'
        }
      }
    },
    responses: {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                code: 'UNAUTHORIZED',
                message: 'Authentication required'
              },
              meta: {
                timestamp: '2025-06-09T23:00:00.000Z',
                version: '1.0.0'
              }
            }
          }
        }
      },
      ForbiddenError: {
        description: 'Insufficient permissions',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                code: 'FORBIDDEN',
                message: 'Insufficient permissions'
              },
              meta: {
                timestamp: '2025-06-09T23:00:00.000Z',
                version: '1.0.0'
              }
            }
          }
        }
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                code: 'NOT_FOUND',
                message: 'Resource not found'
              },
              meta: {
                timestamp: '2025-06-09T23:00:00.000Z',
                version: '1.0.0'
              }
            }
          }
        }
      },
      ValidationError: {
        description: 'Validation failed',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: 'Validation failed',
                details: [
                  {
                    field: 'email',
                    message: 'Invalid email format',
                    value: 'invalid-email'
                  }
                ]
              },
              meta: {
                timestamp: '2025-06-09T23:00:00.000Z',
                version: '1.0.0'
              }
            }
          }
        }
      },
      ServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              success: false,
              error: {
                code: 'INTERNAL_ERROR',
                message: 'Internal server error'
              },
              meta: {
                timestamp: '2025-06-09T23:00:00.000Z',
                version: '1.0.0'
              }
            }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ],
  tags: [
    {
      name: 'Authentication',
      description: 'User authentication and authorization'
    },
    {
      name: 'Users',
      description: 'User management'
    },
    {
      name: 'Branches',
      description: 'Branch management'
    },
    {
      name: 'Services',
      description: 'Service management'
    },
    {
      name: 'Employees',
      description: 'Employee management'
    },
    {
      name: 'Customers',
      description: 'Customer management'
    },
    {
      name: 'Bookings',
      description: 'Booking management'
    },
    {
      name: 'Payments',
      description: 'Payment processing'
    },
    {
      name: 'Notifications',
      description: 'Notification management'
    },
    {
      name: 'Settings',
      description: 'System settings'
    },
    {
      name: 'Reports',
      description: 'Reports and analytics'
    }
  ]
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/modules/*/swagger.js',
    './src/modules/*/route.js'
  ]
};

const swaggerSpec = swaggerJsdoc(options);

module.exports = {
  swaggerSpec,
  swaggerDefinition,
  options
};
