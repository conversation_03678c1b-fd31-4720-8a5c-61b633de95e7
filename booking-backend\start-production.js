/**
 * Production server starter
 * Starts the server without test endpoints and keeps it running
 */

console.log('🚀 Starting Spa Booking System Backend (Production Mode)...');

try {
  // Load environment first
  require('dotenv').config();
  console.log('✅ Environment loaded');

  console.log('1. Loading app...');
  const app = require('./src/app');
  console.log('✅ App loaded successfully!');

  // Verify app is working
  if (typeof app !== 'function') {
    throw new Error('App is not a valid Express application');
  }

  console.log('2. Starting server...');
  const port = process.env.APP_PORT || 3000;
  
  const server = app.listen(port, '0.0.0.0', () => {
    console.log('✅ Server started successfully');
    console.log(`🔗 API URL: http://localhost:${port}`);
    console.log(`📖 API Documentation: http://localhost:${port}/api-docs`);
    console.log(`🏥 Health Check: http://localhost:${port}/health`);
    console.log('🎉 Ready to accept connections!');
    console.log('');
    console.log('📋 Available endpoints:');
    console.log('  - GET  /health                 - Health check');
    console.log('  - GET  /api-docs               - API documentation');
    console.log('  - POST /api/auth/register      - User registration');
    console.log('  - POST /api/auth/login         - User login');
    console.log('  - GET  /api/users              - Get all users');
    console.log('  - GET  /api/branches           - Get all branches');
    console.log('  - GET  /api/services           - Get all services');
    console.log('  - GET  /api/employees          - Get all employees');
    console.log('  - GET  /api/customers          - Get all customers');
    console.log('  - GET  /api/bookings           - Get all bookings');
    console.log('  - GET  /api/payments           - Get all payments');
    console.log('  - GET  /api/notifications      - Get all notifications');
    console.log('');
    console.log('🛑 Press Ctrl+C to stop the server');
  });

  server.on('error', (error) => {
    console.error('❌ Server error:', error.message);
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${port} is already in use. Please use a different port.`);
    }
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down gracefully...');
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 SIGTERM received, shutting down gracefully...');
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });

} catch (error) {
  console.error('❌ Failed to start server:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
