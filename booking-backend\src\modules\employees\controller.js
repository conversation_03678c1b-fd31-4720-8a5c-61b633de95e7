/**
 * Employees Controller
 * HTTP request handlers for employee management
 */

const EmployeesService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  paginatedResponse,
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const { getPaginationParams, buildSortOrder } = require('../../utils/pagination');
const logger = require('../../utils/logger');

class EmployeesController {
  /**
   * Get all employees with pagination and filters
   * GET /api/employees
   */
  async getEmployees(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { page, limit } = getPaginationParams(req.query);
      const { branchId, status, position, specialization, isAvailable, search, sort } = req.query;

      const filters = { branchId, status, position, specialization, isAvailable, search };
      const pagination = { 
        page, 
        limit,
        order: buildSortOrder(sort, ['employeeCode', 'position', 'rating', 'createdAt'], [['employeeCode', 'ASC']])
      };

      const result = await EmployeesService.getEmployees(filters, pagination);

      logger.info('Employees retrieved successfully', {
        userId: req.user.id,
        filters,
        pagination: { page, limit },
        resultCount: result.data.length
      });

      return paginatedResponse(res, result.data, result.pagination, 'Employees retrieved successfully');

    } catch (error) {
      logger.error('Get employees controller error:', error);
      next(error);
    }
  }

  /**
   * Get employee by ID
   * GET /api/employees/:id
   */
  async getEmployeeById(req, res, next) {
    try {
      const { id } = req.params;
      const employee = await EmployeesService.getEmployeeById(parseInt(id));

      logger.info('Employee retrieved successfully', {
        userId: req.user.id,
        employeeId: id
      });

      return successResponse(res, employee, 'Employee retrieved successfully');

    } catch (error) {
      logger.error('Get employee by ID controller error:', error);
      next(error);
    }
  }

  /**
   * Create new employee
   * POST /api/employees
   */
  async createEmployee(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const employeeData = req.body;
      const employee = await EmployeesService.createEmployee(employeeData);

      logger.info('Employee created successfully', {
        userId: req.user.id,
        employeeId: employee.id,
        employeeCode: employee.employeeCode,
        position: employee.position
      });

      return createdResponse(res, employee, 'Employee created successfully');

    } catch (error) {
      logger.error('Create employee controller error:', error);
      next(error);
    }
  }

  /**
   * Update employee
   * PUT /api/employees/:id
   */
  async updateEmployee(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const employee = await EmployeesService.updateEmployee(parseInt(id), updateData);

      logger.info('Employee updated successfully', {
        userId: req.user.id,
        employeeId: id,
        updatedFields: Object.keys(updateData)
      });

      return successResponse(res, employee, 'Employee updated successfully');

    } catch (error) {
      logger.error('Update employee controller error:', error);
      next(error);
    }
  }

  /**
   * Delete employee
   * DELETE /api/employees/:id
   */
  async deleteEmployee(req, res, next) {
    try {
      const { id } = req.params;
      const result = await EmployeesService.deleteEmployee(parseInt(id));

      logger.info('Employee deleted successfully', {
        userId: req.user.id,
        employeeId: id
      });

      return successResponse(res, result, 'Employee deleted successfully');

    } catch (error) {
      logger.error('Delete employee controller error:', error);
      next(error);
    }
  }

  /**
   * Update employee status
   * PATCH /api/employees/:id/status
   */
  async updateEmployeeStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { status } = req.body;

      const employee = await EmployeesService.updateEmployeeStatus(parseInt(id), status);

      logger.info('Employee status updated successfully', {
        userId: req.user.id,
        employeeId: id,
        newStatus: status
      });

      return successResponse(res, employee, `Employee status updated to ${status} successfully`);

    } catch (error) {
      logger.error('Update employee status controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle employee availability
   * PATCH /api/employees/:id/availability
   */
  async toggleEmployeeAvailability(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isAvailable } = req.body;

      const employee = await EmployeesService.toggleEmployeeAvailability(parseInt(id), isAvailable);

      logger.info('Employee availability changed successfully', {
        userId: req.user.id,
        employeeId: id,
        isAvailable
      });

      return successResponse(res, employee, `Employee ${isAvailable ? 'marked as available' : 'marked as unavailable'} successfully`);

    } catch (error) {
      logger.error('Toggle employee availability controller error:', error);
      next(error);
    }
  }

  /**
   * Get employees by branch
   * GET /api/employees/branch/:branchId
   */
  async getEmployeesByBranch(req, res, next) {
    try {
      const { branchId } = req.params;
      const { limit = 100 } = req.query;

      const employees = await EmployeesService.getEmployeesByBranch(parseInt(branchId), { 
        limit: parseInt(limit)
      });

      logger.info('Employees by branch retrieved successfully', {
        userId: req.user.id,
        branchId,
        resultCount: employees.length
      });

      return successResponse(res, employees, 'Branch employees retrieved successfully');

    } catch (error) {
      logger.error('Get employees by branch controller error:', error);
      next(error);
    }
  }

  /**
   * Get employees by specialization
   * GET /api/employees/specialization/:specialization
   */
  async getEmployeesBySpecialization(req, res, next) {
    try {
      const { specialization } = req.params;
      const { limit = 50 } = req.query;

      const employees = await EmployeesService.getEmployeesBySpecialization(specialization, { 
        limit: parseInt(limit)
      });

      logger.info('Employees by specialization retrieved successfully', {
        userId: req.user.id,
        specialization,
        resultCount: employees.length
      });

      return successResponse(res, employees, `${specialization} specialists retrieved successfully`);

    } catch (error) {
      logger.error('Get employees by specialization controller error:', error);
      next(error);
    }
  }

  /**
   * Get available employees
   * GET /api/employees/available
   */
  async getAvailableEmployees(req, res, next) {
    try {
      const { limit = 100 } = req.query;
      const employees = await EmployeesService.getAvailableEmployees({ 
        limit: parseInt(limit) 
      });

      logger.info('Available employees retrieved successfully', {
        userId: req.user.id,
        resultCount: employees.length
      });

      return successResponse(res, employees, 'Available employees retrieved successfully');

    } catch (error) {
      logger.error('Get available employees controller error:', error);
      next(error);
    }
  }

  /**
   * Get top rated employees
   * GET /api/employees/top-rated
   */
  async getTopRatedEmployees(req, res, next) {
    try {
      const { limit = 20 } = req.query;
      const employees = await EmployeesService.getTopRatedEmployees({ 
        limit: parseInt(limit) 
      });

      logger.info('Top rated employees retrieved successfully', {
        userId: req.user.id,
        resultCount: employees.length
      });

      return successResponse(res, employees, 'Top rated employees retrieved successfully');

    } catch (error) {
      logger.error('Get top rated employees controller error:', error);
      next(error);
    }
  }

  /**
   * Search employees
   * GET /api/employees/search
   */
  async searchEmployees(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { q: searchTerm, limit = 20 } = req.query;

      const employees = await EmployeesService.searchEmployees(searchTerm, {
        limit: parseInt(limit)
      });

      logger.info('Employee search completed', {
        userId: req.user.id,
        searchTerm,
        resultCount: employees.length
      });

      return successResponse(res, employees, 'Search completed successfully');

    } catch (error) {
      logger.error('Search employees controller error:', error);
      next(error);
    }
  }

  /**
   * Get employee statistics
   * GET /api/employees/stats
   */
  async getEmployeeStats(req, res, next) {
    try {
      const stats = await EmployeesService.getEmployeeStats();

      logger.info('Employee stats retrieved successfully', {
        userId: req.user.id
      });

      return successResponse(res, stats, 'Employee statistics retrieved successfully');

    } catch (error) {
      logger.error('Get employee stats controller error:', error);
      next(error);
    }
  }
}

module.exports = new EmployeesController();
