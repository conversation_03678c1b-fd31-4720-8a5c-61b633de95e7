/**
 * Helper Utilities
 * Common utility functions
 */

/**
 * Generate URL-friendly slug from text
 */
const generateSlug = (text) => {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove non-word chars except spaces and hyphens
    .replace(/[\s_-]+/g, '-') // Replace spaces, underscores, multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Validate email format
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone format
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

/**
 * Format currency
 */
const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

/**
 * Format date
 */
const formatDate = (date, locale = 'en-US') => {
  return new Date(date).toLocaleDateString(locale);
};

/**
 * Format time
 */
const formatTime = (time, locale = 'en-US') => {
  return new Date(`2000-01-01T${time}`).toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Generate random string
 */
const generateRandomString = (length = 10) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

/**
 * Sanitize string for SQL
 */
const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  return str.replace(/['"\\]/g, '');
};

/**
 * Calculate time difference in minutes
 */
const getTimeDifferenceInMinutes = (time1, time2) => {
  const date1 = new Date(`2000-01-01T${time1}`);
  const date2 = new Date(`2000-01-01T${time2}`);
  return Math.abs(date2 - date1) / (1000 * 60);
};

/**
 * Check if date is in the future
 */
const isFutureDate = (date) => {
  return new Date(date) > new Date();
};

/**
 * Check if time is in business hours
 */
const isBusinessHours = (time, openTime = '09:00', closeTime = '17:00') => {
  const timeToCheck = new Date(`2000-01-01T${time}`);
  const openingTime = new Date(`2000-01-01T${openTime}`);
  const closingTime = new Date(`2000-01-01T${closeTime}`);
  
  return timeToCheck >= openingTime && timeToCheck <= closingTime;
};

module.exports = {
  generateSlug,
  isValidEmail,
  isValidPhone,
  formatCurrency,
  formatDate,
  formatTime,
  generateRandomString,
  sanitizeString,
  getTimeDifferenceInMinutes,
  isFutureDate,
  isBusinessHours
};
