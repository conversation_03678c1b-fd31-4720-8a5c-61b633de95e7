'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import FormsService from '@/services/formsService';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  DollarSign,
  CheckCircle,
  ArrowLeft,
  User,
  Phone,
  Mail
} from 'lucide-react';

/**
 * Public Booking Form Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function BookingFormPage() {
  const params = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    email: '',
    preferredDate: '',
    preferredTime: '',
    specialRequests: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  // Mock form configuration - TODO: Fetch from API based on formId
  const [formConfig, setFormConfig] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFormConfig = async () => {
      try {
        setLoading(true);
        
        // Fetch actual form data by slug
        const result = await FormsService.getFormBySlug(params.formId);
        
        if (result.success) {
          const formData = result.data;
          
          // Transform API response to component format
          const config = {
            id: formData.id,
            name: formData.name,
            businessName: formData.businessName,
            service: {
              name: formData.service.name,
              duration: formData.service.duration,
              price: formData.service.price,
              description: formData.service.description
            },
            branch: {
              name: formData.branch.name,
              address: formData.branch.address,
              phone: formData.branch.phone,
              city: formData.branch.city
            },
            fields: formData.fields,
            branding: formData.branding
          };
          
          setFormConfig(config);
        } else {
          console.error('Failed to load form:', result.error);
          setFormConfig(null);
        }
      } catch (error) {
        console.error('Error fetching form config:', error);
        setFormConfig(null);
      } finally {
        setLoading(false);
      }
    };

    if (params.formId) {
      fetchFormConfig();
    }
  }, [params.formId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.preferredDate) {
      newErrors.preferredDate = 'Preferred date is required';
    }

    if (!formData.preferredTime) {
      newErrors.preferredTime = 'Preferred time is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit booking using the API
      const result = await FormsService.submitPublicBooking({
        formSlug: params.formId,
        customerName: formData.fullName,
        phoneNumber: formData.phone,
        emailAddress: formData.email,
        preferredDate: formData.preferredDate,
        preferredTime: formData.preferredTime,
        specialRequests: formData.specialRequests
      });

      if (result.success) {
        setIsSubmitted(true);
      } else {
        setErrors({ submit: result.error || 'Failed to submit booking. Please try again.' });
      }
    } catch (error) {
      console.error('Booking submission error:', error);
      setErrors({ submit: 'Failed to submit booking. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading booking form...</p>
        </div>
      </div>
    );
  }

  if (!formConfig) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-text mb-2">Form Not Found</h1>
          <p className="text-neutral-600">The booking form you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center py-12 px-4">
        <div className="max-w-md w-full">
          <div className="card text-center">
            <div className="card-body">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-secondary-600" />
              </div>
              
              <h1 className="text-2xl font-bold text-text mb-2">
                ✅ Booking Confirmed!
              </h1>
              
              <p className="text-lg text-neutral-600 mb-6">
                Thank you, {formData.fullName}!
              </p>

              <div className="text-left space-y-3 mb-6">
                <h3 className="font-semibold text-text">Your appointment details:</h3>
                <div className="space-y-2 text-sm text-neutral-600">
                  <p><span className="font-medium">Service:</span> {formConfig.service.name}</p>
                  <p><span className="font-medium">Date:</span> {new Date(formData.preferredDate).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}</p>
                  <p><span className="font-medium">Time:</span> {formData.preferredTime}</p>
                  <p><span className="font-medium">Location:</span> {formConfig.branch.name}</p>
                </div>
              </div>

              <p className="text-neutral-600 mb-6">
                We'll contact you soon to confirm your appointment.
              </p>

              <div className="space-y-3">
                <p className="text-sm text-neutral-600">
                  Questions? Call {formConfig.branch.phone}
                </p>
                
                <button
                  onClick={() => window.location.reload()}
                  className="btn-primary w-full"
                >
                  Book Another Service
                </button>
              </div>
            </div>
          </div>

          {/* Powered by FormBooker */}
          <div className="text-center mt-6">
            <p className="text-xs text-neutral-500">
              Powered by <span className="font-medium">FormBooker</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background py-12 px-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-text mb-2">
            {formConfig.businessName}
          </h1>
          <h2 className="text-xl text-neutral-700">
            {formConfig.name}
          </h2>
        </div>

        {/* Service Info */}
        <div className="card mb-6">
          <div className="card-body">
            <h3 className="font-semibold text-text mb-3">Service Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center text-neutral-600">
                <span className="font-medium">Service:</span>
                <span className="ml-2">{formConfig.service.name}</span>
              </div>
              <div className="flex items-center text-neutral-600">
                <MapPin className="h-4 w-4 mr-1" />
                <span>Location: {formConfig.branch.name}</span>
              </div>
              <div className="flex items-center text-neutral-600">
                <Clock className="h-4 w-4 mr-1" />
                <span>Duration: {formConfig.service.duration} minutes</span>
              </div>
              <div className="flex items-center text-neutral-600">
                <DollarSign className="h-4 w-4 mr-1" />
                <span>Price: ${formConfig.service.price}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Form */}
        <div className="card">
          <div className="card-body">
            <h3 className="font-semibold text-text mb-4">Your Information:</h3>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Full Name */}
              {formConfig.fields.customerName && (
                <div>
                  <label className="form-label">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className={`form-input ${errors.fullName ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your full name"
                  />
                  {errors.fullName && (
                    <p className="form-error">{errors.fullName}</p>
                  )}
                </div>
              )}

              {/* Phone */}
              {formConfig.fields.phoneNumber && (
                <div>
                  <label className="form-label">Phone</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={`form-input ${errors.phone ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && (
                    <p className="form-error">{errors.phone}</p>
                  )}
                </div>
              )}

              {/* Email */}
              {formConfig.fields.emailAddress && (
                <div>
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`form-input ${errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your email address"
                  />
                  {errors.email && (
                    <p className="form-error">{errors.email}</p>
                  )}
                </div>
              )}

              {/* Preferred Date */}
              {formConfig.fields.preferredDate && (
                <div>
                  <label className="form-label">Preferred Date</label>
                  <input
                    type="date"
                    name="preferredDate"
                    value={formData.preferredDate}
                    onChange={handleInputChange}
                    className={`form-input ${errors.preferredDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.preferredDate && (
                    <p className="form-error">{errors.preferredDate}</p>
                  )}
                </div>
              )}

              {/* Preferred Time */}
              {formConfig.fields.preferredTime && (
                <div>
                  <label className="form-label">Preferred Time</label>
                  <input
                    type="time"
                    name="preferredTime"
                    value={formData.preferredTime}
                    onChange={handleInputChange}
                    className={`form-input ${errors.preferredTime ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                  />
                  {errors.preferredTime && (
                    <p className="form-error">{errors.preferredTime}</p>
                  )}
                </div>
              )}

              {/* Special Requests */}
              {formConfig.fields.specialRequests && (
                <div>
                  <label className="form-label">Special Requests (Optional)</label>
                  <textarea
                    name="specialRequests"
                    value={formData.specialRequests}
                    onChange={handleInputChange}
                    className="form-input"
                    rows="3"
                    placeholder="Any special requests or notes..."
                  />
                </div>
              )}

              {/* Submit Error */}
              {errors.submit && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary w-full"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Booking Appointment...
                  </div>
                ) : (
                  'Book Appointment'
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Powered by FormBooker */}
        <div className="text-center mt-6">
          <p className="text-xs text-neutral-500">
            Powered by <span className="font-medium">FormBooker</span>
          </p>
        </div>
      </div>
    </div>
  );
}
