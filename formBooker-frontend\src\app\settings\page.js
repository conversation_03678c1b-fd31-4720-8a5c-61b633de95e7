'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Save, 
  User, 
  Bell, 
  Shield, 
  Trash2,
  Eye,
  EyeOff,
  AlertTriangle
} from 'lucide-react';

/**
 * Settings Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const [showPassword, setShowPassword] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const [accountData, setAccountData] = useState({
    businessName: 'Demo Business',
    email: '<EMAIL>',
    phone: '(*************',
    website: 'https://demobusiness.com'
  });

  const [notificationData, setNotificationData] = useState({
    emailBookings: true,
    smsBookings: true,
    weeklyReports: false
  });

  const [defaultsData, setDefaultsData] = useState({
    defaultBranch: '1',
    bookingWindow: '30'
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const branches = [
    { id: '1', name: 'Main Location' },
    { id: '2', name: 'Downtown Branch' },
    { id: '3', name: 'Mall Location' }
  ];

  const bookingWindowOptions = [
    { value: '7', label: '7 days' },
    { value: '14', label: '14 days' },
    { value: '30', label: '30 days' },
    { value: '60', label: '60 days' },
    { value: '90', label: '90 days' }
  ];

  const tabs = [
    { id: 'account', label: 'Account', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield }
  ];

  const handleAccountChange = (e) => {
    const { name, value } = e.target;
    setAccountData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleDefaultsChange = (e) => {
    const { name, value } = e.target;
    setDefaultsData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveAccount = async (e) => {
    e.preventDefault();
    // TODO: Implement save account API call
    console.log('Saving account data:', accountData);
    console.log('Saving defaults data:', defaultsData);
  };

  const handleSaveNotifications = async (e) => {
    e.preventDefault();
    // TODO: Implement save notifications API call
    console.log('Saving notification preferences:', notificationData);
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();
    // TODO: Implement change password API call
    console.log('Changing password');
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  };

  const handleDeleteAccount = async () => {
    // TODO: Implement delete account API call
    console.log('Deleting account');
    setShowDeleteConfirm(false);
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-text">Account Settings</h1>
            <p className="mt-2 text-neutral-600">
              Manage your account preferences and security settings
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary-700'
                          : 'text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900'
                      }`}
                    >
                      <Icon className="h-4 w-4 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Content */}
            <div className="lg:col-span-3">
              {/* Account Settings */}
              {activeTab === 'account' && (
                <div className="space-y-6">
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-semibold text-text">Business Information:</h2>
                    </div>
                    <div className="card-body">
                      <form onSubmit={handleSaveAccount} className="space-y-4">
                        <div>
                          <label className="form-label">Business Name</label>
                          <input
                            type="text"
                            name="businessName"
                            value={accountData.businessName}
                            onChange={handleAccountChange}
                            className="form-input"
                          />
                        </div>

                        <div>
                          <label className="form-label">Email</label>
                          <input
                            type="email"
                            name="email"
                            value={accountData.email}
                            onChange={handleAccountChange}
                            className="form-input"
                          />
                        </div>

                        <div>
                          <label className="form-label">Phone</label>
                          <input
                            type="tel"
                            name="phone"
                            value={accountData.phone}
                            onChange={handleAccountChange}
                            className="form-input"
                          />
                        </div>

                        <div>
                          <label className="form-label">Website</label>
                          <input
                            type="url"
                            name="website"
                            value={accountData.website}
                            onChange={handleAccountChange}
                            className="form-input"
                          />
                        </div>

                        <button type="submit" className="btn-primary">
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </button>
                      </form>
                    </div>
                  </div>

                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-semibold text-text">Form Defaults:</h2>
                    </div>
                    <div className="card-body">
                      <div className="space-y-4">
                        <div>
                          <label className="form-label">Default Branch</label>
                          <select
                            name="defaultBranch"
                            value={defaultsData.defaultBranch}
                            onChange={handleDefaultsChange}
                            className="form-input"
                          >
                            {branches.map(branch => (
                              <option key={branch.id} value={branch.id}>
                                {branch.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="form-label">Booking Window</label>
                          <select
                            name="bookingWindow"
                            value={defaultsData.bookingWindow}
                            onChange={handleDefaultsChange}
                            className="form-input"
                          >
                            {bookingWindowOptions.map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                          <p className="form-help">How far in advance customers can book</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <div className="card">
                  <div className="card-header">
                    <h2 className="text-lg font-semibold text-text">Notification Preferences:</h2>
                  </div>
                  <div className="card-body">
                    <form onSubmit={handleSaveNotifications} className="space-y-4">
                      <div className="space-y-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="emailBookings"
                            name="emailBookings"
                            checked={notificationData.emailBookings}
                            onChange={handleNotificationChange}
                            className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                          />
                          <label htmlFor="emailBookings" className="ml-3 text-sm text-neutral-700">
                            ☑ Email notifications for bookings
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="smsBookings"
                            name="smsBookings"
                            checked={notificationData.smsBookings}
                            onChange={handleNotificationChange}
                            className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                          />
                          <label htmlFor="smsBookings" className="ml-3 text-sm text-neutral-700">
                            ☑ SMS notifications for bookings
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="weeklyReports"
                            name="weeklyReports"
                            checked={notificationData.weeklyReports}
                            onChange={handleNotificationChange}
                            className="w-4 h-4 text-primary-600 border-neutral-300 rounded focus:ring-primary-500"
                          />
                          <label htmlFor="weeklyReports" className="ml-3 text-sm text-neutral-700">
                            ☐ Weekly summary reports
                          </label>
                        </div>
                      </div>

                      <button type="submit" className="btn-primary">
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </button>
                    </form>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <div className="card">
                    <div className="card-header">
                      <h2 className="text-lg font-semibold text-text">Change Password</h2>
                    </div>
                    <div className="card-body">
                      <form onSubmit={handleChangePassword} className="space-y-4">
                        <div>
                          <label className="form-label">Current Password</label>
                          <div className="relative">
                            <input
                              type={showPassword ? 'text' : 'password'}
                              name="currentPassword"
                              value={passwordData.currentPassword}
                              onChange={handlePasswordChange}
                              className="form-input pr-10"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4 text-neutral-400" />
                              ) : (
                                <Eye className="h-4 w-4 text-neutral-400" />
                              )}
                            </button>
                          </div>
                        </div>

                        <div>
                          <label className="form-label">New Password</label>
                          <input
                            type="password"
                            name="newPassword"
                            value={passwordData.newPassword}
                            onChange={handlePasswordChange}
                            className="form-input"
                          />
                        </div>

                        <div>
                          <label className="form-label">Confirm New Password</label>
                          <input
                            type="password"
                            name="confirmPassword"
                            value={passwordData.confirmPassword}
                            onChange={handlePasswordChange}
                            className="form-input"
                          />
                        </div>

                        <button type="submit" className="btn-primary">
                          Change Password
                        </button>
                      </form>
                    </div>
                  </div>

                  <div className="card border-red-200">
                    <div className="card-header border-red-200">
                      <h2 className="text-lg font-semibold text-red-700">Danger Zone:</h2>
                    </div>
                    <div className="card-body">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-base font-medium text-text mb-2">Delete Account</h3>
                          <p className="text-sm text-neutral-600 mb-4">
                            Permanently delete your account and all associated data. This action cannot be undone.
                          </p>
                          <button
                            onClick={() => setShowDeleteConfirm(true)}
                            className="btn-outline text-red-600 border-red-300 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Account
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          {showDeleteConfirm && (
            <div className="modal-overlay flex items-center justify-center p-4">
              <div className="modal-content animate-scale-in">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <h2 className="text-lg font-semibold text-text">Delete Account</h2>
                  </div>
                  
                  <p className="text-neutral-600 mb-6">
                    Are you sure you want to delete your account? This will permanently remove all your data, 
                    including forms, bookings, and settings. This action cannot be undone.
                  </p>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className="btn-outline flex-1"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDeleteAccount}
                      className="btn-primary bg-red-600 hover:bg-red-700 flex-1"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
