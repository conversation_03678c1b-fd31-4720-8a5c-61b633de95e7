/**
 * Payment Routes
 * Defines API endpoints for payment management
 */

const express = require('express');
const router = express.Router();
const PaymentController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validateIdParam } = require('../../middleware/validation');
const { generalLimiter: rateLimiter, adminLimiter } = require('../../middleware/rateLimiter');

// Validation schemas
const { body, query } = require('express-validator');

// Create payment validation
const createPaymentValidation = [
  body('bookingId')
    .isInt({ min: 1 })
    .withMessage('Valid booking ID is required'),
  body('customerId')
    .isInt({ min: 1 })
    .withMessage('Valid customer ID is required'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Valid amount is required'),
  body('method')
    .isIn(['cash', 'card', 'bank_transfer', 'e_wallet', 'credit'])
    .withMessage('Valid payment method is required'),
  body('status')
    .optional()
    .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])
    .withMessage('Invalid payment status'),
  body('transactionId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must not exceed 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be 3 characters'),
  body('fees')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Fees must be a positive number'),
  body('isPartialPayment')
    .optional()
    .isBoolean()
    .withMessage('isPartialPayment must be a boolean')
];

// Update payment validation
const updatePaymentValidation = [
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Valid amount is required'),
  body('method')
    .optional()
    .isIn(['cash', 'card', 'bank_transfer', 'e_wallet', 'credit'])
    .withMessage('Valid payment method is required'),
  body('status')
    .optional()
    .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])
    .withMessage('Invalid payment status'),
  body('transactionId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must not exceed 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters')
];

// Process payment validation
const processPaymentValidation = [
  body('transactionId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must not exceed 100 characters'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters')
];

// Refund payment validation
const refundPaymentValidation = [
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Valid refund amount is required'),
  body('reason')
    .notEmpty()
    .withMessage('Refund reason is required')
    .isLength({ min: 5, max: 500 })
    .withMessage('Refund reason must be between 5 and 500 characters')
];

// Cancel payment validation
const cancelPaymentValidation = [
  body('reason')
    .notEmpty()
    .withMessage('Cancellation reason is required')
    .isLength({ min: 5, max: 500 })
    .withMessage('Cancellation reason must be between 5 and 500 characters')
];

// Query validation for filtering
const filterValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])
    .withMessage('Invalid status value'),
  query('method')
    .optional()
    .isIn(['cash', 'card', 'bank_transfer', 'e_wallet', 'credit'])
    .withMessage('Invalid payment method'),
  query('customerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid customer ID is required'),
  query('bookingId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid booking ID is required'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required (YYYY-MM-DD)'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required (YYYY-MM-DD)'),
  query('minAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum amount must be a positive number'),
  query('maxAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum amount must be a positive number')
];

/**
 * @route   GET /api/payments/my-history
 * @desc    Get current customer's payment history
 * @access  Private (Customer)
 */
router.get('/my-history',
  authenticate,
  rateLimiter,
  filterValidation,
  validateRequest,
  PaymentController.getMyPaymentHistory
);

/**
 * @route   GET /api/payments/stats
 * @desc    Get payment statistics
 * @access  Private (Admin, Staff)
 */
router.get('/stats',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  PaymentController.getPaymentStats
);

/**
 * @route   GET /api/payments/code/:code
 * @desc    Get payment by payment code
 * @access  Private
 */
router.get('/code/:code',
  authenticate,
  rateLimiter,
  PaymentController.getPaymentByCode
);

/**
 * @route   GET /api/payments/booking/:bookingId
 * @desc    Get payments by booking ID
 * @access  Private
 */
router.get('/booking/:bookingId',
  authenticate,
  rateLimiter,
  validateIdParam('bookingId'),
  validateRequest,
  PaymentController.getPaymentsByBookingId
);

/**
 * @route   GET /api/payments
 * @desc    Get all payments with pagination and filtering
 * @access  Private (Admin, Staff)
 */
router.get('/',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  filterValidation,
  validateRequest,
  PaymentController.getAllPayments
);

/**
 * @route   POST /api/payments
 * @desc    Create a new payment
 * @access  Private (Admin, Staff)
 */
router.post('/',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  createPaymentValidation,
  validateRequest,
  PaymentController.createPayment
);

/**
 * @route   GET /api/payments/:id
 * @desc    Get payment by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  rateLimiter,
  validateIdParam('id'),
  validateRequest,
  PaymentController.getPaymentById
);

/**
 * @route   PUT /api/payments/:id
 * @desc    Update payment
 * @access  Private (Admin, Staff)
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  updatePaymentValidation,
  validateRequest,
  PaymentController.updatePayment
);

/**
 * @route   PATCH /api/payments/:id/process
 * @desc    Process payment (mark as completed)
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/process',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  processPaymentValidation,
  validateRequest,
  PaymentController.processPayment
);

/**
 * @route   PATCH /api/payments/:id/refund
 * @desc    Refund payment
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/refund',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  refundPaymentValidation,
  validateRequest,
  PaymentController.refundPayment
);

/**
 * @route   PATCH /api/payments/:id/cancel
 * @desc    Cancel payment
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/cancel',
  authenticate,
  authorize(['admin', 'staff']),
  rateLimiter,
  validateIdParam('id'),
  cancelPaymentValidation,
  validateRequest,
  PaymentController.cancelPayment
);

/**
 * @route   DELETE /api/payments/:id
 * @desc    Delete payment (soft delete)
 * @access  Private (Admin)
 */
router.delete('/:id',
  authenticate,
  authorize(['admin']),
  adminLimiter,
  validateIdParam('id'),
  validateRequest,
  PaymentController.deletePayment
);

module.exports = router;
