/**
 * Authentication Debug Script
 * Debug authentication persistence issues
 */

console.log('🔍 Authentication Debug Script');

// Simulate browser environment
global.window = {
  location: { pathname: '/bookings' },
  localStorage: {
    data: {},
    getItem(key) {
      return this.data[key] || null;
    },
    setItem(key, value) {
      this.data[key] = value;
    },
    removeItem(key) {
      delete this.data[key];
    }
  },
  document: {
    cookie: ''
  }
};

// Mock JWT token (valid for testing)
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.test';
const mockUser = {
  id: 1,
  email: '<EMAIL>',
  role: 'customer',
  name: 'Test User'
};

// Set up mock data
window.localStorage.setItem('accessToken', mockToken);
window.localStorage.setItem('refreshToken', 'refresh_token_here');
window.localStorage.setItem('user', JSON.stringify(mockUser));

console.log('📦 Mock data set up:');
console.log('- accessToken:', window.localStorage.getItem('accessToken') ? 'SET' : 'NOT SET');
console.log('- refreshToken:', window.localStorage.getItem('refreshToken') ? 'SET' : 'NOT SET');
console.log('- user:', window.localStorage.getItem('user') ? 'SET' : 'NOT SET');

// Import and test tokenManager
try {
  // Mock the api module
  const tokenManager = {
    getToken: () => {
      if (typeof window !== 'undefined') {
        return window.localStorage.getItem('accessToken');
      }
      return null;
    },
    
    getRefreshToken: () => {
      if (typeof window !== 'undefined') {
        return window.localStorage.getItem('refreshToken');
      }
      return null;
    },
    
    getUser: () => {
      if (typeof window !== 'undefined') {
        const user = window.localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
      }
      return null;
    },
    
    isTokenValid: () => {
      const token = tokenManager.getToken();
      if (!token) {
        console.log('❌ No token found');
        return false;
      }
      
      try {
        // Basic JWT format check (header.payload.signature)
        const parts = token.split('.');
        if (parts.length !== 3) {
          console.log('❌ Invalid JWT format');
          return false;
        }
        
        // For testing, let's decode a real token structure
        // Create a valid token for testing
        const payload = {
          id: 1,
          email: '<EMAIL>',
          role: 'customer',
          name: 'Test User',
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours from now
        };
        
        const currentTime = Math.floor(Date.now() / 1000);
        console.log('🕐 Token validation:');
        console.log('- Current time:', currentTime);
        console.log('- Token expires:', payload.exp);
        console.log('- Time until expiry:', payload.exp - currentTime, 'seconds');
        
        // Check if token is expired (with 30 second buffer)
        const isValid = payload.exp > (currentTime + 30);
        console.log('- Is valid:', isValid);
        return isValid;
      } catch (error) {
        console.error('❌ Token validation error:', error);
        return false;
      }
    }
  };

  // Mock authService
  const authService = {
    isAuthenticated: () => {
      const token = tokenManager.getToken();
      const user = tokenManager.getUser();
      const isTokenValid = tokenManager.isTokenValid();
      
      console.log('🔐 Authentication check:');
      console.log('- Has token:', !!token);
      console.log('- Has user:', !!user);
      console.log('- Token valid:', isTokenValid);
      
      // User is authenticated if they have a valid token and user data
      const isAuth = !!(token && user && isTokenValid);
      console.log('- Final result:', isAuth);
      return isAuth;
    },
    
    getCurrentUser: () => {
      return tokenManager.getUser();
    }
  };

  console.log('\n🧪 Testing authentication flow...');
  
  // Test 1: Check token manager
  console.log('\n1️⃣ Testing tokenManager:');
  const token = tokenManager.getToken();
  const user = tokenManager.getUser();
  const isTokenValid = tokenManager.isTokenValid();
  
  console.log('- getToken():', token ? 'SUCCESS' : 'FAILED');
  console.log('- getUser():', user ? 'SUCCESS' : 'FAILED');
  console.log('- isTokenValid():', isTokenValid ? 'SUCCESS' : 'FAILED');
  
  // Test 2: Check authService
  console.log('\n2️⃣ Testing authService:');
  const isAuth = authService.isAuthenticated();
  const currentUser = authService.getCurrentUser();
  
  console.log('- isAuthenticated():', isAuth ? 'SUCCESS' : 'FAILED');
  console.log('- getCurrentUser():', currentUser ? 'SUCCESS' : 'FAILED');
  
  // Test 3: Simulate AuthContext initialization
  console.log('\n3️⃣ Simulating AuthContext.initializeAuth():');
  
  const initializeAuth = () => {
    console.log('🔄 Starting initializeAuth...');
    
    // Check if user is authenticated from localStorage
    const isAuth = authService.isAuthenticated();
    const currentUser = authService.getCurrentUser();
    const token = tokenManager.getToken();
    
    console.log('- authService.isAuthenticated():', isAuth);
    console.log('- authService.getCurrentUser():', !!currentUser);
    console.log('- tokenManager.getToken():', !!token);
    
    if (isAuth && currentUser && token) {
      console.log('✅ User should be authenticated');
      return { success: true, user: currentUser };
    } else {
      console.log('❌ User should be logged out');
      console.log('  - isAuth:', isAuth);
      console.log('  - currentUser:', !!currentUser);
      console.log('  - token:', !!token);
      return { success: false };
    }
  };
  
  const result = initializeAuth();
  
  console.log('\n🎯 Final Result:');
  if (result.success) {
    console.log('✅ Authentication should work - user should stay on /bookings');
  } else {
    console.log('❌ Authentication failed - user would be redirected to login');
  }
  
  // Test 4: Test with expired token
  console.log('\n4️⃣ Testing with expired token:');
  
  // Create expired token
  const expiredPayload = {
    id: 1,
    email: '<EMAIL>',
    role: 'customer',
    name: 'Test User',
    iat: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
    exp: Math.floor(Date.now() / 1000) - 1800  // 30 minutes ago (expired)
  };
  
  // Override isTokenValid for expired token test
  const originalIsTokenValid = tokenManager.isTokenValid;
  tokenManager.isTokenValid = () => {
    const currentTime = Math.floor(Date.now() / 1000);
    const isValid = expiredPayload.exp > (currentTime + 30);
    console.log('- Expired token test - Is valid:', isValid);
    return isValid;
  };
  
  const expiredResult = authService.isAuthenticated();
  console.log('- With expired token:', expiredResult ? 'AUTHENTICATED' : 'NOT AUTHENTICATED');
  
  // Restore original function
  tokenManager.isTokenValid = originalIsTokenValid;
  
} catch (error) {
  console.error('❌ Error during testing:', error);
}

console.log('\n🏁 Debug script completed');
