/**
 * Authentication Flow Test Script
 * Tests the improved authentication persistence
 */

const { chromium } = require('playwright');

async function testAuthFlow() {
  console.log('🚀 Starting Authentication Flow Test...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Test 1: Initial load should show landing page
    console.log('📋 Test 1: Initial load');
    await page.goto('http://localhost:4000');
    await page.waitForSelector('h1');
    const title = await page.textContent('h1');
    console.log(`✅ Landing page loaded: ${title}`);
    
    // Test 2: Login flow
    console.log('\n📋 Test 2: Login flow');
    await page.click('a[href="/login"]');
    await page.waitForSelector('form');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard');
    console.log('✅ Login successful, redirected to dashboard');
    
    // Test 3: Check localStorage tokens
    const tokens = await page.evaluate(() => {
      return {
        accessToken: localStorage.getItem('accessToken'),
        refreshToken: localStorage.getItem('refreshToken'),
        user: localStorage.getItem('user')
      };
    });
    
    console.log('✅ Tokens stored in localStorage:', {
      hasAccessToken: !!tokens.accessToken,
      hasRefreshToken: !!tokens.refreshToken,
      hasUser: !!tokens.user
    });
    
    // Test 4: Page refresh should maintain session
    console.log('\n📋 Test 4: Page refresh persistence');
    await page.reload();
    await page.waitForSelector('h1');
    const dashboardTitle = await page.textContent('h1');
    console.log(`✅ After refresh: ${dashboardTitle}`);
    
    // Test 5: Navigate to different protected route
    console.log('\n📋 Test 5: Protected route navigation');
    await page.click('a[href="/forms"]');
    await page.waitForURL('**/forms');
    console.log('✅ Protected route accessible');
    
    // Test 6: Close and reopen browser (simulate browser restart)
    console.log('\n📋 Test 6: Browser restart simulation');
    await page.close();
    const newPage = await context.newPage();
    await newPage.goto('http://localhost:4000/dashboard');
    
    // Should stay on dashboard if tokens are valid
    await newPage.waitForSelector('h1', { timeout: 5000 });
    const newPageTitle = await newPage.textContent('h1');
    console.log(`✅ After browser restart: ${newPageTitle}`);
    
    // Test 7: Logout flow
    console.log('\n📋 Test 7: Logout flow');
    await newPage.click('button:has-text("Sign Out")');
    await newPage.waitForURL('**/login');
    console.log('✅ Logout successful, redirected to login');
    
    // Test 8: Check tokens are cleared
    const clearedTokens = await newPage.evaluate(() => {
      return {
        accessToken: localStorage.getItem('accessToken'),
        refreshToken: localStorage.getItem('refreshToken'),
        user: localStorage.getItem('user')
      };
    });
    
    console.log('✅ Tokens cleared after logout:', {
      hasAccessToken: !!clearedTokens.accessToken,
      hasRefreshToken: !!clearedTokens.refreshToken,
      hasUser: !!clearedTokens.user
    });
    
    // Test 9: Try to access protected route after logout
    console.log('\n📋 Test 9: Protected route after logout');
    await newPage.goto('http://localhost:4000/dashboard');
    await newPage.waitForURL('**/login*');
    console.log('✅ Redirected to login when accessing protected route');
    
    console.log('\n🎉 All authentication flow tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test
if (require.main === module) {
  testAuthFlow().catch(console.error);
}

module.exports = { testAuthFlow };
