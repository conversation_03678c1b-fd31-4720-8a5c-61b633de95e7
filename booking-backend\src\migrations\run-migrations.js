/**
 * Migration Runner
 * Runs all migration scripts to create database tables
 */

const { Sequelize } = require('sequelize');
const config = require('../config/environment');
const fs = require('fs');
const path = require('path');

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database.database,
  config.database.username,
  config.database.password,
  {
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    logging: console.log,
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

async function runMigrations() {
  try {
    console.log('🚀 Starting database migrations...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Create migrations table if not exists
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get list of migration files
    const migrationsDir = __dirname;
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js') && file !== 'run-migrations.js')
      .sort();

    console.log(`📋 Found ${migrationFiles.length} migration files`);

    // Check which migrations have already been run
    const [executedMigrations] = await sequelize.query(
      'SELECT name FROM migrations ORDER BY executed_at'
    );
    const executedNames = executedMigrations.map(m => m.name);

    // Run pending migrations
    for (const file of migrationFiles) {
      const migrationName = file.replace('.js', '');
      
      if (executedNames.includes(migrationName)) {
        console.log(`⏭️  Skipping ${migrationName} (already executed)`);
        continue;
      }

      console.log(`🔄 Running migration: ${migrationName}`);
      
      try {
        const migration = require(path.join(migrationsDir, file));
        
        // Create queryInterface
        const queryInterface = sequelize.getQueryInterface();
        
        // Run the migration
        await migration.up(queryInterface, Sequelize);
        
        // Record migration as executed
        await sequelize.query(
          'INSERT INTO migrations (name) VALUES (?)',
          { replacements: [migrationName] }
        );
        
        console.log(`✅ Completed migration: ${migrationName}`);
      } catch (error) {
        console.error(`❌ Failed migration: ${migrationName}`);
        console.error('Error:', error.message);
        throw error;
      }
    }

    console.log('🎉 All migrations completed successfully!');
    
    // Show final table list
    const [tables] = await sequelize.query('SHOW TABLES');
    console.log('📋 Current database tables:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`  - ${tableName}`);
    });

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}

module.exports = { runMigrations };
