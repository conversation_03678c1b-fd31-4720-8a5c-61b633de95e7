@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-background text-text antialiased;
  }
  
  * {
    @apply border-neutral-200;
  }
}

@layer components {
  /* FormBooker Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500 shadow-sm;
  }
  
  .btn-accent {
    @apply btn bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500 shadow-sm;
  }
  
  .btn-outline {
    @apply btn border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-neutral-600 hover:bg-neutral-100 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* FormBooker Card Components */
  .card {
    @apply bg-white rounded-xl border border-neutral-200 shadow-soft;
  }
  
  .card-hover {
    @apply card hover:shadow-medium transition-shadow duration-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-neutral-200;
  }
  
  .card-body {
    @apply px-6 py-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-neutral-200 bg-neutral-50 rounded-b-xl;
  }
  
  /* FormBooker Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-neutral-300 rounded-lg text-sm placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-red-600 mt-1;
  }
  
  .form-help {
    @apply text-sm text-neutral-500 mt-1;
  }
  
  /* FormBooker Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-accent {
    @apply badge bg-accent-100 text-accent-800;
  }
  
  .badge-neutral {
    @apply badge bg-neutral-100 text-neutral-800;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-error {
    @apply badge bg-red-100 text-red-800;
  }
  
  /* FormBooker Stats Components */
  .stat-card {
    @apply card p-6 text-center;
  }
  
  .stat-value {
    @apply text-3xl font-bold text-neutral-900;
  }
  
  .stat-label {
    @apply text-sm font-medium text-neutral-600 mt-1;
  }
  
  /* FormBooker Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  .nav-link-inactive {
    @apply nav-link text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900;
  }
  
  /* FormBooker Modal */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50;
    animation: fadeIn 0.3s ease-out;
  }
  
  .modal-overlay.modal-closing {
    animation: fadeOut 0.3s ease-out;
  }
  
  .modal-content {
    @apply bg-white rounded-xl shadow-large max-w-md w-full mx-4;
  }
  
  .modal-form {
    @apply bg-white rounded-xl shadow-2xl border border-neutral-200;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    border-radius: 16px !important;
    overflow: hidden;
  }
  
  .modal-form.modal-closing {
    animation: modalSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Custom Scrollbar for Modal */
  .modal-scroll {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .modal-scroll::-webkit-scrollbar {
    width: 8px;
  }
  
  .modal-scroll::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  .modal-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
    border-radius: 4px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }
  
  .modal-scroll::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #94a3b8 0%, #64748b 100%);
  }
  
  .modal-scroll::-webkit-scrollbar-corner {
    background: #f8fafc;
  }
  
  /* Modal Form Body with Custom Scroll */
  .modal-form-body {
    max-height: calc(90vh - 140px);
    overflow-y: auto;
    padding-right: 4px;
    margin-right: -4px;
  }
  
  /* FormBooker Animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalSlideOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
