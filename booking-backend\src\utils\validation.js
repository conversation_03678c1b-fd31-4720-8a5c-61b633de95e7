/**
 * Validation Utilities
 * Helper functions for data validation
 */

const { body, param, query, validationResult } = require('express-validator');

/**
 * Common validation rules
 */
const commonValidations = {
  // ID validation
  id: param('id')
    .isInt({ min: 1 })
    .withMessage('Valid ID required'),

  // Pagination validation
  page: query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  // Search validation
  search: query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),

  // Sort validation
  sort: query('sort')
    .optional()
    .matches(/^[a-zA-Z_][a-zA-Z0-9_]*:(asc|desc)$/)
    .withMessage('Sort format must be "field:direction"'),

  // Date validation
  date: (field) => body(field)
    .isISO8601()
    .withMessage(`${field} must be a valid date (YYYY-MM-DD)`),

  dateTime: (field) => body(field)
    .isISO8601()
    .withMessage(`${field} must be a valid datetime`),

  time: (field) => body(field)
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage(`${field} must be a valid time (HH:MM)`),

  // String validation
  name: (field, min = 2, max = 100) => body(field)
    .trim()
    .isLength({ min, max })
    .withMessage(`${field} must be between ${min} and ${max} characters`)
    .matches(/^[a-zA-ZÀ-ỹ\s]+$/)
    .withMessage(`${field} must contain only letters and spaces`),

  description: (field, max = 500) => body(field)
    .optional()
    .trim()
    .isLength({ max })
    .withMessage(`${field} must not exceed ${max} characters`),

  // Email validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email required'),

  // Phone validation (Vietnam format)
  phone: body('phone')
    .matches(/^(0|\+84)[0-9]{9,10}$/)
    .withMessage('Valid Vietnamese phone number required'),

  // Password validation
  password: body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be between 6 and 128 characters'),

  // Enum validation
  enum: (field, values) => body(field)
    .isIn(values)
    .withMessage(`${field} must be one of: ${values.join(', ')}`),

  // Number validation
  positiveInt: (field) => body(field)
    .isInt({ min: 1 })
    .withMessage(`${field} must be a positive integer`),

  decimal: (field, min = 0) => body(field)
    .isDecimal({ decimal_digits: '0,2' })
    .custom(value => parseFloat(value) >= min)
    .withMessage(`${field} must be a valid decimal number >= ${min}`),

  // Boolean validation
  boolean: (field) => body(field)
    .optional()
    .isBoolean()
    .withMessage(`${field} must be a boolean value`),

  // Array validation
  array: (field, itemValidator) => body(field)
    .isArray()
    .withMessage(`${field} must be an array`)
    .custom((value) => {
      if (itemValidator) {
        return value.every(itemValidator);
      }
      return true;
    })
    .withMessage(`Invalid items in ${field} array`)
};

/**
 * User validation rules
 */
const userValidations = {
  register: [
    commonValidations.name('name'),
    commonValidations.email,
    commonValidations.password,
    commonValidations.phone,
    body('role')
      .optional()
      .isIn(['admin', 'staff', 'customer'])
      .withMessage('Role must be admin, staff, or customer')
  ],

  login: [
    commonValidations.email,
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],

  updateProfile: [
    commonValidations.name('name'),
    commonValidations.phone,
    body('dateOfBirth')
      .optional()
      .isISO8601()
      .withMessage('Date of birth must be a valid date'),
    body('gender')
      .optional()
      .isIn(['male', 'female', 'other'])
      .withMessage('Gender must be male, female, or other')
  ],

  changePassword: [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    commonValidations.password
  ]
};

/**
 * Booking validation rules
 */
const bookingValidations = {
  create: [
    body('customerId')
      .isInt({ min: 1 })
      .withMessage('Valid customer ID required'),
    body('serviceId')
      .isInt({ min: 1 })
      .withMessage('Valid service ID required'),
    body('employeeId')
      .isInt({ min: 1 })
      .withMessage('Valid employee ID required'),
    body('branchId')
      .isInt({ min: 1 })
      .withMessage('Valid branch ID required'),
    commonValidations.date('bookingDate'),
    commonValidations.time('startTime'),
    commonValidations.description('customerNote', 500)
  ],

  update: [
    commonValidations.id,
    body('status')
      .optional()
      .isIn(['pending', 'confirmed', 'cancelled', 'completed', 'no_show'])
      .withMessage('Invalid status'),
    commonValidations.description('staffNote', 500),
    body('paymentStatus')
      .optional()
      .isIn(['unpaid', 'deposit', 'paid'])
      .withMessage('Invalid payment status')
  ],

  query: [
    commonValidations.page,
    commonValidations.limit,
    query('status')
      .optional()
      .isIn(['pending', 'confirmed', 'cancelled', 'completed', 'no_show'])
      .withMessage('Invalid status filter'),
    query('branchId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Valid branch ID required'),
    query('dateFrom')
      .optional()
      .isISO8601()
      .withMessage('Valid start date required'),
    query('dateTo')
      .optional()
      .isISO8601()
      .withMessage('Valid end date required')
  ]
};

/**
 * Service validation rules
 */
const serviceValidations = {
  create: [
    commonValidations.name('name'),
    commonValidations.description('description', 1000),
    commonValidations.decimal('price'),
    body('duration')
      .isInt({ min: 15, max: 480 })
      .withMessage('Duration must be between 15 and 480 minutes'),
    body('categoryId')
      .isInt({ min: 1 })
      .withMessage('Valid category ID required')
  ],

  update: [
    commonValidations.id,
    commonValidations.name('name'),
    commonValidations.description('description', 1000),
    commonValidations.decimal('price'),
    body('duration')
      .optional()
      .isInt({ min: 15, max: 480 })
      .withMessage('Duration must be between 15 and 480 minutes'),
    commonValidations.boolean('isActive')
  ]
};

/**
 * Custom validation functions
 */
const customValidations = {
  // Check if date is in the future
  isFutureDate: (value) => {
    const inputDate = new Date(value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return inputDate >= today;
  },

  // Check if time is within business hours
  isBusinessHours: (value) => {
    const [hours, minutes] = value.split(':').map(Number);
    const timeInMinutes = hours * 60 + minutes;
    const startTime = 8 * 60; // 8:00 AM
    const endTime = 20 * 60; // 8:00 PM
    return timeInMinutes >= startTime && timeInMinutes <= endTime;
  },

  // Check if password is strong enough
  isStrongPassword: (value) => {
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
    
    return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
  },

  // Check if phone number is unique (requires database check)
  isUniquePhone: async (value, { req }) => {
    // This would need to be implemented with actual database check
    // For now, just return true
    return true;
  },

  // Check if email is unique (requires database check)
  isUniqueEmail: async (value, { req }) => {
    // This would need to be implemented with actual database check
    // For now, just return true
    return true;
  }
};

/**
 * Validation middleware
 */
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.param || error.path,
      message: error.msg || error.message,
      value: error.value,
      location: error.location
    }));
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: formattedErrors
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: process.env.API_VERSION || '1.0.0'
      }
    });
  }
  
  next();
};

/**
 * Sanitize input data
 */
const sanitizeInput = (data) => {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
};

/**
 * Validate ID parameter
 */
const validateIdParam = (paramName = 'id') => {
  return [
    param(paramName)
      .isInt({ min: 1 })
      .withMessage(`Valid ${paramName} is required`)
  ];
};

module.exports = {
  commonValidations,
  userValidations,
  bookingValidations,
  serviceValidations,
  customValidations,
  validateRequest,
  validateIdParam,
  sanitizeInput
};
