# Database Schema Documentation - Spa Booking System

## Database Overview
- **Database Type**: MySQL 8.0
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_unicode_ci
- **Engine**: InnoDB (for foreign key support)
- **Naming Convention**: snake_case for all database objects

## Complete Database Schema

### Step 1: Create Database
```sql
CREATE DATABASE IF NOT EXISTS booking_system
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;
USE booking_system;
javascript



### Step 2: Create Tables (Execute in this order)

#### 2.1. branches table
```sql
-- Chi nhánh spa/clinic
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'Tên chi nhánh',
    address TEXT NOT NULL COMMENT 'Địa chỉ chi nhánh',
    phone VARCHAR(20) NOT NULL COMMENT '<PERSON>ố điện thoại',
    email VARCHAR(100) COMMENT '<PERSON>ail chi nh<PERSON>',
    opening_time TIME DEFAULT '08:00:00' COMMENT 'Giờ mở cửa',
    closing_time TIME DEFAULT '20:00:00' COMMENT 'Giờ đóng cửa',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Trạng thái hoạt động',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.2. users table
sql


-- Người dùng hệ thống (admin, staff)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'staff', 'customer') NOT NULL DEFAULT 'customer',
    phone VARCHAR(20),
    branch_id INT COMMENT 'Chi nhánh làm việc (cho staff)',
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_branch_role (branch_id, role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.3. services table
sql


-- Dịch vụ spa
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'Tên dịch vụ',
    description TEXT COMMENT 'Mô tả dịch vụ',
    price DECIMAL(10,2) NOT NULL COMMENT 'Giá dịch vụ',
    duration_minutes INT NOT NULL COMMENT 'Thời gian thực hiện (phút)',
    image_url VARCHAR(500) COMMENT 'Ảnh dịch vụ',
    branch_id INT NOT NULL COMMENT 'Chi nhánh cung cấp',
    category VARCHAR(50) COMMENT 'Loại dịch vụ: facial, massage, nails, etc',
    is_active BOOLEAN DEFAULT TRUE,
    min_advance_booking_hours INT DEFAULT 2 COMMENT 'Số giờ tối thiểu đặt trước',
    max_advance_booking_days INT DEFAULT 30 COMMENT 'Số ngày tối đa đặt trước',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    INDEX idx_branch (branch_id),
    INDEX idx_active (is_active),
    INDEX idx_category (category),
    FULLTEXT idx_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.4. employees table
sql


-- Nhân viên
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    branch_id INT NOT NULL,
    avatar_url VARCHAR(500),
    specialization TEXT COMMENT 'Chuyên môn',
    bio TEXT COMMENT 'Giới thiệu',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    INDEX idx_branch (branch_id),
    INDEX idx_email (email),
    INDEX idx_active_branch (is_active, branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.5. employee_services table
sql


-- Nhân viên - Dịch vụ (nhiều-nhiều)
CREATE TABLE employee_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    service_id INT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'Dịch vụ chính của nhân viên',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_service (employee_id, service_id),
    INDEX idx_service (service_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.6. employee_schedules table
sql


-- Lịch làm việc nhân viên
CREATE TABLE employee_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    day_of_week TINYINT NOT NULL COMMENT '0=CN, 1=T2, ..., 6=T7',
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_start TIME COMMENT 'Giờ nghỉ trưa bắt đầu',
    break_end TIME COMMENT 'Giờ nghỉ trưa kết thúc',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_employee_day (employee_id, day_of_week),
    INDEX idx_active (is_active),
    CONSTRAINT chk_day_of_week CHECK (day_of_week BETWEEN 0 AND 6),
    CONSTRAINT chk_time_valid CHECK (start_time < end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.7. employee_time_offs table
sql


-- Ngày nghỉ của nhân viên
CREATE TABLE employee_time_offs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    date DATE NOT NULL,
    start_time TIME COMMENT 'NULL = cả ngày',
    end_time TIME COMMENT 'NULL = cả ngày',
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    INDEX idx_employee_date (employee_id, date),
    UNIQUE KEY unique_time_off (employee_id, date, start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.8. customers table
sql


-- Khách hàng
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20) NOT NULL,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    address TEXT,
    notes TEXT COMMENT 'Ghi chú về khách hàng',
    source VARCHAR(50) COMMENT 'Nguồn khách: walk-in, online, referral',
    preferred_branch_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (preferred_branch_id) REFERENCES branches(id) ON DELETE SET NULL,
    UNIQUE KEY unique_phone (phone),
    UNIQUE KEY unique_email (email),
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.9. bookings table
sql


-- Lịch hẹn/Booking
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'Mã booking: BK20250610001',
    customer_id INT NOT NULL,
    service_id INT NOT NULL,
    employee_id INT NOT NULL,
    branch_id INT NOT NULL,
    booking_date DATE NOT NULL COMMENT 'Ngày hẹn',
    start_time TIME NOT NULL COMMENT 'Giờ bắt đầu',
    end_time TIME NOT NULL COMMENT 'Giờ kết thúc (tự tính từ duration)',
    status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'no_show') NOT NULL DEFAULT 'pending',
    customer_note TEXT COMMENT 'Ghi chú từ khách',
    staff_note TEXT COMMENT 'Ghi chú từ nhân viên',
    payment_status ENUM('unpaid', 'deposit', 'paid') NOT NULL DEFAULT 'unpaid',
    total_amount DECIMAL(10,2) NOT NULL,
    deposit_amount DECIMAL(10,2) DEFAULT 0,
    cancelled_by INT COMMENT 'User ID người hủy',
    cancelled_at DATETIME,
    cancelled_reason TEXT,
    completed_at DATETIME,
    no_show_at DATETIME,
    created_by INT COMMENT 'User ID người tạo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (branch_id) REFERENCES branches(id),
    FOREIGN KEY (cancelled_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (status),
    INDEX idx_employee_date (employee_id, booking_date),
    INDEX idx_customer (customer_id),
    INDEX idx_branch_date (branch_id, booking_date),
    INDEX idx_composite (branch_id, booking_date, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.10. payments table
sql


-- Thanh toán
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id VARCHAR(100) UNIQUE COMMENT 'Mã giao dịch từ payment gateway',
    booking_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    method ENUM('stripe', 'paypal', 'momo', 'cash', 'bank_transfer') NOT NULL,
    type ENUM('deposit', 'full_payment', 'refund') NOT NULL,
    status ENUM('pending', 'processing', 'success', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    gateway_response JSON COMMENT 'Response từ payment gateway',
    paid_at DATETIME,
    refunded_amount DECIMAL(10,2) DEFAULT 0,
    refunded_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id),
    INDEX idx_booking (booking_id),
    INDEX idx_status (status),
    INDEX idx_method (method),
    INDEX idx_transaction (transaction_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.11. notifications table
sql


-- Thông báo
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    recipient_type ENUM('user', 'customer') NOT NULL,
    user_id INT COMMENT 'NULL nếu gửi cho customer',
    customer_id INT COMMENT 'NULL nếu gửi cho user',
    type ENUM('email', 'sms', 'push', 'in_app') NOT NULL,
    template VARCHAR(50) COMMENT 'Template name',
    subject VARCHAR(200),
    content TEXT NOT NULL,
    metadata JSON COMMENT 'Dữ liệu bổ sung',
    scheduled_at DATETIME COMMENT 'Thời gian dự kiến gửi',
    status ENUM('pending', 'sending', 'sent', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    sent_at DATETIME,
    failed_at DATETIME,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_user (user_id),
    INDEX idx_customer (customer_id),
    INDEX idx_scheduled (scheduled_at, status),
    INDEX idx_type_status (type, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.12. settings table
sql


-- Cài đặt hệ thống
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    branch_id INT COMMENT 'NULL = global setting',
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
    category VARCHAR(50) COMMENT 'booking, payment, notification, general',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE COMMENT 'Có thể xem public qua API',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE KEY unique_branch_key (branch_id, setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
2.13. audit_logs table
sql


-- Audit logs for tracking changes
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(50) NOT NULL COMMENT 'create, update, delete, login, etc',
    entity_type VARCHAR(50) NOT NULL COMMENT 'booking, payment, user, etc',
    entity_id INT NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_action (action),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
Sample Data (For Development)
sql


-- Insert sample branches
INSERT INTO branches (name, address, phone, email) VALUES
('Spa Luxury - Quận 1', '123 Nguyễn Huệ, Q1, TP.HCM', '0281234567', '<EMAIL>'),
('Spa Luxury - Quận 7', '456 Nguyễn Văn Linh, Q7, TP.HCM', '0287654321', '<EMAIL>');
-- Insert sample services INSERT INTO services (name, description, price, duration_minutes, branch_id, category) VALUES ('Massage Thư Giãn', 'Massage toàn thân với tinh dầu thiên nhiên', 500000, 60, 1, 'massage'), ('Chăm Sóc Da Mặt', 'Làm sạch và dưỡng da mặt chuyên sâu', 800000, 90, 1, 'facial'), ('Làm Móng Gel', 'Làm móng tay với sơn gel cao cấp', 300000, 45, 1, 'nails');
-- Insert default settings INSERT INTO settings (setting_key, setting_value, setting_type, category, description) VALUES ('booking.advance_hours', '2', 'number', 'booking', 'Số giờ tối thiểu đặt trước'), ('booking.deposit_percentage', '30', 'number', 'booking', 'Phần trăm đặt cọc'), ('notification.reminder_hours', '24', 'number', 'notification', 'Gửi nhắc nhở trước bao nhiêu giờ'), ('payment.enable_stripe', 'true', 'boolean', 'payment', 'Bật thanh toán Stripe'), ('payment.enable_momo', 'true', 'boolean', 'payment', 'Bật thanh toán MoMo');
javascript



## Database Maintenance

### Indexes Strategy
1. **Primary Keys**: Tự động được index
2. **Foreign Keys**: Index để tăng performance JOIN
3. **Unique Constraints**: Tự động được index
4. **Search Fields**: Index các field thường xuyên WHERE (email, phone, status)
5. **Date Fields**: Index cho các query theo thời gian
6. **Composite Indexes**: Cho các query phức tạp thường dùng

### Partitioning Strategy (For Large Scale)
```sql
-- Partition bookings table by month (when > 1M records)
ALTER TABLE bookings 
PARTITION BY RANGE (YEAR(booking_date) * 100 + MONTH(booking_date)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- Add more partitions as needed
);
