'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  Calendar, 
  Phone, 
  Mail, 
  Eye, 
  MessageCircle,
  Filter,
  ChevronDown,
  Clock,
  MapPin,
  User,
  X
} from 'lucide-react';

/**
 * Bookings Management Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function BookingsPage() {
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [statusFilter, setStatusFilter] = useState('All');
  const [timeFilter, setTimeFilter] = useState('This Week');

  // Mock data - TODO: Replace with real API data
  const bookings = [
    {
      id: 1,
      customerName: '<PERSON>',
      service: 'Hair Cut',
      branch: 'Main Location',
      date: 'Dec 15, 2024',
      time: '2:00 PM',
      phone: '+1234567890',
      email: '<EMAIL>',
      status: 'Confirmed',
      specialRequests: 'Please use organic products',
      formName: 'Hair Cut Booking',
      createdAt: '2024-12-10T10:30:00Z'
    },
    {
      id: 2,
      customerName: '<PERSON>',
      service: 'Massage',
      branch: 'Downtown Branch',
      date: 'Dec 16, 2024',
      time: '10:00 AM',
      phone: '+1987654321',
      email: '<EMAIL>',
      status: 'Pending',
      specialRequests: 'First time customer',
      formName: 'Massage Booking',
      createdAt: '2024-12-11T14:15:00Z'
    },
    {
      id: 3,
      customerName: '<PERSON> Johnson',
      service: 'Facial',
      branch: 'Main Location',
      date: 'Dec 17, 2024',
      time: '3:30 PM',
      phone: '+1555666777',
      email: '<EMAIL>',
      status: 'Confirmed',
      specialRequests: '',
      formName: 'Facial Treatment',
      createdAt: '2024-12-12T09:45:00Z'
    }
  ];

  const statusOptions = ['All', 'Pending', 'Confirmed', 'Completed', 'Cancelled'];
  const timeOptions = ['All Time', 'Today', 'This Week', 'This Month'];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Confirmed':
        return 'badge-success';
      case 'Pending':
        return 'badge-warning';
      case 'Completed':
        return 'badge-primary';
      case 'Cancelled':
        return 'badge-error';
      default:
        return 'badge-neutral';
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesStatus = statusFilter === 'All' || booking.status === statusFilter;
    // TODO: Implement time filtering logic
    return matchesStatus;
  });

  const handleStatusChange = (bookingId, newStatus) => {
    // TODO: Implement status update API call
    console.log(`Updating booking ${bookingId} to ${newStatus}`);
  };

  const handleContact = (type, contact) => {
    if (type === 'email') {
      window.open(`mailto:${contact}`);
    } else if (type === 'phone') {
      window.open(`tel:${contact}`);
    }
  };

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-text">All Bookings</h1>
            <p className="mt-2 text-neutral-600">
              Manage your customer appointments and bookings
            </p>
          </div>

          {/* Filters */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-neutral-500" />
              <span className="text-sm font-medium text-neutral-700">Filter:</span>
            </div>
            
            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-input w-auto"
            >
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>

            {/* Time Filter */}
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="form-input w-auto"
            >
              {timeOptions.map(time => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
          </div>

          {/* Bookings List */}
          <div className="space-y-4">
            {filteredBookings.length > 0 ? (
              filteredBookings.map((booking) => (
                <div key={booking.id} className="card">
                  <div className="card-body">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      {/* Booking Info */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-lg font-semibold text-text">
                              {booking.customerName} - {booking.service}
                            </h3>
                            <div className="flex items-center space-x-4 text-sm text-neutral-600 mt-1">
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                📅 {booking.date} at {booking.time}
                              </div>
                              <div className="flex items-center">
                                <MapPin className="h-4 w-4 mr-1" />
                                {booking.branch}
                              </div>
                            </div>
                          </div>
                          <span className={`badge ${getStatusBadge(booking.status)}`}>
                            {booking.status}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center text-neutral-600">
                            <Phone className="h-4 w-4 mr-2" />
                            📞 {booking.phone}
                          </div>
                          <div className="flex items-center text-neutral-600">
                            <Mail className="h-4 w-4 mr-2" />
                            📧 {booking.email}
                          </div>
                        </div>

                        {booking.specialRequests && (
                          <div className="mt-3 p-3 bg-neutral-50 rounded-lg">
                            <p className="text-sm text-neutral-700">
                              <span className="font-medium">Special Requests:</span> {booking.specialRequests}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-2">
                        {/* Status Dropdown */}
                        <select
                          value={booking.status}
                          onChange={(e) => handleStatusChange(booking.id, e.target.value)}
                          className="form-input text-sm"
                        >
                          <option value="Pending">Pending</option>
                          <option value="Confirmed">Confirmed</option>
                          <option value="Completed">Completed</option>
                          <option value="Cancelled">Cancelled</option>
                        </select>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => setSelectedBooking(booking)}
                            className="btn-outline btn-sm"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </button>
                          <button
                            onClick={() => handleContact('phone', booking.phone)}
                            className="btn-outline btn-sm"
                          >
                            <Phone className="h-4 w-4 mr-1" />
                            Contact
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              /* Empty State */
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Calendar className="h-12 w-12 text-neutral-400" />
                </div>
                <h3 className="text-xl font-semibold text-text mb-2">
                  No bookings found
                </h3>
                <p className="text-neutral-600 mb-6 max-w-md mx-auto">
                  {statusFilter !== 'All' || timeFilter !== 'All Time' 
                    ? 'Try adjusting your filters to see more bookings.'
                    : 'When customers book through your forms, their appointments will appear here.'
                  }
                </p>
              </div>
            )}
          </div>

          {/* Booking Details Modal */}
          {selectedBooking && (
            <div className="modal-overlay flex items-center justify-center p-4">
              <div className="modal-content animate-scale-in">
                <div className="flex items-center justify-between p-6 border-b border-neutral-200">
                  <h2 className="text-xl font-semibold text-text">Booking Details</h2>
                  <button
                    onClick={() => setSelectedBooking(null)}
                    className="p-1 hover:bg-neutral-100 rounded-lg transition-colors duration-200"
                  >
                    <X className="h-5 w-5 text-neutral-500" />
                  </button>
                </div>

                <div className="p-6 space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-neutral-700">Customer:</span>
                      <p className="text-text">{selectedBooking.customerName}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Service:</span>
                      <p className="text-text">{selectedBooking.service}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Branch:</span>
                      <p className="text-text">{selectedBooking.branch}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Date:</span>
                      <p className="text-text">{selectedBooking.date}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Time:</span>
                      <p className="text-text">{selectedBooking.time}</p>
                    </div>
                    <div>
                      <span className="font-medium text-neutral-700">Phone:</span>
                      <p className="text-text">{selectedBooking.phone}</p>
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium text-neutral-700">Email:</span>
                      <p className="text-text">{selectedBooking.email}</p>
                    </div>
                  </div>

                  {selectedBooking.specialRequests && (
                    <div>
                      <span className="font-medium text-neutral-700">Special Requests:</span>
                      <p className="text-text mt-1 p-3 bg-neutral-50 rounded-lg">
                        "{selectedBooking.specialRequests}"
                      </p>
                    </div>
                  )}

                  <div>
                    <span className="font-medium text-neutral-700">Status:</span>
                    <select
                      value={selectedBooking.status}
                      onChange={(e) => handleStatusChange(selectedBooking.id, e.target.value)}
                      className="form-input mt-1"
                    >
                      <option value="Pending">Pending</option>
                      <option value="Confirmed">Confirmed</option>
                      <option value="Completed">Completed</option>
                      <option value="Cancelled">Cancelled</option>
                    </select>
                  </div>
                </div>

                <div className="flex space-x-3 p-6 border-t border-neutral-200">
                  <button
                    onClick={() => handleContact('email', selectedBooking.email)}
                    className="btn-outline flex-1"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </button>
                  <button
                    onClick={() => handleContact('phone', selectedBooking.phone)}
                    className="btn-outline flex-1"
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call Customer
                  </button>
                </div>

                <div className="flex space-x-3 px-6 pb-6">
                  <button className="btn-primary flex-1">
                    Edit Booking
                  </button>
                  <button className="btn-outline flex-1 text-red-600 border-red-300 hover:bg-red-50">
                    Cancel Booking
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
