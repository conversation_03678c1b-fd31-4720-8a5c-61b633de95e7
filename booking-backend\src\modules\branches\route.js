/**
 * Branches Routes
 * API routes for branch management
 */

const express = require('express');
const router = express.Router();
const BranchesController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validatePagination, validateIdParam } = require('../../middleware/validation');
const { adminLimiter, generalLimiter } = require('../../middleware/rateLimiter');
const { body, query, param } = require('express-validator');

// Validation rules
const createBranchValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch name must be between 2 and 100 characters'),
  body('address')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters'),
  body('phone')
    .matches(/^(0|\+84)[0-9]{9,10}$/)
    .withMessage('Valid Vietnamese phone number required'),
  body('email')
    .optional({ nullable: true })
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        throw new Error('Valid email required');
      }
      return true;
    }),
  body('city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('district')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length < 2 || value.length > 50) {
        throw new Error('District must be between 2 and 50 characters');
      }
      return true;
    }),
  body('ward')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length < 2 || value.length > 50) {
        throw new Error('Ward must be between 2 and 50 characters');
      }
      return true;
    }),
  body('latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('openTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Open time must be in HH:MM format'),
  body('closeTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Close time must be in HH:MM format'),
  body('workingDays')
    .optional()
    .isArray()
    .withMessage('Working days must be an array'),
  body('workingDays.*')
    .optional()
    .isIn(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
    .withMessage('Invalid working day'),
  body('managerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid manager ID required'),
  body('description')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length > 1000) {
        throw new Error('Description must not exceed 1000 characters');
      }
      return true;
    }),
  body('images')
    .optional()
    .isArray()
    .withMessage('Images must be an array')
];

const updateBranchValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Branch name must be between 2 and 100 characters'),
  body('address')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Address must be between 10 and 500 characters'),
  body('phone')
    .optional()
    .matches(/^(0|\+84)[0-9]{9,10}$/)
    .withMessage('Valid Vietnamese phone number required'),
  body('email')
    .optional({ nullable: true })
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        throw new Error('Valid email required');
      }
      return true;
    }),
  body('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('district')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length < 2 || value.length > 50) {
        throw new Error('District must be between 2 and 50 characters');
      }
      return true;
    }),
  body('ward')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length < 2 || value.length > 50) {
        throw new Error('Ward must be between 2 and 50 characters');
      }
      return true;
    }),
  body('latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('openTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Open time must be in HH:MM format'),
  body('closeTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Close time must be in HH:MM format'),
  body('workingDays')
    .optional()
    .isArray()
    .withMessage('Working days must be an array'),
  body('workingDays.*')
    .optional()
    .isIn(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
    .withMessage('Invalid working day'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('managerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid manager ID required'),
  body('description')
    .optional({ nullable: true })
    .trim()
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (value.length > 1000) {
        throw new Error('Description must not exceed 1000 characters');
      }
      return true;
    }),
  body('images')
    .optional()
    .isArray()
    .withMessage('Images must be an array')
];

const toggleStatusValidation = [
  body('isActive')
    .isBoolean()
    .withMessage('isActive is required and must be a boolean')
];

const nearbyBranchesValidation = [
  query('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Valid latitude is required'),
  query('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Valid longitude is required'),
  query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Radius must be between 0.1 and 100 km'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
];

const updateImagesValidation = [
  body('images')
    .isArray()
    .withMessage('Images must be an array'),
  body('images.*')
    .isURL()
    .withMessage('Each image must be a valid URL')
];

const getBranchesValidation = [
  query('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  query('district')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('District must be between 2 and 50 characters'),
  query('isActive')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isActive must be true or false'),
  query('hasLocation')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('hasLocation must be true or false'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/branches
 * @desc    Get all branches with pagination and filters
 * @access  Private (Admin, Staff, Customer)
 */
router.get('/', 
  authorize(['admin', 'staff', 'customer']),
  getBranchesValidation,
  validatePagination,
  validateRequest,
  BranchesController.getBranches
);

/**
 * @route   GET /api/branches/stats
 * @desc    Get branch statistics
 * @access  Private (Admin)
 */
router.get('/stats', 
  authorize(['admin']),
  BranchesController.getBranchStats
);

/**
 * @route   GET /api/branches/active
 * @desc    Get active branches (simple list)
 * @access  Private (All authenticated users)
 */
router.get('/active', 
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  BranchesController.getActiveBranches
);

/**
 * @route   GET /api/branches/nearby
 * @desc    Find nearby branches
 * @access  Private (All authenticated users)
 */
router.get('/nearby', 
  nearbyBranchesValidation,
  validateRequest,
  BranchesController.findNearbyBranches
);

/**
 * @route   GET /api/branches/city/:city
 * @desc    Get branches by city
 * @access  Private (All authenticated users)
 */
router.get('/city/:city', 
  param('city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  BranchesController.getBranchesByCity
);

/**
 * @route   GET /api/branches/:id
 * @desc    Get branch by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', 
  validateIdParam(),
  validateRequest,
  BranchesController.getBranchById
);

/**
 * @route   POST /api/branches
 * @desc    Create new branch
 * @access  Private (Admin, Customer)
 */
router.post('/', 
  authorize(['admin', 'customer']),
  adminLimiter,
  createBranchValidation,
  validateRequest,
  BranchesController.createBranch
);

/**
 * @route   PUT /api/branches/:id
 * @desc    Update branch
 * @access  Private (Admin, Customer)
 */
router.put('/:id', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  updateBranchValidation,
  validateRequest,
  BranchesController.updateBranch
);

/**
 * @route   DELETE /api/branches/:id
 * @desc    Delete branch (soft delete)
 * @access  Private (Admin, Customer)
 */
router.delete('/:id', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  BranchesController.deleteBranch
);

/**
 * @route   PATCH /api/branches/:id/status
 * @desc    Toggle branch status
 * @access  Private (Admin, Customer)
 */
router.patch('/:id/status', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  toggleStatusValidation,
  validateRequest,
  BranchesController.toggleBranchStatus
);

/**
 * @route   POST /api/branches/:id/images
 * @desc    Update branch images
 * @access  Private (Admin, Customer)
 */
router.post('/:id/images', 
  authorize(['admin', 'customer']),
  adminLimiter,
  validateIdParam(),
  updateImagesValidation,
  validateRequest,
  BranchesController.updateBranchImages
);

/**
 * @route   GET /api/branches/:id/open-status
 * @desc    Check if branch is open
 * @access  Private (All authenticated users)
 */
router.get('/:id/open-status', 
  validateIdParam(),
  query('datetime')
    .optional()
    .isISO8601()
    .withMessage('Datetime must be in ISO8601 format'),
  validateRequest,
  BranchesController.checkBranchOpenStatus
);

module.exports = router;
