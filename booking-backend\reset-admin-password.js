/**
 * Reset Admin Password Script
 * Reset the admin user password to a known value for testing
 */

const { sequelize } = require('./src/database/connection');
const User = require('./src/modules/auth/model');

async function resetAdminPassword() {
  try {
    console.log('🔧 Resetting admin password...\n');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Find admin user
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    
    if (!adminUser) {
      console.log('❌ No admin user found');
      return;
    }

    console.log('✅ Admin user found:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Role: ${adminUser.role}`);

    // Reset password to known value
    const newPassword = 'admin123456';
    await adminUser.update({ password: newPassword });

    console.log('\n✅ Admin password reset successfully!');
    console.log('\n📋 Admin Login Credentials:');
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Password: ${newPassword}`);
    console.log(`   Role: ${adminUser.role}`);

  } catch (error) {
    console.error('❌ Error resetting admin password:', error.message);
    console.error(error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run the reset
resetAdminPassword();
