const axios = require('axios');

async function testFormStructure() {
  try {
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    const token = loginRes.data.data.token;
    
    const formsRes = await axios.get('http://localhost:3000/api/forms?limit=1', {
      headers: { 'Authorization': `<PERSON><PERSON> ${token}` }
    });
    
    if (formsRes.data.success && formsRes.data.data.forms.length > 0) {
      const form = formsRes.data.data.forms[0];
      console.log('Form structure:');
      console.log('Keys:', Object.keys(form));
      console.log('Service type:', typeof form.service, form.service);
      console.log('Branch type:', typeof form.branch, form.branch);
      console.log('BookingsThisMonth type:', typeof form.bookingsThisMonth, form.bookingsThisMonth);
      console.log('LastBooking type:', typeof form.lastBooking, form.lastBooking);
    } else {
      console.log('No forms found');
    }
  } catch(e) {
    console.error('Error:', e.message);
  }
}

testFormStructure();
