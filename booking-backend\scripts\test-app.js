/**
 * Test Application Script
 * Test the application without database connection
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');

// Import configurations
const config = require('../src/config/environment');

// Import middleware
const { errorHandler, notFound } = require('../src/middleware/errorHandler');
const { generalLimiter } = require('../src/middleware/rateLimiter');
const { sanitizeRequest } = require('../src/middleware/validation');

// Import utilities
const logger = require('../src/utils/logger');

console.log('🚀 Testing Spa Booking System Backend...');
console.log('📋 Configuration loaded:', {
  app: config.app,
  database: {
    host: config.database.host,
    database: config.database.database,
    alter: config.database.alter
  }
});

// Create Express application
const app = express();

// Trust proxy
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: config.security.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

// Rate limiting
app.use('/api/', generalLimiter);

// Body parsing middleware
app.use(express.json({ 
  limit: '10mb',
  type: 'application/json'
}));
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb',
  type: 'application/x-www-form-urlencoded'
}));

// Compression middleware
app.use(compression());

// Request sanitization
app.use(sanitizeRequest);

// Logging middleware
app.use(morgan('dev'));

// Custom request logging
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.request(req, res, duration);
  });
  
  next();
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.env,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        status: 'not_connected',
        message: 'Database connection skipped for testing'
      }
    };

    res.status(200).json(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: config.app.name,
    version: config.app.version,
    environment: config.app.env,
    documentation: `${config.app.url}/api-docs`,
    endpoints: {
      health: '/health',
      docs: '/api-docs',
      api: '/api'
    }
  });
});

// Test endpoints
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working!',
    timestamp: new Date().toISOString(),
    config: {
      environment: config.app.env,
      version: config.app.version,
      port: config.app.port
    }
  });
});

app.get('/api/test/auth', (req, res) => {
  res.json({
    success: true,
    message: 'Auth endpoints ready (database connection required for full functionality)',
    endpoints: [
      'POST /api/auth/register',
      'POST /api/auth/login',
      'POST /api/auth/refresh',
      'GET /api/auth/profile',
      'PUT /api/auth/profile',
      'POST /api/auth/change-password',
      'POST /api/auth/forgot-password',
      'POST /api/auth/reset-password'
    ]
  });
});

app.get('/api/test/middleware', (req, res) => {
  res.json({
    success: true,
    message: 'All middleware loaded successfully',
    middleware: [
      'helmet - Security headers',
      'cors - Cross-origin resource sharing',
      'rate-limiter - Request rate limiting',
      'body-parser - JSON and URL-encoded parsing',
      'compression - Response compression',
      'sanitization - Input sanitization',
      'logging - Request logging'
    ]
  });
});

// Swagger documentation (only in non-production)
if (config.app.env !== 'production') {
  try {
    const swaggerUi = require('swagger-ui-express');
    const { swaggerSpec } = require('../src/config/swagger');
    
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: `${config.app.name} API Documentation`,
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true
      }
    }));
    
    // Swagger JSON endpoint
    app.get('/api-docs.json', (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });
    
    console.log('📚 Swagger documentation loaded');
  } catch (error) {
    console.warn('⚠️  Swagger documentation failed to load:', error.message);
  }
}

// 404 handler for undefined routes
app.use(notFound);

// Global error handler
app.use(errorHandler);

// Start server
const startTestServer = () => {
  try {
    const port = config.app.port;
    const server = app.listen(port, () => {
      console.log('✅ Test server started successfully!');
      console.log(`🌐 Server running on port ${port}`);
      console.log(`📍 Environment: ${config.app.env}`);
      console.log(`🔗 API URL: ${config.app.url}`);
      console.log(`📖 API Documentation: ${config.app.url}/api-docs`);
      console.log(`❤️  Health Check: ${config.app.url}/health`);
      console.log('');
      console.log('🧪 Test endpoints:');
      console.log(`   GET ${config.app.url}/api/test`);
      console.log(`   GET ${config.app.url}/api/test/auth`);
      console.log(`   GET ${config.app.url}/api/test/middleware`);
      console.log('');
      console.log('⚠️  Note: Database-dependent features are disabled for testing');
    });
    
    // Server error handling
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }
      
      const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
      
      switch (error.code) {
        case 'EACCES':
          console.error(`❌ ${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          console.error(`❌ ${bind} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
    
    return server;
    
  } catch (error) {
    console.error('❌ Failed to start test server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);
  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
startTestServer();
