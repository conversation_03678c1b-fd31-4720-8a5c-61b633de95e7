/**
 * Employee Model
 * Employee management (extends User model for staff members)
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Employee = sequelize.define('Employee', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Reference to User table'
  },
  employeeCode: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    field: 'employee_code',
    validate: {
      len: [3, 20],
      isAlphanumeric: true
    },
    comment: 'Unique employee code'
  },
  position: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      len: [2, 50],
      notEmpty: true
    },
    comment: 'Employee position/title'
  },
  department: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [2, 50]
    },
    comment: 'Department name'
  },
  specializations: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of service specializations'
  },
  qualifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of qualifications and certifications'
  },
  experience: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Work experience description'
  },
  skills: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of skills and expertise'
  },
  languages: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of spoken languages'
  },
  workingHours: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'working_hours',
    comment: 'Weekly working schedule'
  },
  hourlyRate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    field: 'hourly_rate',
    comment: 'Hourly rate in VND'
  },
  commissionRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    field: 'commission_rate',
    comment: 'Commission rate percentage'
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    field: 'start_date',
    comment: 'Employment start date'
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    field: 'end_date',
    comment: 'Employment end date'
  },
  contractType: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'full_time',
    validate: {
      isIn: [['full_time', 'part_time', 'contract', 'intern']]
    },
    field: 'contract_type',
    comment: 'Employment contract type'
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'active',
    validate: {
      isIn: [['active', 'inactive', 'on_leave', 'terminated']]
    },
    comment: 'Employee status'
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'branch_id',
    references: {
      model: 'branches',
      key: 'id'
    },
    comment: 'Primary branch assignment'
  },
  managerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'manager_id',
    references: {
      model: 'employees',
      key: 'id'
    },
    comment: 'Direct manager employee ID'
  },
  emergencyContact: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'emergency_contact',
    comment: 'Emergency contact information'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes about employee'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 5
    },
    comment: 'Average customer rating'
  },
  totalBookings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    field: 'total_bookings',
    comment: 'Total number of completed bookings'
  },
  isAvailable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_available',
    comment: 'Current availability status'
  }
}, {
  tableName: 'employees',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete
  
  // Indexes
  indexes: [
    { fields: ['user_id'] },
    { fields: ['employee_code'] },
    { fields: ['branch_id'] },
    { fields: ['manager_id'] },
    { fields: ['status'] },
    { fields: ['position'] },
    { fields: ['is_available'] },
    { fields: ['rating'] },
    { fields: ['created_at'] }
  ],
  
  // Scopes
  scopes: {
    active: {
      where: { status: 'active' }
    },
    available: {
      where: { status: 'active', isAvailable: true }
    },
    byBranch: (branchId) => ({
      where: { branchId, status: 'active' }
    }),
    bySpecialization: (specialization) => ({
      where: {
        specializations: {
          [sequelize.Sequelize.Op.contains]: [specialization]
        },
        status: 'active'
      }
    }),
    topRated: {
      where: {
        status: 'active',
        rating: { [sequelize.Sequelize.Op.gte]: 4.0 }
      },
      order: [['rating', 'DESC']]
    }
  }
});

// Instance methods
Employee.prototype.getFullInfo = async function() {
  const User = require('../auth/model');
  const user = await User.findByPk(this.userId, {
    attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
  });
  
  return {
    ...this.toJSON(),
    user: user ? user.toJSON() : null
  };
};

Employee.prototype.isWorkingDay = function(date = new Date()) {
  if (!this.workingHours) return true;
  
  const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
  return this.workingHours[dayName] && this.workingHours[dayName].isWorking;
};

Employee.prototype.getWorkingHoursForDay = function(date = new Date()) {
  if (!this.workingHours) return null;
  
  const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
  return this.workingHours[dayName] || null;
};

Employee.prototype.hasSpecialization = function(specialization) {
  return this.specializations && this.specializations.includes(specialization);
};

Employee.prototype.updateRating = async function(newRating) {
  // This would typically be called after a booking is completed
  // For now, just update the rating directly
  const currentRating = this.rating || 0;
  const totalBookings = this.totalBookings || 0;
  
  const updatedRating = totalBookings === 0 
    ? newRating 
    : ((currentRating * totalBookings) + newRating) / (totalBookings + 1);
  
  await this.update({
    rating: Math.round(updatedRating * 100) / 100, // Round to 2 decimal places
    totalBookings: totalBookings + 1
  });
};

// Class methods
Employee.getActiveEmployees = function(options = {}) {
  return this.scope('active').findAll(options);
};

Employee.getAvailableEmployees = function(options = {}) {
  return this.scope('available').findAll(options);
};

Employee.getEmployeesByBranch = function(branchId, options = {}) {
  return this.scope({ method: ['byBranch', branchId] }).findAll(options);
};

Employee.getEmployeesBySpecialization = function(specialization, options = {}) {
  return this.scope({ method: ['bySpecialization', specialization] }).findAll(options);
};

Employee.getTopRatedEmployees = function(options = {}) {
  return this.scope('topRated').findAll(options);
};

Employee.findByEmployeeCode = function(employeeCode) {
  return this.findOne({ where: { employeeCode } });
};

Employee.findByUserId = function(userId) {
  return this.findOne({ where: { userId } });
};

// Define associations
Employee.associate = (models) => {
  // Employee belongs to User
  Employee.belongsTo(models.users, {
    foreignKey: 'user_id',
    as: 'user'
  });

  // Employee belongs to Branch
  Employee.belongsTo(models.branches, {
    foreignKey: 'branch_id',
    as: 'branch'
  });

  // Employee belongs to Employee (manager)
  Employee.belongsTo(models.employees, {
    foreignKey: 'manager_id',
    as: 'manager'
  });

  // Employee has many Employees (subordinates)
  Employee.hasMany(models.employees, {
    foreignKey: 'manager_id',
    as: 'subordinates'
  });

  // Employee has many Bookings
  Employee.hasMany(models.bookings, {
    foreignKey: 'employee_id',
    as: 'bookings'
  });

  // Employee belongs to many Services
  Employee.belongsToMany(models.services, {
    through: 'employee_services',
    foreignKey: 'employee_id',
    otherKey: 'service_id',
    as: 'services'
  });
};

module.exports = Employee;
