/**
 * Environment Variables Test
 * Test if environment variables are loaded correctly
 */

console.log('=== Before dotenv.config() ===');
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('NODE_ENV:', process.env.NODE_ENV);

// Load environment
require('dotenv').config();

console.log('\n=== After dotenv.config() ===');
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_DATABASE:', process.env.DB_DATABASE);

// Test slug generation
const { generateSlug } = require('./src/utils/helpers');

console.log('\n=== Slug Generation Test ===');
const testNames = [
  'Hair Cut Booking Form',
  'Facial Treatment Form',
  'Special Service @ Spa',
  'Test Form 123!'
];

testNames.forEach(name => {
  const slug = generateSlug(name);
  console.log(`"${name}" -> "${slug}"`);
});

// Test URL generation
console.log('\n=== URL Generation Test ===');
const testSlug = 'hair-cut-booking-form';
const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4000';

const publicUrl = `${frontendUrl}/book/${testSlug}`;
const embedCode = `<iframe src="${frontendUrl}/book/${testSlug}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`;

console.log('Frontend URL:', frontendUrl);
console.log('Public URL:', publicUrl);
console.log('Embed Code Length:', embedCode.length);
console.log('Embed Code Preview:', embedCode.substring(0, 100) + '...');

console.log('\n=== Test Result ===');
console.log('Environment Loading:', process.env.FRONTEND_URL ? '✅ SUCCESS' : '❌ FAILED');
console.log('Slug Generation:', testSlug ? '✅ SUCCESS' : '❌ FAILED');
console.log('URL Generation:', publicUrl.includes('/book/') ? '✅ SUCCESS' : '❌ FAILED');
console.log('Embed Generation:', embedCode.includes('<iframe') ? '✅ SUCCESS' : '❌ FAILED');
