/**
 * Users Model
 * Extended user management (uses same User model from auth)
 */

// Re-export the User model from auth module
const User = require('../auth/model');

// Additional class methods for user management
User.getActiveUsers = function(options = {}) {
  return this.scope('active').findAll({
    ...options,
    attributes: { exclude: ['password', 'emailVerificationToken', 'passwordResetToken'] }
  });
};

User.getUsersByRole = function(role, options = {}) {
  return this.scope('withoutSensitive').findAll({
    where: { role, isActive: true },
    ...options
  });
};

User.searchUsers = function(searchTerm, options = {}) {
  const { Op } = require('sequelize');
  return this.scope('withoutSensitive').findAll({
    where: {
      isActive: true,
      [Op.or]: [
        { name: { [Op.like]: `%${searchTerm}%` } },
        { email: { [Op.like]: `%${searchTerm}%` } },
        { phone: { [Op.like]: `%${searchTerm}%` } }
      ]
    },
    ...options
  });
};

User.getUserStats = async function() {
  const { Op } = require('sequelize');
  const sequelize = require('../../database/connection').sequelize;
  
  const stats = await this.findAll({
    attributes: [
      'role',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    where: { isActive: true },
    group: ['role'],
    raw: true
  });
  
  const totalUsers = await this.count({ where: { isActive: true } });
  const newUsersThisMonth = await this.count({
    where: {
      isActive: true,
      createdAt: {
        [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      }
    }
  });
  
  return {
    total: totalUsers,
    newThisMonth: newUsersThisMonth,
    byRole: stats.reduce((acc, stat) => {
      acc[stat.role] = parseInt(stat.count);
      return acc;
    }, {})
  };
};

module.exports = User;
