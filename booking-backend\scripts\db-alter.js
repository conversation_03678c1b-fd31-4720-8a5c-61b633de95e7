/**
 * Database Alter CLI Script
 * Command line interface for database operations
 */

const DatabaseSyncService = require('../src/database/sync');
const { sequelize, DataTypes } = require('../src/database/connection');
const logger = require('../src/utils/logger');

const commands = {
  /**
   * Sync all models to database
   */
  'sync-all': async () => {
    console.log('🔄 Syncing all models...');
    await DatabaseSyncService.syncDatabase();
    console.log('✅ All models synced successfully');
  },

  /**
   * Sync a specific model
   * Usage: npm run db:sync:model ModelName
   */
  'sync-model': async (modelName) => {
    if (!modelName) {
      throw new Error('Model name is required. Usage: npm run db:sync:model ModelName');
    }
    
    console.log(`🔄 Syncing model: ${modelName}...`);
    await DatabaseSyncService.syncModel(modelName);
    console.log(`✅ Model ${modelName} synced successfully`);
  },

  /**
   * Check table structure for a model
   * Usage: npm run db:check ModelName
   */
  'check-table': async (modelName) => {
    if (!modelName) {
      throw new Error('Model name is required. Usage: npm run db:check ModelName');
    }
    
    console.log(`🔍 Checking table structure for: ${modelName}...`);
    const structure = await DatabaseSyncService.checkTableStructure(modelName);
    console.log('📋 Table structure:');
    console.table(structure);
  },

  /**
   * Alter a table column
   * Usage: npm run db:alter tableName columnName columnType
   */
  'alter-column': async (tableName, columnName, columnType) => {
    if (!tableName || !columnName || !columnType) {
      throw new Error('Usage: npm run db:alter tableName columnName columnType');
    }
    
    // Parse column type
    let columnDefinition;
    try {
      columnDefinition = parseColumnType(columnType);
    } catch (error) {
      throw new Error(`Invalid column type: ${columnType}. ${error.message}`);
    }
    
    console.log(`🔧 Altering column ${columnName} in table ${tableName}...`);
    await DatabaseSyncService.alterTableColumn(tableName, columnName, columnDefinition);
    console.log(`✅ Column ${columnName} altered successfully`);
  },

  /**
   * Add a column to a table
   * Usage: npm run db:add tableName columnName columnType
   */
  'add-column': async (tableName, columnName, columnType) => {
    if (!tableName || !columnName || !columnType) {
      throw new Error('Usage: npm run db:add tableName columnName columnType');
    }
    
    // Parse column type
    let columnDefinition;
    try {
      columnDefinition = parseColumnType(columnType);
    } catch (error) {
      throw new Error(`Invalid column type: ${columnType}. ${error.message}`);
    }
    
    console.log(`➕ Adding column ${columnName} to table ${tableName}...`);
    await DatabaseSyncService.addTableColumn(tableName, columnName, columnDefinition);
    console.log(`✅ Column ${columnName} added successfully`);
  },

  /**
   * Remove a column from a table
   * Usage: npm run db:remove tableName columnName
   */
  'remove-column': async (tableName, columnName) => {
    if (!tableName || !columnName) {
      throw new Error('Usage: npm run db:remove tableName columnName');
    }
    
    console.log(`⚠️  WARNING: This will permanently delete column ${columnName} from table ${tableName}`);
    console.log('🗑️  Removing column...');
    await DatabaseSyncService.removeTableColumn(tableName, columnName);
    console.log(`✅ Column ${columnName} removed successfully`);
  },

  /**
   * List all tables in the database
   */
  'list-tables': async () => {
    console.log('📋 Listing all tables...');
    const tables = await DatabaseSyncService.getAllTables();
    console.log('Tables in database:');
    tables.forEach(table => console.log(`  - ${table}`));
  },

  /**
   * Show sync configuration
   */
  'show-config': async () => {
    console.log('⚙️  Database sync configuration:');
    const config = DatabaseSyncService.getSyncConfig();
    console.log(JSON.stringify(config, null, 2));
  },

  /**
   * Test database connection
   */
  'test-connection': async () => {
    console.log('🔌 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
  },

  /**
   * Show help
   */
  'help': async () => {
    console.log(`
📚 Database Alter CLI Commands:

Basic Operations:
  sync-all                           - Sync all models to database
  sync-model <ModelName>             - Sync specific model
  check-table <ModelName>            - Check table structure
  test-connection                    - Test database connection
  list-tables                        - List all tables
  show-config                        - Show sync configuration

Column Operations:
  alter-column <table> <col> <type>  - Alter existing column
  add-column <table> <col> <type>    - Add new column
  remove-column <table> <col>        - Remove column (DANGEROUS!)

Column Types:
  STRING(length)                     - VARCHAR with length
  TEXT                               - TEXT field
  INTEGER                            - Integer number
  DECIMAL(precision,scale)           - Decimal number
  BOOLEAN                            - Boolean true/false
  DATE                               - Date only
  DATETIME                           - Date and time
  TIME                               - Time only

Examples:
  npm run db:sync:model User
  npm run db:check User
  npm run db:add users email STRING(100)
  npm run db:alter users phone STRING(20)
  npm run db:remove users old_field

⚠️  WARNING: Always backup your database before running alter operations!
    `);
  }
};

/**
 * Parse column type string to Sequelize DataType
 */
const parseColumnType = (typeString) => {
  const type = typeString.toUpperCase();
  
  if (type === 'STRING' || type.startsWith('STRING(')) {
    const match = type.match(/STRING\((\d+)\)/);
    const length = match ? parseInt(match[1]) : 255;
    return { type: DataTypes.STRING(length) };
  }
  
  if (type === 'TEXT') {
    return { type: DataTypes.TEXT };
  }
  
  if (type === 'INTEGER' || type === 'INT') {
    return { type: DataTypes.INTEGER };
  }
  
  if (type.startsWith('DECIMAL(')) {
    const match = type.match(/DECIMAL\((\d+),(\d+)\)/);
    if (match) {
      const precision = parseInt(match[1]);
      const scale = parseInt(match[2]);
      return { type: DataTypes.DECIMAL(precision, scale) };
    }
    return { type: DataTypes.DECIMAL };
  }
  
  if (type === 'BOOLEAN' || type === 'BOOL') {
    return { type: DataTypes.BOOLEAN };
  }
  
  if (type === 'DATE') {
    return { type: DataTypes.DATEONLY };
  }
  
  if (type === 'DATETIME') {
    return { type: DataTypes.DATE };
  }
  
  if (type === 'TIME') {
    return { type: DataTypes.TIME };
  }
  
  throw new Error(`Unsupported column type: ${typeString}`);
};

/**
 * Main CLI function
 */
const runCommand = async () => {
  const [,, command, ...args] = process.argv;
  
  if (!command || command === 'help') {
    await commands.help();
    return;
  }
  
  if (!commands[command]) {
    console.error(`❌ Unknown command: ${command}`);
    console.log('Run "npm run db:help" for available commands');
    process.exit(1);
  }
  
  try {
    console.log(`🚀 Starting command: ${command}`);
    console.log('⏰ Timestamp:', new Date().toISOString());
    
    // Connect to database
    await sequelize.authenticate();
    
    // Run command
    await commands[command](...args);
    
    console.log('🎉 Command completed successfully');
    
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    logger.error('CLI command failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await sequelize.close();
  }
};

// Run CLI if this file is executed directly
if (require.main === module) {
  runCommand();
}

module.exports = { commands, parseColumnType };
