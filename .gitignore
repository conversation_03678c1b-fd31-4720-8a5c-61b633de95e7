# Dependencies
**/node_modules/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Environment variables
**/.env
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local

# Build outputs
**/dist/
**/build/
**/.next/
**/out/
**/.nuxt/

# Coverage directory
**/coverage/
**/.nyc_output/

# Logs
**/logs/
**/*.log

# Cache
**/.cache/
**/.parcel-cache/
**/.eslintcache/
**/.npm/
**/.rpt2_cache/
**/.rts2_cache_cjs/
**/.rts2_cache_es/
**/.rts2_cache_umd/

# Editor directories and files
**/.idea/
**/.vscode/
**/*.swp
**/*.swo
**/*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Uploads
**/uploads/*
!**/uploads/.gitkeep

# Database
**/*.sqlite
**/*.sqlite3
**/*.db

# Docker
**/.dockerignore
**/docker-compose.override.yml

# SSL certificates
**/ssl/
**/*.pem
**/*.key
**/*.crt