// Simple server start without complex logging
require('dotenv').config();

const express = require('express');
const app = express();
const port = 3000;

// Basic middleware
app.use(express.json());

// Test route
app.get('/test', (req, res) => {
  res.json({ 
    message: 'Server is running!', 
    timestamp: new Date().toISOString(),
    modules: ['auth', 'users', 'branches', 'services', 'employees', 'customers']
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', uptime: process.uptime() });
});

// Start server
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
  console.log(`Test: http://localhost:${port}/test`);
  console.log(`Health: http://localhost:${port}/health`);
});
