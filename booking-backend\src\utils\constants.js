/**
 * Application Constants
 * Centralized constants for the application
 */

// User roles
const USER_ROLES = {
  ADMIN: 'admin',
  STAFF: 'staff',
  CUSTOMER: 'customer'
};

// Booking statuses
const BOOKING_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  NO_SHOW: 'no_show'
};

// Payment statuses
const PAYMENT_STATUSES = {
  UNPAID: 'unpaid',
  DEPOSIT: 'deposit',
  PAID: 'paid'
};

// Payment methods
const PAYMENT_METHODS = {
  CASH: 'cash',
  CARD: 'card',
  BANK_TRANSFER: 'bank_transfer',
  MOMO: 'momo',
  STRIPE: 'stripe'
};

// Gender options
const GENDERS = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other'
};

// Notification types
const NOTIFICATION_TYPES = {
  BOOKING_CREATED: 'booking_created',
  BOOKING_CONFIRMED: 'booking_confirmed',
  BOOKING_CANCELLED: 'booking_cancelled',
  BOOKING_REMINDER: 'booking_reminder',
  PAYMENT_RECEIVED: 'payment_received',
  PROMOTION: 'promotion',
  SYSTEM: 'system'
};

// Notification channels
const NOTIFICATION_CHANNELS = {
  EMAIL: 'email',
  SMS: 'sms',
  PUSH: 'push',
  IN_APP: 'in_app'
};

// Service categories
const SERVICE_CATEGORIES = {
  FACIAL: 'facial',
  MASSAGE: 'massage',
  BODY_TREATMENT: 'body_treatment',
  NAIL_CARE: 'nail_care',
  HAIR_CARE: 'hair_care',
  SKINCARE: 'skincare',
  WELLNESS: 'wellness'
};



// Working days
const WORKING_DAYS = {
  MONDAY: 'monday',
  TUESDAY: 'tuesday',
  WEDNESDAY: 'wednesday',
  THURSDAY: 'thursday',
  FRIDAY: 'friday',
  SATURDAY: 'saturday',
  SUNDAY: 'sunday'
};



// Employee specializations
const EMPLOYEE_SPECIALIZATIONS = {
  SWEDISH_MASSAGE: 'swedish_massage',
  DEEP_TISSUE: 'deep_tissue',
  HOT_STONE: 'hot_stone',
  AROMATHERAPY: 'aromatherapy',
  THAI_MASSAGE: 'thai_massage',
  REFLEXOLOGY: 'reflexology',
  FACIAL_TREATMENT: 'facial_treatment',
  ANTI_AGING: 'anti_aging',
  ACNE_TREATMENT: 'acne_treatment',
  BODY_WRAP: 'body_wrap',
  EXFOLIATION: 'exfoliation',
  NAIL_ART: 'nail_art',
  MANICURE: 'manicure',
  PEDICURE: 'pedicure',
  HAIR_STYLING: 'hair_styling',
  HAIR_COLORING: 'hair_coloring',
  MEDITATION: 'meditation',
  YOGA: 'yoga',
  WELLNESS_COACHING: 'wellness_coaching'
};

// Membership levels
const MEMBERSHIP_LEVELS = {
  BRONZE: 'bronze',
  SILVER: 'silver',
  GOLD: 'gold',
  PLATINUM: 'platinum',
  DIAMOND: 'diamond'
};

// Time slots (in minutes)
const TIME_SLOTS = {
  SLOT_15: 15,
  SLOT_30: 30,
  SLOT_45: 45,
  SLOT_60: 60,
  SLOT_90: 90,
  SLOT_120: 120
};

// Business hours
const BUSINESS_HOURS = {
  OPEN_TIME: '08:00',
  CLOSE_TIME: '20:00',
  LUNCH_START: '12:00',
  LUNCH_END: '13:00'
};

// Pagination defaults
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100
};

// File upload limits
const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
};

// Error codes
const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR'
};

// HTTP status codes
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
};

// Cache keys
const CACHE_KEYS = {
  USER_SESSION: 'user_session:',
  BOOKING_AVAILABILITY: 'booking_availability:',
  SERVICE_LIST: 'service_list',
  EMPLOYEE_SCHEDULE: 'employee_schedule:',
  BRANCH_INFO: 'branch_info:'
};

// Cache TTL (in seconds)
const CACHE_TTL = {
  SHORT: 300,      // 5 minutes
  MEDIUM: 1800,    // 30 minutes
  LONG: 3600,      // 1 hour
  VERY_LONG: 86400 // 24 hours
};

// Email templates
const EMAIL_TEMPLATES = {
  WELCOME: 'welcome',
  BOOKING_CONFIRMATION: 'booking_confirmation',
  BOOKING_REMINDER: 'booking_reminder',
  BOOKING_CANCELLATION: 'booking_cancellation',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification'
};

// SMS templates
const SMS_TEMPLATES = {
  BOOKING_CONFIRMATION: 'booking_confirmation',
  BOOKING_REMINDER: 'booking_reminder',
  VERIFICATION_CODE: 'verification_code'
};

// Date formats
const DATE_FORMATS = {
  DATE_ONLY: 'YYYY-MM-DD',
  TIME_ONLY: 'HH:mm',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DISPLAY_DATE: 'DD/MM/YYYY',
  DISPLAY_DATETIME: 'DD/MM/YYYY HH:mm'
};

// Regex patterns
const REGEX_PATTERNS = {
  PHONE_VN: /^(0|\+84)[0-9]{9,10}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_STRONG: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  TIME_24H: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  DATE_ISO: /^\d{4}-\d{2}-\d{2}$/
};

// Default values
const DEFAULTS = {
  BOOKING_DURATION: 60, // minutes
  REMINDER_TIME: 24,    // hours before appointment
  SESSION_TIMEOUT: 24,  // hours
  PASSWORD_RESET_EXPIRY: 1, // hours
  EMAIL_VERIFICATION_EXPIRY: 24 // hours
};

// System settings keys
const SYSTEM_SETTINGS = {
  BOOKING_ADVANCE_DAYS: 'booking_advance_days',
  CANCELLATION_DEADLINE: 'cancellation_deadline',
  DEPOSIT_PERCENTAGE: 'deposit_percentage',
  AUTO_CONFIRM_BOOKINGS: 'auto_confirm_bookings',
  SEND_REMINDERS: 'send_reminders',
  REMINDER_HOURS: 'reminder_hours',
  WORKING_HOURS_START: 'working_hours_start',
  WORKING_HOURS_END: 'working_hours_end',
  LUNCH_BREAK_START: 'lunch_break_start',
  LUNCH_BREAK_END: 'lunch_break_end'
};

// Export all constants
module.exports = {
  USER_ROLES,
  BOOKING_STATUSES,
  PAYMENT_STATUSES,
  PAYMENT_METHODS,
  GENDERS,
  NOTIFICATION_TYPES,
  NOTIFICATION_CHANNELS,
  SERVICE_CATEGORIES,
  EMPLOYEE_SPECIALIZATIONS,
  MEMBERSHIP_LEVELS,
  WORKING_DAYS,
  TIME_SLOTS,
  BUSINESS_HOURS,
  PAGINATION,
  FILE_UPLOAD,
  ERROR_CODES,
  HTTP_STATUS,
  CACHE_KEYS,
  CACHE_TTL,
  EMAIL_TEMPLATES,
  SMS_TEMPLATES,
  DATE_FORMATS,
  REGEX_PATTERNS,
  DEFAULTS,
  SYSTEM_SETTINGS
};
