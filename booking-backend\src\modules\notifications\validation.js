/**
 * Notification Validation Schemas
 * Express-validator validation schemas for notification-related requests
 */

const { body, query, param } = require('express-validator');

/**
 * Validation rules for creating a new notification
 */
const createNotificationValidation = [
  body('recipientId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Recipient ID must be a positive integer'),

  body('type')
    .notEmpty()
    .withMessage('Type is required')
    .isIn(['booking', 'payment', 'system', 'promotion', 'reminder'])
    .withMessage('Type must be one of: booking, payment, system, promotion, reminder'),

  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),

  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message must be between 1 and 1000 characters'),

  body('data')
    .optional()
    .isObject()
    .withMessage('Data must be an object'),

  body('channel')
    .optional()
    .isIn(['in_app', 'email', 'sms', 'push'])
    .withMessage('Channel must be one of: in_app, email, sms, push'),

  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be one of: low, medium, high, urgent'),

  body('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must be less than 100 characters'),

  body('templateId')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Template ID must be less than 100 characters'),

  body('scheduledAt')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid ISO 8601 date'),

  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid ISO 8601 date')
];

/**
 * Validation rules for updating a notification
 */
const updateNotificationValidation = [
  body('title')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),

  body('message')
    .optional()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Message must be between 1 and 1000 characters'),

  body('data')
    .optional()
    .isObject()
    .withMessage('Data must be an object'),

  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be one of: low, medium, high, urgent'),

  body('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must be less than 100 characters'),

  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid ISO 8601 date')
];

/**
 * Validation rules for notification query parameters
 */
const notificationQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('recipientId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Recipient ID must be a positive integer'),

  query('type')
    .optional()
    .isIn(['booking', 'payment', 'system', 'promotion', 'reminder'])
    .withMessage('Type must be one of: booking, payment, system, promotion, reminder'),

  query('status')
    .optional()
    .isIn(['pending', 'sent', 'delivered', 'failed'])
    .withMessage('Status must be one of: pending, sent, delivered, failed'),

  query('channel')
    .optional()
    .isIn(['in_app', 'email', 'sms', 'push'])
    .withMessage('Channel must be one of: in_app, email, sms, push'),

  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be one of: low, medium, high, urgent'),

  query('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must be less than 100 characters'),

  query('isRead')
    .optional()
    .isBoolean()
    .withMessage('isRead must be a boolean'),

  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),

  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),

  query('search')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Search term must be less than 255 characters')
];

/**
 * Validation rules for notification ID parameter
 */
const notificationIdValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Notification ID must be a positive integer')
];

module.exports = {
  createNotificationValidation,
  updateNotificationValidation,
  notificationQueryValidation,
  notificationIdValidation
};
