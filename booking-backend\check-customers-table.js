const { sequelize } = require('./src/database/connection');

async function checkCustomersTable() {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected');
    
    // Check customers table structure
    const [results] = await sequelize.query("DESCRIBE customers");
    
    console.log('\n📋 Customers table columns:');
    results.forEach(column => {
      console.log(`  - ${column.Field} (${column.Type})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkCustomersTable();
