/**
 * Account Manager Script
 * Comprehensive tool for managing user account locks and login attempts
 */

const { sequelize } = require('../src/database/connection');
const User = require('../src/modules/auth/model');

class AccountManager {
  static async connect() {
    await sequelize.authenticate();
    console.log('✅ Database connection established');
  }

  static async disconnect() {
    await sequelize.close();
  }

  /**
   * Check all user accounts for lock status
   */
  static async checkAllAccounts() {
    const users = await User.findAll({
      attributes: [
        'id', 'email', 'name', 'role', 'isActive', 
        'loginAttempts', 'lockUntil', 'lastLogin'
      ]
    });

    console.log(`\n📊 Account Status Report (${users.length} users):\n`);
    
    let lockedCount = 0;
    let attemptsCount = 0;
    
    users.forEach((user, index) => {
      const isLocked = user.lockUntil && user.lockUntil > new Date();
      const hasAttempts = user.loginAttempts > 0;
      
      if (isLocked) lockedCount++;
      if (hasAttempts) attemptsCount++;
      
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   Role: ${user.role} | Active: ${user.isActive}`);
      console.log(`   Login Attempts: ${user.loginAttempts}/5`);
      
      if (isLocked) {
        const timeRemaining = Math.ceil((user.lockUntil - new Date()) / (1000 * 60));
        console.log(`   🔒 LOCKED - ${timeRemaining} minutes remaining`);
      } else if (hasAttempts) {
        console.log(`   ⚠️  ${user.loginAttempts} failed attempts`);
      } else {
        console.log(`   ✅ Account OK`);
      }
      console.log('');
    });

    return { total: users.length, locked: lockedCount, withAttempts: attemptsCount };
  }

  /**
   * Unlock specific user account
   */
  static async unlockAccount(email) {
    const user = await User.findOne({ where: { email } });
    
    if (!user) {
      throw new Error(`User not found: ${email}`);
    }

    const wasLocked = user.lockUntil && user.lockUntil > new Date();
    const hadAttempts = user.loginAttempts > 0;

    await user.update({
      loginAttempts: 0,
      lockUntil: null
    });

    console.log(`✅ Account unlocked: ${email}`);
    if (wasLocked) console.log('   - Removed account lock');
    if (hadAttempts) console.log(`   - Reset ${user.loginAttempts} failed attempts`);
    
    return { wasLocked, hadAttempts };
  }

  /**
   * Unlock all user accounts
   */
  static async unlockAllAccounts() {
    const users = await User.findAll({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { loginAttempts: { [sequelize.Sequelize.Op.gt]: 0 } },
          { lockUntil: { [sequelize.Sequelize.Op.not]: null } }
        ]
      }
    });

    if (users.length === 0) {
      console.log('ℹ️  No accounts need unlocking');
      return [];
    }

    const unlockPromises = users.map(async (user) => {
      await user.update({
        loginAttempts: 0,
        lockUntil: null
      });
      return user.email;
    });

    const unlockedEmails = await Promise.all(unlockPromises);
    
    console.log(`✅ Unlocked ${unlockedEmails.length} accounts:`);
    unlockedEmails.forEach(email => console.log(`   - ${email}`));
    
    return unlockedEmails;
  }

  /**
   * Get locked accounts only
   */
  static async getLockedAccounts() {
    const users = await User.findAll({
      where: {
        lockUntil: { [sequelize.Sequelize.Op.gt]: new Date() }
      },
      attributes: ['id', 'email', 'name', 'role', 'loginAttempts', 'lockUntil']
    });

    return users.map(user => ({
      email: user.email,
      name: user.name,
      role: user.role,
      attempts: user.loginAttempts,
      lockUntil: user.lockUntil,
      minutesRemaining: Math.ceil((user.lockUntil - new Date()) / (1000 * 60))
    }));
  }

  /**
   * Reset password for user (for testing)
   */
  static async resetPassword(email, newPassword) {
    const user = await User.findOne({ where: { email } });
    
    if (!user) {
      throw new Error(`User not found: ${email}`);
    }

    await user.update({ password: newPassword });
    console.log(`✅ Password reset for: ${email}`);
    
    return true;
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    await AccountManager.connect();

    switch (command) {
      case 'check':
        const status = await AccountManager.checkAllAccounts();
        console.log('📋 SUMMARY:');
        console.log(`   Total: ${status.total} | Locked: ${status.locked} | With Attempts: ${status.withAttempts}`);
        break;

      case 'unlock':
        const email = args[1];
        if (email) {
          await AccountManager.unlockAccount(email);
        } else {
          await AccountManager.unlockAllAccounts();
        }
        break;

      case 'locked':
        const lockedAccounts = await AccountManager.getLockedAccounts();
        if (lockedAccounts.length === 0) {
          console.log('✅ No accounts are currently locked');
        } else {
          console.log('🔒 Currently locked accounts:');
          lockedAccounts.forEach(account => {
            console.log(`   - ${account.email} (${account.role}) - ${account.minutesRemaining} min remaining`);
          });
        }
        break;

      case 'reset-password':
        const resetEmail = args[1];
        const newPassword = args[2] || 'password123';
        if (resetEmail) {
          await AccountManager.resetPassword(resetEmail, newPassword);
        } else {
          console.log('❌ Email required for password reset');
        }
        break;

      default:
        console.log('🔧 Account Manager - Usage:');
        console.log('  node account-manager.js check                    - Check all accounts');
        console.log('  node account-manager.js unlock                   - Unlock all accounts');
        console.log('  node account-manager.js unlock <email>           - Unlock specific account');
        console.log('  node account-manager.js locked                   - Show locked accounts only');
        console.log('  node account-manager.js reset-password <email>   - Reset password');
        console.log('');
        console.log('📋 Quick Commands:');
        console.log('  npm run unlock-accounts     - Unlock all accounts');
        console.log('  npm run check-accounts      - Check account status');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await AccountManager.disconnect();
    process.exit(0);
  }
}

// Export for programmatic use
module.exports = AccountManager;

// Run CLI if called directly
if (require.main === module) {
  main();
}
