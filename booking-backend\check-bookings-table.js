const { sequelize } = require('./src/database/connection');

async function checkBookingsTable() {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected');
    
    // Check bookings table structure
    const [results] = await sequelize.query("DESCRIBE bookings");
    
    console.log('\n📋 Bookings table columns:');
    results.forEach(column => {
      console.log(`  - ${column.Field} (${column.Type})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkBookingsTable();
