/**
 * Role-Based Access Control (RBAC) Middleware
 * Handles authorization based on user roles and permissions
 */

const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Role hierarchy definition
 * Higher number = more permissions
 */
const ROLE_HIERARCHY = {
  customer: 1,
  employee: 2,
  manager: 3,
  admin: 4,
  super_admin: 5
};

/**
 * Permission definitions for each role
 */
const ROLE_PERMISSIONS = {
  customer: [
    'booking:create',
    'booking:read:own',
    'booking:update:own',
    'booking:cancel:own',
    'payment:read:own',
    'notification:read:own',
    'notification:mark_read:own',
    'profile:read:own',
    'profile:update:own'
  ],
  employee: [
    'booking:read',
    'booking:update',
    'booking:confirm',
    'booking:complete',
    'customer:read',
    'service:read',
    'branch:read',
    'notification:read:own',
    'notification:mark_read:own',
    'schedule:read:own',
    'schedule:update:own'
  ],
  manager: [
    'booking:read',
    'booking:create',
    'booking:update',
    'booking:delete',
    'booking:confirm',
    'booking:complete',
    'booking:cancel',
    'customer:read',
    'customer:create',
    'customer:update',
    'employee:read',
    'employee:create',
    'employee:update',
    'service:read',
    'service:create',
    'service:update',
    'branch:read',
    'branch:update',
    'payment:read',
    'payment:process',
    'payment:refund',
    'notification:read',
    'notification:create',
    'notification:send',
    'stats:read:branch',
    'reports:read:branch'
  ],
  admin: [
    'booking:*',
    'customer:*',
    'employee:*',
    'service:*',
    'branch:*',
    'payment:*',
    'notification:*',
    'user:*',
    'stats:*',
    'reports:*',
    'settings:*'
  ],
  super_admin: [
    '*:*'
  ]
};

/**
 * Check if user has required role level
 */
function hasRoleLevel(userRole, requiredRoles) {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  
  if (Array.isArray(requiredRoles)) {
    return requiredRoles.some(role => {
      const requiredLevel = ROLE_HIERARCHY[role] || 0;
      return userLevel >= requiredLevel;
    });
  }
  
  const requiredLevel = ROLE_HIERARCHY[requiredRoles] || 0;
  return userLevel >= requiredLevel;
}

/**
 * Check if user has specific permission
 */
function hasPermission(userRole, permission) {
  const userPermissions = ROLE_PERMISSIONS[userRole] || [];
  
  // Super admin has all permissions
  if (userPermissions.includes('*:*')) {
    return true;
  }
  
  // Check exact permission match
  if (userPermissions.includes(permission)) {
    return true;
  }
  
  // Check wildcard permissions
  const [resource, action] = permission.split(':');
  const wildcardResource = `${resource}:*`;
  const wildcardAction = `*:${action}`;
  
  return userPermissions.includes(wildcardResource) || 
         userPermissions.includes(wildcardAction);
}

/**
 * Authorization middleware factory
 * @param {string|string[]} roles - Required roles
 * @param {string} permission - Optional specific permission
 * @returns {Function} Express middleware
 */
function authorize(roles, permission = null) {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        throw new AppError('Authentication required', 401, 'AUTHENTICATION_REQUIRED');
      }

      const userRole = req.user.role;
      const userId = req.user.id;

      // Log authorization attempt
      logger.debug('Authorization check', {
        userId,
        userRole,
        requiredRoles: roles,
        permission,
        endpoint: req.originalUrl,
        method: req.method
      });

      // Check role-based access
      if (roles && !hasRoleLevel(userRole, roles)) {
        logger.warn('Access denied - insufficient role', {
          userId,
          userRole,
          requiredRoles: roles,
          endpoint: req.originalUrl
        });
        throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
      }

      // Check permission-based access
      if (permission && !hasPermission(userRole, permission)) {
        logger.warn('Access denied - missing permission', {
          userId,
          userRole,
          requiredPermission: permission,
          endpoint: req.originalUrl
        });
        throw new AppError('Permission denied', 403, 'PERMISSION_DENIED');
      }

      // Check resource ownership for 'own' permissions
      if (permission && permission.includes(':own')) {
        const resourceId = req.params.id || req.params.userId || req.body.userId;
        if (resourceId && parseInt(resourceId) !== userId && userRole === 'customer') {
          logger.warn('Access denied - resource ownership', {
            userId,
            resourceId,
            endpoint: req.originalUrl
          });
          throw new AppError('Access denied to this resource', 403, 'RESOURCE_ACCESS_DENIED');
        }
      }

      logger.debug('Authorization successful', {
        userId,
        userRole,
        endpoint: req.originalUrl
      });

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Check if user can access specific resource
 */
function canAccessResource(req, res, next) {
  try {
    const { user } = req;
    const resourceId = req.params.id;
    const resourceType = req.baseUrl.split('/').pop(); // Extract resource type from URL

    // Admin and manager can access all resources
    if (['admin', 'super_admin', 'manager'].includes(user.role)) {
      return next();
    }

    // For customers, check if they're accessing their own resources
    if (user.role === 'customer') {
      // Check based on resource type
      switch (resourceType) {
        case 'bookings':
          // Customer can only access their own bookings
          // This would need to be validated against the database
          break;
        case 'payments':
          // Customer can only access their own payments
          break;
        case 'notifications':
          // Customer can only access their own notifications
          break;
        default:
          break;
      }
    }

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Middleware to check if user owns the resource
 */
function requireOwnership(resourceField = 'userId') {
  return async (req, res, next) => {
    try {
      const { user } = req;
      const resourceId = req.params.id;

      // Admin can access everything
      if (['admin', 'super_admin'].includes(user.role)) {
        return next();
      }

      // For other roles, check ownership
      // This would typically involve a database query
      // For now, we'll just check if the user ID matches
      if (req.body[resourceField] && req.body[resourceField] !== user.id) {
        throw new AppError('You can only access your own resources', 403, 'OWNERSHIP_REQUIRED');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Get user permissions
 */
function getUserPermissions(role) {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check if role exists
 */
function isValidRole(role) {
  return Object.keys(ROLE_HIERARCHY).includes(role);
}

module.exports = {
  authorize,
  canAccessResource,
  requireOwnership,
  hasRoleLevel,
  hasPermission,
  getUserPermissions,
  isValidRole,
  ROLE_HIERARCHY,
  ROLE_PERMISSIONS
};
