/**
 * Debug Form Creation Payload
 * Analyzes the exact data flow from frontend to backend
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function debugFormCreationPayload() {
  console.log('🔍 Debugging Form Creation Data Flow...\n');

  try {
    // Step 1: Login and get token
    console.log('1. Authenticating user...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log('✅ Login successful');
    console.log('User ID:', user.id);
    console.log('User Email:', user.email);

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Check user's current services and branches
    console.log('\n2. Checking user prerequisites...');
    
    try {
      const branchesResponse = await axios.get(`${API_BASE}/branches`, { headers });
      const branches = branchesResponse.data.data?.data || [];
      console.log(`✅ Branches found: ${branches.length}`);
      
      if (branches.length > 0) {
        branches.forEach((branch, index) => {
          console.log(`   Branch ${index + 1}: ID=${branch.id}, Name="${branch.name}", Manager=${branch.manager_id || branch.managerId}`);
        });
      }

      const servicesResponse = await axios.get(`${API_BASE}/services`, { headers });
      const services = servicesResponse.data.data?.data || [];
      console.log(`✅ Services found: ${services.length}`);
      
      if (services.length > 0) {
        services.forEach((service, index) => {
          console.log(`   Service ${index + 1}: ID=${service.id}, Name="${service.name}", BranchID=${service.branch_id || service.branchId}`);
        });
      }

      // Step 3: Analyze data compatibility
      console.log('\n3. Analyzing data compatibility...');
      
      if (branches.length === 0) {
        console.log('❌ No branches found - user needs to create a branch first');
        return;
      }
      
      if (services.length === 0) {
        console.log('❌ No services found - user needs to create a service first');
        return;
      }

      // Find compatible service-branch pairs
      const compatiblePairs = [];
      for (const service of services) {
        for (const branch of branches) {
          // Check if service belongs to this branch or if service is global
          const serviceBranchId = service.branch_id || service.branchId;
          const branchManagerId = branch.manager_id || branch.managerId;
          
          if (!serviceBranchId || serviceBranchId === branch.id) {
            if (branchManagerId === user.id) {
              compatiblePairs.push({ service, branch });
            }
          }
        }
      }

      console.log(`Compatible service-branch pairs: ${compatiblePairs.length}`);
      
      if (compatiblePairs.length === 0) {
        console.log('❌ No compatible service-branch pairs found');
        console.log('   This means either:');
        console.log('   - Services are not linked to user\'s branches');
        console.log('   - User is not the manager of the branches');
        console.log('   - Service branch_id doesn\'t match any user branches');
        return;
      }

      // Step 4: Test form creation with valid data
      console.log('\n4. Testing form creation with valid data...');
      
      const testPair = compatiblePairs[0];
      console.log(`Using Service: ${testPair.service.name} (ID: ${testPair.service.id})`);
      console.log(`Using Branch: ${testPair.branch.name} (ID: ${testPair.branch.id})`);

      // Simulate exact frontend payload
      const frontendFormData = {
        name: 'Debug Test Form',
        serviceId: testPair.service.id.toString(), // Frontend sends as string
        branchId: testPair.branch.id.toString(),   // Frontend sends as string
        fields: {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        }
      };

      // Transform to backend format (like formsService.js does)
      const requestData = {
        name: frontendFormData.name,
        serviceId: parseInt(frontendFormData.serviceId),
        branchId: parseInt(frontendFormData.branchId),
        status: frontendFormData.status || 'active',
        fieldsConfig: frontendFormData.fields || {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        brandingConfig: {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        }
      };

      console.log('\n=== Request Payload Analysis ===');
      console.log('Frontend form data:', JSON.stringify(frontendFormData, null, 2));
      console.log('Transformed request data:', JSON.stringify(requestData, null, 2));

      // Make the actual request
      try {
        const formResponse = await axios.post(`${API_BASE}/forms`, requestData, { headers });
        console.log('\n✅ Form creation successful!');
        console.log('Response status:', formResponse.status);
        console.log('Form created:', formResponse.data.data);
        
      } catch (formError) {
        console.log('\n❌ Form creation failed:');
        console.log('Status:', formError.response?.status);
        console.log('Error code:', formError.response?.data?.error?.code);
        console.log('Error message:', formError.response?.data?.error?.message);
        console.log('Full error data:', JSON.stringify(formError.response?.data, null, 2));
        
        // Analyze the specific error
        if (formError.response?.status === 404) {
          console.log('\n🔍 404 Error Analysis:');
          console.log('This suggests the backend validation failed to find:');
          console.log('- Service with ID', requestData.serviceId, 'belonging to user\'s branch');
          console.log('- Branch with ID', requestData.branchId, 'where user is manager');
        }
      }

    } catch (error) {
      console.log('❌ Error checking prerequisites:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the debug
debugFormCreationPayload();
