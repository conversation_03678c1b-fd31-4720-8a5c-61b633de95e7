const express = require('express');
const router = express.Router();
const FormsController = require('../modules/forms/controller');
const BookingsController = require('../modules/bookings/controller');

/**
 * Public Forms Routes
 * These routes don't require authentication and are used by customers
 */

/**
 * @swagger
 * /api/public/forms/{slug}:
 *   get:
 *     summary: Get form by slug for public booking
 *     tags: [Public Forms]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Form slug identifier
 *     responses:
 *       200:
 *         description: Form retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     businessName:
 *                       type: string
 *                     service:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                         price:
 *                           type: number
 *                         duration:
 *                           type: integer
 *                         description:
 *                           type: string
 *                     branch:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                         address:
 *                           type: string
 *                         phone:
 *                           type: string
 *                         city:
 *                           type: string
 *                     fields:
 *                       type: object
 *                       properties:
 *                         customerName:
 *                           type: boolean
 *                         phoneNumber:
 *                           type: boolean
 *                         emailAddress:
 *                           type: boolean
 *                         preferredDate:
 *                           type: boolean
 *                         preferredTime:
 *                           type: boolean
 *                         specialRequests:
 *                           type: boolean
 *                     branding:
 *                       type: object
 *                       properties:
 *                         primaryColor:
 *                           type: string
 *                         logo:
 *                           type: string
 *                         customMessage:
 *                           type: string
 *       404:
 *         description: Form not found or inactive
 *       500:
 *         description: Internal server error
 */
router.get('/forms/:slug', FormsController.getFormBySlug);

/**
 * @swagger
 * /api/public/bookings:
 *   post:
 *     summary: Create booking from public form
 *     tags: [Public Bookings]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - formSlug
 *               - customerName
 *               - phoneNumber
 *               - emailAddress
 *               - preferredDate
 *               - preferredTime
 *             properties:
 *               formSlug:
 *                 type: string
 *                 description: Form slug identifier
 *               customerName:
 *                 type: string
 *                 description: Customer full name
 *               phoneNumber:
 *                 type: string
 *                 description: Customer phone number
 *               emailAddress:
 *                 type: string
 *                 description: Customer email address
 *               preferredDate:
 *                 type: string
 *                 format: date
 *                 description: Preferred booking date
 *               preferredTime:
 *                 type: string
 *                 description: Preferred booking time
 *               specialRequests:
 *                 type: string
 *                 description: Special requests or notes
 *     responses:
 *       201:
 *         description: Booking created successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Form not found
 *       500:
 *         description: Internal server error
 */
router.post('/bookings', BookingsController.createPublicBooking);

module.exports = router;
