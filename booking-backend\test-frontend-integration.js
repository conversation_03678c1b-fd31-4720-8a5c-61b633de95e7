const axios = require('axios');

async function testFrontendIntegration() {
  console.log('🔗 Testing Frontend-Backend Integration...\n');
  
  try {
    // Test the exact same URL that frontend uses
    const frontendApiUrl = 'http://localhost:3001/api';
    
    console.log('🌐 Frontend API URL:', frontendApiUrl);
    
    // 1. Test health check
    console.log('\n1. Testing health check...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ Health check:', healthResponse.data);
    
    // 2. Test public form lookup (what happens when user visits /book/[slug])
    console.log('\n2. Testing public form lookup...');
    const formResponse = await axios.get(`${frontendApiUrl}/public/forms/test-public-form`);
    console.log('✅ Form found:', {
      title: formResponse.data.data.title,
      slug: formResponse.data.data.slug,
      isPublic: formResponse.data.data.isPublic
    });
    
    // 3. Test exact booking submission that frontend sends
    console.log('\n3. Testing public booking submission (exact frontend format)...');
    
    // This should match exactly what frontend sends
    const bookingData = {
      formSlug: 'test-public-form',
      customerName: 'Test User From Frontend',
      phoneNumber: '**********',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-20',
      preferredTime: '15:30',
      specialRequests: 'This is from frontend integration test'
    };
    
    console.log('📤 Sending booking data:', JSON.stringify(bookingData, null, 2));
    
    const bookingResponse = await axios.post(`${frontendApiUrl}/public/bookings`, bookingData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('✅ Booking response:', JSON.stringify(bookingResponse.data, null, 2));
    
    // 4. Verify booking was saved
    console.log('\n4. Verifying booking was saved...');
    const savedBookingsResponse = await axios.get(`${frontendApiUrl}/test/bookings`);
    console.log('✅ Total bookings saved:', savedBookingsResponse.data.count);
    
    console.log('\n🎉 SUCCESS! Frontend-Backend integration is working perfectly!');
    console.log('📍 The error must be in frontend configuration or browser cache.');
    console.log('💡 Next steps:');
    console.log('   1. Restart the frontend server to reload new environment variables');
    console.log('   2. Clear browser cache');
    console.log('   3. Check browser console for detailed error messages');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.response?.status || 'No status');
    console.error('📄 Error details:', JSON.stringify(error.response?.data || error.message, null, 2));
    console.error('📍 Failed URL:', error.config?.url);
    
    if (error.response?.status === 400) {
      console.log('\n🔍 Status 400 suggests validation error or wrong data format');
      console.log('📝 Check if the request data matches backend expectations');
    }
  }
}

testFrontendIntegration();
