/**
 * Employees Routes
 * API routes for employee management
 */

const express = require('express');
const router = express.Router();
const EmployeesController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validatePagination, validateIdParam } = require('../../middleware/validation');
const { adminLimiter, generalLimiter } = require('../../middleware/rateLimiter');
const { body, query, param } = require('express-validator');

// Validation rules
const createEmployeeValidation = [
  body('userId')
    .isInt({ min: 1 })
    .withMessage('Valid user ID required'),
  body('employeeCode')
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('Employee code must be 3-20 alphanumeric characters'),
  body('position')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Position must be between 2 and 50 characters'),
  body('department')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Department must be between 2 and 50 characters'),
  body('specializations')
    .optional()
    .isArray()
    .withMessage('Specializations must be an array'),
  body('qualifications')
    .optional()
    .isArray()
    .withMessage('Qualifications must be an array'),
  body('experience')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Experience must not exceed 2000 characters'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('languages')
    .optional()
    .isArray()
    .withMessage('Languages must be an array'),
  body('workingHours')
    .optional()
    .isObject()
    .withMessage('Working hours must be an object'),
  body('hourlyRate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Hourly rate must be a positive number'),
  body('commissionRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0 and 100'),
  body('startDate')
    .isISO8601()
    .withMessage('Valid start date required'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Valid end date required'),
  body('contractType')
    .isIn(['full_time', 'part_time', 'contract', 'intern'])
    .withMessage('Invalid contract type'),
  body('branchId')
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('managerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid manager ID required'),
  body('emergencyContact')
    .optional()
    .isObject()
    .withMessage('Emergency contact must be an object'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters')
];

const updateEmployeeValidation = [
  body('employeeCode')
    .optional()
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('Employee code must be 3-20 alphanumeric characters'),
  body('position')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Position must be between 2 and 50 characters'),
  body('department')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Department must be between 2 and 50 characters'),
  body('specializations')
    .optional()
    .isArray()
    .withMessage('Specializations must be an array'),
  body('qualifications')
    .optional()
    .isArray()
    .withMessage('Qualifications must be an array'),
  body('experience')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Experience must not exceed 2000 characters'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('languages')
    .optional()
    .isArray()
    .withMessage('Languages must be an array'),
  body('workingHours')
    .optional()
    .isObject()
    .withMessage('Working hours must be an object'),
  body('hourlyRate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Hourly rate must be a positive number'),
  body('commissionRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0 and 100'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Valid start date required'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Valid end date required'),
  body('contractType')
    .optional()
    .isIn(['full_time', 'part_time', 'contract', 'intern'])
    .withMessage('Invalid contract type'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'on_leave', 'terminated'])
    .withMessage('Invalid employee status'),
  body('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('managerId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid manager ID required'),
  body('emergencyContact')
    .optional()
    .isObject()
    .withMessage('Emergency contact must be an object'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('isAvailable')
    .optional()
    .isBoolean()
    .withMessage('isAvailable must be a boolean')
];

const updateStatusValidation = [
  body('status')
    .isIn(['active', 'inactive', 'on_leave', 'terminated'])
    .withMessage('Invalid employee status')
];

const toggleAvailabilityValidation = [
  body('isAvailable')
    .isBoolean()
    .withMessage('isAvailable is required and must be a boolean')
];

const searchValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

const getEmployeesValidation = [
  query('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'on_leave', 'terminated'])
    .withMessage('Invalid employee status'),
  query('position')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Position must be between 2 and 50 characters'),
  query('specialization')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Specialization must be between 2 and 50 characters'),
  query('isAvailable')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isAvailable must be true or false'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/employees
 * @desc    Get all employees with pagination and filters
 * @access  Private (Admin, Staff)
 */
router.get('/', 
  authorize(['admin', 'staff']),
  getEmployeesValidation,
  validatePagination,
  validateRequest,
  EmployeesController.getEmployees
);

/**
 * @route   GET /api/employees/stats
 * @desc    Get employee statistics
 * @access  Private (Admin)
 */
router.get('/stats', 
  authorize(['admin']),
  EmployeesController.getEmployeeStats
);

/**
 * @route   GET /api/employees/available
 * @desc    Get available employees
 * @access  Private (Admin, Staff)
 */
router.get('/available', 
  authorize(['admin', 'staff']),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  EmployeesController.getAvailableEmployees
);

/**
 * @route   GET /api/employees/top-rated
 * @desc    Get top rated employees
 * @access  Private (Admin, Staff)
 */
router.get('/top-rated', 
  authorize(['admin', 'staff']),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
  EmployeesController.getTopRatedEmployees
);

/**
 * @route   GET /api/employees/search
 * @desc    Search employees
 * @access  Private (Admin, Staff)
 */
router.get('/search', 
  authorize(['admin', 'staff']),
  searchValidation,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
  EmployeesController.searchEmployees
);

/**
 * @route   GET /api/employees/branch/:branchId
 * @desc    Get employees by branch
 * @access  Private (Admin, Staff)
 */
router.get('/branch/:branchId', 
  authorize(['admin', 'staff']),
  param('branchId')
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Limit must be between 1 and 200'),
  validateRequest,
  EmployeesController.getEmployeesByBranch
);

/**
 * @route   GET /api/employees/specialization/:specialization
 * @desc    Get employees by specialization
 * @access  Private (Admin, Staff)
 */
router.get('/specialization/:specialization', 
  authorize(['admin', 'staff']),
  param('specialization')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Specialization must be between 2 and 50 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
  EmployeesController.getEmployeesBySpecialization
);

/**
 * @route   GET /api/employees/:id
 * @desc    Get employee by ID
 * @access  Private (Admin, Staff)
 */
router.get('/:id', 
  authorize(['admin', 'staff']),
  validateIdParam(),
  validateRequest,
  EmployeesController.getEmployeeById
);

/**
 * @route   POST /api/employees
 * @desc    Create new employee
 * @access  Private (Admin)
 */
router.post('/', 
  authorize(['admin']),
  adminLimiter,
  createEmployeeValidation,
  validateRequest,
  EmployeesController.createEmployee
);

/**
 * @route   PUT /api/employees/:id
 * @desc    Update employee
 * @access  Private (Admin)
 */
router.put('/:id', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  updateEmployeeValidation,
  validateRequest,
  EmployeesController.updateEmployee
);

/**
 * @route   DELETE /api/employees/:id
 * @desc    Delete employee (soft delete)
 * @access  Private (Admin)
 */
router.delete('/:id', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  EmployeesController.deleteEmployee
);

/**
 * @route   PATCH /api/employees/:id/status
 * @desc    Update employee status
 * @access  Private (Admin)
 */
router.patch('/:id/status', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  updateStatusValidation,
  validateRequest,
  EmployeesController.updateEmployeeStatus
);

/**
 * @route   PATCH /api/employees/:id/availability
 * @desc    Toggle employee availability
 * @access  Private (Admin, Staff)
 */
router.patch('/:id/availability', 
  authorize(['admin', 'staff']),
  adminLimiter,
  validateIdParam(),
  toggleAvailabilityValidation,
  validateRequest,
  EmployeesController.toggleEmployeeAvailability
);

module.exports = router;
