# Kế Hoạch Phát Triển Hệ Thống Booking Spa Backend

## Tổng Quan Dự Án

### Mục Tiêu
Xây dựng hệ thống backend cho ứng dụng đặt lịch spa/clinic với các tính năng quản lý booking, kh<PERSON><PERSON> hàng, <PERSON><PERSON><PERSON> viê<PERSON>, dịch vụ và thanh toán.

### Công <PERSON>ệ Sử Dụng
- **Backend Framework**: Express.js 4.x
- **Runtime**: Node.js 20.x LTS
- **Language**: JavaScript (ES6+)
- **Database**: MySQL 8.0
- **ORM**: Sequelize 6.x
- **Cache**: Redis 7.x (tùy chọn)
- **API Style**: RESTful API
- **Documentation**: Swagger/OpenAPI 3.0

### Kiến Trúc Hệ Thống
- **Pattern**: MVC (Model-View-Controller)
- **Structure**: Module-based organization
- **Database**: Relational database với foreign keys
- **Authentication**: JWT-based authentication
- **Authorization**: Role-based access control

## Cấu Trú<PERSON>

```
booking-backend/
├── src/
│   ├── app.js                          # Entry point của ứng dụng
│   ├── server.js                       # Server configuration
│   ├── config/                         # Cấu hình ứng dụng
│   │   ├── database.js                 # Cấu hình database
│   │   ├── auth.js                     # Cấu hình JWT
│   │   ├── redis.js                    # Cấu hình Redis
│   │   ├── swagger.js                  # Cấu hình Swagger
│   │   └── environment.js              # Environment variables
│   ├── middleware/                     # Middleware functions
│   │   ├── auth.js                     # Authentication middleware
│   │   ├── validation.js               # Request validation
│   │   ├── errorHandler.js             # Error handling
│   │   ├── rateLimiter.js              # Rate limiting
│   │   └── logger.js                   # Request logging
│   ├── utils/                          # Utility functions
│   │   ├── response.js                 # Standardized API responses
│   │   ├── pagination.js               # Pagination helpers
│   │   ├── dateTime.js                 # Date/time utilities
│   │   ├── validation.js               # Validation helpers
│   │   └── constants.js                # Application constants
│   ├── database/                       # Database related files
│   │   ├── connection.js               # Database connection
│   │   ├── migrations/                 # Database migrations
│   │   ├── seeders/                    # Database seeders
│   │   └── sync.js                     # Database synchronization
│   └── modules/                        # Feature modules
│       ├── auth/                       # Authentication module
│       │   ├── controller.js
│       │   ├── service.js
│       │   ├── model.js
│       │   ├── route.js
│       │   └── swagger.js
│       ├── branches/                   # Chi nhánh module
│       │   ├── controller.js
│       │   ├── service.js
│       │   ├── model.js
│       │   ├── route.js
│       │   └── swagger.js
│       ├── users/                      # Người dùng module
│       ├── services/                   # Dịch vụ spa module
│       ├── employees/                  # Nhân viên module
│       ├── customers/                  # Khách hàng module
│       ├── bookings/                   # Đặt lịch module
│       ├── payments/                   # Thanh toán module
│       ├── notifications/              # Thông báo module
│       ├── settings/                   # Cài đặt module
│       └── reports/                    # Báo cáo module
├── tests/                              # Test files
│   ├── unit/                           # Unit tests
│   ├── integration/                    # Integration tests
│   └── fixtures/                       # Test data
├── docs/                               # Documentation
├── logs/                               # Log files
├── uploads/                            # File uploads
├── .env.example                        # Environment template
├── .env.development                    # Development environment
├── .env.production                     # Production environment
├── .gitignore
├── package.json
├── README.md
└── docker-compose.yml                  # Docker configuration
```

## Cấu Trúc Module Chi Tiết

### Mỗi Module Bao Gồm:

#### 1. **controller.js** - Xử lý HTTP Requests
```javascript
// Ví dụ: modules/bookings/controller.js
const BookingService = require('./service');
const { successResponse, errorResponse } = require('../../utils/response');
const { validateBooking } = require('../../utils/validation');

class BookingController {
  async createBooking(req, res, next) {
    try {
      const validatedData = validateBooking(req.body);
      const booking = await BookingService.createBooking(validatedData);
      return successResponse(res, booking, 'Booking created successfully', 201);
    } catch (error) {
      return errorResponse(res, error.message, 400);
    }
  }

  async getBookings(req, res, next) {
    try {
      const { page, limit, ...filters } = req.query;
      const bookings = await BookingService.getBookings(filters, { page, limit });
      return successResponse(res, bookings, 'Bookings retrieved successfully');
    } catch (error) {
      return errorResponse(res, error.message, 400);
    }
  }
}

module.exports = new BookingController();
```

#### 2. **service.js** - Business Logic
```javascript
// Ví dụ: modules/bookings/service.js
const Booking = require('./model');
const { Op } = require('sequelize');
const { paginate } = require('../../utils/pagination');

class BookingService {
  async createBooking(bookingData) {
    // Kiểm tra availability
    await this.checkAvailability(bookingData);

    // Tạo booking code
    bookingData.bookingCode = await this.generateBookingCode();

    // Tạo booking
    const booking = await Booking.create(bookingData);

    // Gửi notification (nếu cần)
    // await NotificationService.sendBookingConfirmation(booking);

    return booking;
  }

  async getBookings(filters, pagination) {
    const whereClause = this.buildWhereClause(filters);
    return await paginate(Booking, whereClause, pagination);
  }

  async checkAvailability(bookingData) {
    const { employeeId, bookingDate, startTime, endTime } = bookingData;

    const conflictingBooking = await Booking.findOne({
      where: {
        employeeId,
        bookingDate,
        [Op.or]: [
          {
            startTime: { [Op.between]: [startTime, endTime] }
          },
          {
            endTime: { [Op.between]: [startTime, endTime] }
          }
        ],
        status: { [Op.notIn]: ['cancelled'] }
      }
    });

    if (conflictingBooking) {
      throw new Error('Time slot is not available');
    }
  }

  async generateBookingCode() {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `BK${date}${random}`;
  }

  buildWhereClause(filters) {
    const where = {};

    if (filters.status) where.status = filters.status;
    if (filters.branchId) where.branchId = filters.branchId;
    if (filters.customerId) where.customerId = filters.customerId;
    if (filters.employeeId) where.employeeId = filters.employeeId;
    if (filters.dateFrom && filters.dateTo) {
      where.bookingDate = {
        [Op.between]: [filters.dateFrom, filters.dateTo]
      };
    }

    return where;
  }
}

module.exports = new BookingService();
```

#### 3. **model.js** - Sequelize Model Definitions
```javascript
// Ví dụ: modules/bookings/model.js
const { DataTypes } = require('sequelize');
const sequelize = require('../../database/connection');

const Booking = sequelize.define('Booking', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  bookingCode: {
    type: DataTypes.STRING(20),
    unique: true,
    allowNull: false,
    comment: 'Mã booking: BK20250610001'
  },
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  serviceId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  employeeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'employees',
      key: 'id'
    }
  },
  branchId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'branches',
      key: 'id'
    }
  },
  bookingDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: 'Ngày hẹn'
  },
  startTime: {
    type: DataTypes.TIME,
    allowNull: false,
    comment: 'Giờ bắt đầu'
  },
  endTime: {
    type: DataTypes.TIME,
    allowNull: false,
    comment: 'Giờ kết thúc'
  },
  status: {
    type: DataTypes.ENUM('pending', 'confirmed', 'cancelled', 'completed', 'no_show'),
    defaultValue: 'pending',
    allowNull: false
  },
  customerNote: {
    type: DataTypes.TEXT,
    comment: 'Ghi chú từ khách'
  },
  staffNote: {
    type: DataTypes.TEXT,
    comment: 'Ghi chú từ nhân viên'
  },
  paymentStatus: {
    type: DataTypes.ENUM('unpaid', 'deposit', 'paid'),
    defaultValue: 'unpaid',
    allowNull: false
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  depositAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  cancelledBy: {
    type: DataTypes.INTEGER,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User ID người hủy'
  },
  cancelledAt: {
    type: DataTypes.DATE
  },
  cancelledReason: {
    type: DataTypes.TEXT
  },
  completedAt: {
    type: DataTypes.DATE
  },
  noShowAt: {
    type: DataTypes.DATE
  },
  createdBy: {
    type: DataTypes.INTEGER,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User ID người tạo'
  }
}, {
  tableName: 'bookings',
  timestamps: true,
  underscored: true,
  indexes: [
    { fields: ['booking_date'] },
    { fields: ['status'] },
    { fields: ['employee_id', 'booking_date'] },
    { fields: ['customer_id'] },
    { fields: ['branch_id', 'booking_date'] },
    { fields: ['branch_id', 'booking_date', 'status'] }
  ]
});

// Associations
Booking.associate = (models) => {
  Booking.belongsTo(models.Customer, { foreignKey: 'customerId', as: 'customer' });
  Booking.belongsTo(models.Service, { foreignKey: 'serviceId', as: 'service' });
  Booking.belongsTo(models.Employee, { foreignKey: 'employeeId', as: 'employee' });
  Booking.belongsTo(models.Branch, { foreignKey: 'branchId', as: 'branch' });
  Booking.belongsTo(models.User, { foreignKey: 'cancelledBy', as: 'cancelledByUser' });
  Booking.belongsTo(models.User, { foreignKey: 'createdBy', as: 'createdByUser' });
  Booking.hasMany(models.Payment, { foreignKey: 'bookingId', as: 'payments' });
};

module.exports = Booking;
```

#### 4. **route.js** - API Routes Definition
```javascript
// Ví dụ: modules/bookings/route.js
const express = require('express');
const router = express.Router();
const BookingController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest } = require('../../middleware/validation');
const { bookingValidationRules } = require('./validation');

// Public routes (nếu có)

// Protected routes
router.use(authenticate); // Require authentication for all routes below

// GET /api/bookings - Lấy danh sách bookings
router.get('/',
  authorize(['admin', 'staff', 'customer']),
  BookingController.getBookings
);

// GET /api/bookings/:id - Lấy chi tiết booking
router.get('/:id',
  authorize(['admin', 'staff', 'customer']),
  BookingController.getBookingById
);

// POST /api/bookings - Tạo booking mới
router.post('/',
  authorize(['admin', 'staff', 'customer']),
  bookingValidationRules.create,
  validateRequest,
  BookingController.createBooking
);

// PUT /api/bookings/:id - Cập nhật booking
router.put('/:id',
  authorize(['admin', 'staff']),
  bookingValidationRules.update,
  validateRequest,
  BookingController.updateBooking
);

// DELETE /api/bookings/:id - Hủy booking
router.delete('/:id',
  authorize(['admin', 'staff', 'customer']),
  BookingController.cancelBooking
);

// POST /api/bookings/:id/confirm - Xác nhận booking
router.post('/:id/confirm',
  authorize(['admin', 'staff']),
  BookingController.confirmBooking
);

// POST /api/bookings/:id/complete - Hoàn thành booking
router.post('/:id/complete',
  authorize(['admin', 'staff']),
  BookingController.completeBooking
);

// GET /api/bookings/availability/check - Kiểm tra availability
router.get('/availability/check',
  authorize(['admin', 'staff', 'customer']),
  BookingController.checkAvailability
);

module.exports = router;
```

#### 5. **swagger.js** - API Documentation
```javascript
// Ví dụ: modules/bookings/swagger.js
/**
 * @swagger
 * components:
 *   schemas:
 *     Booking:
 *       type: object
 *       required:
 *         - customerId
 *         - serviceId
 *         - employeeId
 *         - branchId
 *         - bookingDate
 *         - startTime
 *         - totalAmount
 *       properties:
 *         id:
 *           type: integer
 *           description: ID của booking
 *         bookingCode:
 *           type: string
 *           description: Mã booking duy nhất
 *           example: "BK202506100001"
 *         customerId:
 *           type: integer
 *           description: ID khách hàng
 *         serviceId:
 *           type: integer
 *           description: ID dịch vụ
 *         employeeId:
 *           type: integer
 *           description: ID nhân viên
 *         branchId:
 *           type: integer
 *           description: ID chi nhánh
 *         bookingDate:
 *           type: string
 *           format: date
 *           description: Ngày đặt lịch
 *           example: "2025-06-10"
 *         startTime:
 *           type: string
 *           format: time
 *           description: Giờ bắt đầu
 *           example: "14:00"
 *         endTime:
 *           type: string
 *           format: time
 *           description: Giờ kết thúc
 *           example: "15:30"
 *         status:
 *           type: string
 *           enum: [pending, confirmed, cancelled, completed, no_show]
 *           description: Trạng thái booking
 *         customerNote:
 *           type: string
 *           description: Ghi chú từ khách hàng
 *         totalAmount:
 *           type: number
 *           format: decimal
 *           description: Tổng tiền
 *         paymentStatus:
 *           type: string
 *           enum: [unpaid, deposit, paid]
 *           description: Trạng thái thanh toán
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian tạo
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Thời gian cập nhật
 */

/**
 * @swagger
 * /api/bookings:
 *   get:
 *     summary: Lấy danh sách bookings
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Số trang
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Số lượng items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, confirmed, cancelled, completed, no_show]
 *         description: Lọc theo trạng thái
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: integer
 *         description: Lọc theo chi nhánh
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Từ ngày
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Đến ngày
 *     responses:
 *       200:
 *         description: Danh sách bookings
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Booking'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                 message:
 *                   type: string
 *                   example: "Bookings retrieved successfully"
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 *   post:
 *     summary: Tạo booking mới
 *     tags: [Bookings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerId
 *               - serviceId
 *               - employeeId
 *               - branchId
 *               - bookingDate
 *               - startTime
 *             properties:
 *               customerId:
 *                 type: integer
 *                 example: 1
 *               serviceId:
 *                 type: integer
 *                 example: 1
 *               employeeId:
 *                 type: integer
 *                 example: 1
 *               branchId:
 *                 type: integer
 *                 example: 1
 *               bookingDate:
 *                 type: string
 *                 format: date
 *                 example: "2025-06-10"
 *               startTime:
 *                 type: string
 *                 format: time
 *                 example: "14:00"
 *               customerNote:
 *                 type: string
 *                 example: "Tôi muốn massage nhẹ nhàng"
 *     responses:
 *       201:
 *         description: Booking created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Booking'
 *                 message:
 *                   type: string
 *                   example: "Booking created successfully"
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Time slot conflict
 *       500:
 *         description: Server error
 */

module.exports = {};
```

## Cơ Chế Alter Table Linh Hoạt

### 1. Cấu Hình Alter Table
```javascript
// src/config/database.js
const config = {
  development: {
    username: process.env.DB_USERNAME || 'spa_user',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_DATABASE || 'spa_booking_system',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log,

    // Cấu hình alter table
    alter: {
      enabled: process.env.DB_ALTER_ENABLED === 'true', // Bật/tắt alter table
      drop: process.env.DB_ALTER_DROP === 'true',       // Cho phép drop columns
      force: process.env.DB_ALTER_FORCE === 'true'      // Force alter (nguy hiểm)
    },

    // Sync options
    sync: {
      force: process.env.DB_SYNC_FORCE === 'true',      // Drop và recreate tables
      alter: process.env.DB_SYNC_ALTER === 'true'       // Alter existing tables
    }
  },

  production: {
    // ... production config
    alter: {
      enabled: false,  // Tắt alter table trong production
      drop: false,
      force: false
    },
    sync: {
      force: false,
      alter: false
    }
  }
};

module.exports = config;
```

### 2. Database Sync Service
```javascript
// src/database/sync.js
const sequelize = require('./connection');
const config = require('../config/database');
const logger = require('../utils/logger');

class DatabaseSyncService {
  constructor() {
    this.config = config[process.env.NODE_ENV || 'development'];
  }

  async syncDatabase() {
    try {
      logger.info('Starting database synchronization...');

      if (this.config.sync.force) {
        logger.warn('FORCE SYNC: This will drop all tables!');
        await sequelize.sync({ force: true });
        logger.info('Database force synced successfully');
      } else if (this.config.sync.alter) {
        logger.info('ALTER SYNC: Updating table structures...');
        await sequelize.sync({ alter: this.config.alter });
        logger.info('Database alter synced successfully');
      } else {
        logger.info('SAFE SYNC: Creating missing tables only...');
        await sequelize.sync();
        logger.info('Database synced successfully');
      }

    } catch (error) {
      logger.error('Database sync failed:', error);
      throw error;
    }
  }

  async syncModel(modelName, options = {}) {
    try {
      const model = sequelize.models[modelName];
      if (!model) {
        throw new Error(`Model ${modelName} not found`);
      }

      const syncOptions = {
        ...this.config.alter,
        ...options
      };

      if (syncOptions.enabled) {
        logger.info(`Syncing model: ${modelName}`);
        await model.sync({ alter: syncOptions });
        logger.info(`Model ${modelName} synced successfully`);
      } else {
        logger.warn(`Alter table disabled for model: ${modelName}`);
      }

    } catch (error) {
      logger.error(`Failed to sync model ${modelName}:`, error);
      throw error;
    }
  }

  async checkTableStructure(modelName) {
    try {
      const model = sequelize.models[modelName];
      if (!model) {
        throw new Error(`Model ${modelName} not found`);
      }

      const tableInfo = await sequelize.getQueryInterface().describeTable(model.tableName);
      const modelAttributes = model.getTableName ? model.getTableName() : model.tableName;

      logger.info(`Table structure for ${modelName}:`, tableInfo);
      return tableInfo;

    } catch (error) {
      logger.error(`Failed to check table structure for ${modelName}:`, error);
      throw error;
    }
  }

  async alterTableColumn(tableName, columnName, columnDefinition) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();

      logger.info(`Altering column ${columnName} in table ${tableName}`);
      await queryInterface.changeColumn(tableName, columnName, columnDefinition);
      logger.info(`Column ${columnName} altered successfully`);

    } catch (error) {
      logger.error(`Failed to alter column ${columnName}:`, error);
      throw error;
    }
  }

  async addTableColumn(tableName, columnName, columnDefinition) {
    try {
      if (!this.config.alter.enabled) {
        throw new Error('Alter table is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();

      logger.info(`Adding column ${columnName} to table ${tableName}`);
      await queryInterface.addColumn(tableName, columnName, columnDefinition);
      logger.info(`Column ${columnName} added successfully`);

    } catch (error) {
      logger.error(`Failed to add column ${columnName}:`, error);
      throw error;
    }
  }

  async removeTableColumn(tableName, columnName) {
    try {
      if (!this.config.alter.enabled || !this.config.alter.drop) {
        throw new Error('Drop column is disabled');
      }

      const queryInterface = sequelize.getQueryInterface();

      logger.warn(`Removing column ${columnName} from table ${tableName}`);
      await queryInterface.removeColumn(tableName, columnName);
      logger.info(`Column ${columnName} removed successfully`);

    } catch (error) {
      logger.error(`Failed to remove column ${columnName}:`, error);
      throw error;
    }
  }
}

module.exports = new DatabaseSyncService();
```

### 3. Model với Alter Table Support
```javascript
// Ví dụ: modules/bookings/model.js (với alter table support)
const { DataTypes } = require('sequelize');
const sequelize = require('../../database/connection');
const DatabaseSyncService = require('../../database/sync');

const Booking = sequelize.define('Booking', {
  // ... model definition như trên
}, {
  tableName: 'bookings',
  timestamps: true,
  underscored: true,

  // Hooks để handle alter table
  hooks: {
    afterSync: async (options) => {
      // Kiểm tra và thực hiện alter table nếu cần
      if (options.alter && process.env.DB_ALTER_ENABLED === 'true') {
        console.log('Checking table structure for Booking model...');
        await DatabaseSyncService.checkTableStructure('Booking');
      }
    }
  }
});

// Method để sync model với alter table
Booking.syncWithAlter = async (options = {}) => {
  const alterEnabled = process.env.DB_ALTER_ENABLED === 'true';

  if (alterEnabled) {
    console.log('Syncing Booking model with alter table...');
    return await DatabaseSyncService.syncModel('Booking', options);
  } else {
    console.log('Alter table disabled, using safe sync...');
    return await Booking.sync();
  }
};

module.exports = Booking;
```

### 4. CLI Commands cho Alter Table
```javascript
// scripts/db-alter.js
const DatabaseSyncService = require('../src/database/sync');
const sequelize = require('../src/database/connection');

const commands = {
  // Sync tất cả models
  syncAll: async () => {
    await DatabaseSyncService.syncDatabase();
  },

  // Sync một model cụ thể
  syncModel: async (modelName) => {
    await DatabaseSyncService.syncModel(modelName);
  },

  // Kiểm tra cấu trúc table
  checkTable: async (modelName) => {
    await DatabaseSyncService.checkTableStructure(modelName);
  },

  // Alter một column
  alterColumn: async (tableName, columnName, columnType) => {
    const columnDefinition = { type: columnType };
    await DatabaseSyncService.alterTableColumn(tableName, columnName, columnDefinition);
  },

  // Thêm column
  addColumn: async (tableName, columnName, columnType) => {
    const columnDefinition = { type: columnType };
    await DatabaseSyncService.addTableColumn(tableName, columnName, columnDefinition);
  },

  // Xóa column
  removeColumn: async (tableName, columnName) => {
    await DatabaseSyncService.removeTableColumn(tableName, columnName);
  }
};

// CLI interface
const [,, command, ...args] = process.argv;

async function runCommand() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully');

    switch (command) {
      case 'sync-all':
        await commands.syncAll();
        break;
      case 'sync-model':
        await commands.syncModel(args[0]);
        break;
      case 'check-table':
        await commands.checkTable(args[0]);
        break;
      case 'alter-column':
        await commands.alterColumn(args[0], args[1], args[2]);
        break;
      case 'add-column':
        await commands.addColumn(args[0], args[1], args[2]);
        break;
      case 'remove-column':
        await commands.removeColumn(args[0], args[1]);
        break;
      default:
        console.log('Available commands:');
        console.log('  sync-all                    - Sync all models');
        console.log('  sync-model <modelName>      - Sync specific model');
        console.log('  check-table <modelName>     - Check table structure');
        console.log('  alter-column <table> <col> <type> - Alter column');
        console.log('  add-column <table> <col> <type>   - Add column');
        console.log('  remove-column <table> <col>       - Remove column');
    }
  } catch (error) {
    console.error('Command failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

runCommand();
```

### 5. Environment Variables cho Alter Table
```bash
# .env.development
# Database Alter Table Configuration
DB_ALTER_ENABLED=true          # Bật/tắt alter table
DB_ALTER_DROP=false            # Cho phép drop columns
DB_ALTER_FORCE=false           # Force alter (nguy hiểm)

# Database Sync Configuration
DB_SYNC_FORCE=false            # Drop và recreate tables
DB_SYNC_ALTER=true             # Alter existing tables

# .env.production
DB_ALTER_ENABLED=false         # Tắt alter table trong production
DB_ALTER_DROP=false
DB_ALTER_FORCE=false
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false
```

## Quy Trình Phát Triển

### 1. Setup Dự Án
```bash
# Khởi tạo project
mkdir booking-backend
cd booking-backend
npm init -y

# Cài đặt dependencies
npm install express sequelize mysql2 bcryptjs jsonwebtoken
npm install cors helmet morgan compression dotenv
npm install express-rate-limit express-validator
npm install swagger-jsdoc swagger-ui-express
npm install moment-timezone

# Dev dependencies
npm install --save-dev nodemon jest supertest
npm install --save-dev eslint prettier husky lint-staged

# Tạo cấu trúc thư mục
mkdir -p src/{config,middleware,utils,database/{migrations,seeders},modules}
mkdir -p tests/{unit,integration,fixtures}
mkdir -p docs logs uploads
```

### 2. Cấu Hình Package.json
```json
{
  "name": "spa-booking-backend",
  "version": "1.0.0",
  "description": "Spa Booking System Backend API",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/",
    "db:sync": "node scripts/db-alter.js sync-all",
    "db:sync:model": "node scripts/db-alter.js sync-model",
    "db:check": "node scripts/db-alter.js check-table",
    "db:alter": "node scripts/db-alter.js alter-column",
    "db:add": "node scripts/db-alter.js add-column",
    "db:remove": "node scripts/db-alter.js remove-column"
  },
  "keywords": ["spa", "booking", "api", "express", "sequelize"],
  "author": "Your Name",
  "license": "MIT"
}
```

### 3. Workflow Development
1. **Tạo Model**: Định nghĩa model trong `modules/{module}/model.js`
2. **Kiểm tra Alter**: Chạy `npm run db:check {ModelName}` để kiểm tra cấu trúc
3. **Sync Model**: Chạy `npm run db:sync:model {ModelName}` để đồng bộ
4. **Tạo Service**: Implement business logic trong `service.js`
5. **Tạo Controller**: Implement HTTP handlers trong `controller.js`
6. **Tạo Routes**: Định nghĩa API endpoints trong `route.js`
7. **Tạo Documentation**: Viết Swagger docs trong `swagger.js`
8. **Testing**: Viết unit tests và integration tests
9. **Deploy**: Deploy lên staging/production

## Best Practices cho Express.js và Sequelize

### 1. Express.js Best Practices

#### Application Structure
```javascript
// src/app.js - Main application file
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const { errorHandler } = require('./middleware/errorHandler');
const { notFound } = require('./middleware/notFound');
const routes = require('./routes');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression
app.use(compression());

// Logging
app.use(morgan('combined'));

// Routes
app.use('/api', routes);

// Swagger documentation
if (process.env.NODE_ENV !== 'production') {
  const swaggerUi = require('swagger-ui-express');
  const swaggerSpec = require('./config/swagger');
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

// Error handling
app.use(notFound);
app.use(errorHandler);

module.exports = app;
```

#### Error Handling
```javascript
// src/middleware/errorHandler.js
const { ValidationError, DatabaseError } = require('sequelize');
const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  logger.error('Error occurred:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Sequelize validation errors
  if (err instanceof ValidationError) {
    const errors = err.errors.map(error => ({
      field: error.path,
      message: error.message,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: errors
      }
    });
  }

  // Sequelize database errors
  if (err instanceof DatabaseError) {
    return res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Database operation failed'
      }
    });
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid authentication token'
      }
    });
  }

  // Default error
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';

  res.status(statusCode).json({
    success: false,
    error: {
      code: err.code || 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : message
    }
  });
};

const notFound = (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.originalUrl} not found`
    }
  });
};

module.exports = { errorHandler, notFound };
```

#### Response Utilities
```javascript
// src/utils/response.js
const successResponse = (res, data, message = 'Success', statusCode = 200, pagination = null) => {
  const response = {
    success: true,
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0'
    }
  };

  if (pagination) {
    response.pagination = pagination;
  }

  return res.status(statusCode).json(response);
};

const errorResponse = (res, message, statusCode = 400, code = null, details = null) => {
  const response = {
    success: false,
    error: {
      code: code || 'BAD_REQUEST',
      message
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0'
    }
  };

  if (details) {
    response.error.details = details;
  }

  return res.status(statusCode).json(response);
};

module.exports = {
  successResponse,
  errorResponse
};
```

### 2. Sequelize Best Practices

#### Model Definition
```javascript
// Best practices cho model definition
const { DataTypes } = require('sequelize');
const sequelize = require('../../database/connection');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  email: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: false,
    validate: {
      isEmail: true,
      len: [5, 100]
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [2, 100],
      notEmpty: true
    }
  },
  role: {
    type: DataTypes.ENUM('admin', 'staff', 'customer'),
    defaultValue: 'customer',
    allowNull: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active'
  },
  lastLogin: {
    type: DataTypes.DATE,
    field: 'last_login'
  }
}, {
  tableName: 'users',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete

  // Indexes
  indexes: [
    { fields: ['email'] },
    { fields: ['role'] },
    { fields: ['is_active'] },
    { fields: ['created_at'] }
  ],

  // Hooks
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        const bcrypt = require('bcryptjs');
        user.password = await bcrypt.hash(user.password, 10);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        const bcrypt = require('bcryptjs');
        user.password = await bcrypt.hash(user.password, 10);
      }
    }
  },

  // Scopes
  scopes: {
    active: {
      where: { isActive: true }
    },
    withoutPassword: {
      attributes: { exclude: ['password'] }
    }
  }
});

// Instance methods
User.prototype.comparePassword = async function(password) {
  const bcrypt = require('bcryptjs');
  return await bcrypt.compare(password, this.password);
};

User.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password;
  return values;
};

// Class methods
User.findByEmail = function(email) {
  return this.findOne({ where: { email } });
};

// Associations
User.associate = (models) => {
  User.belongsTo(models.Branch, { foreignKey: 'branchId', as: 'branch' });
  User.hasMany(models.Booking, { foreignKey: 'createdBy', as: 'createdBookings' });
};

module.exports = User;
```

#### Query Optimization
```javascript
// src/utils/queryOptimization.js
class QueryOptimizer {
  // Pagination helper
  static paginate(page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    return { limit: parseInt(limit), offset: parseInt(offset) };
  }

  // Include associations efficiently
  static includeAssociations(includes = []) {
    return includes.map(include => {
      if (typeof include === 'string') {
        return { association: include, required: false };
      }
      return { ...include, required: false };
    });
  }

  // Build where clause from filters
  static buildWhereClause(filters, allowedFields = []) {
    const { Op } = require('sequelize');
    const where = {};

    Object.keys(filters).forEach(key => {
      if (allowedFields.includes(key) && filters[key] !== undefined) {
        const value = filters[key];

        // Handle different filter types
        if (key.endsWith('_like')) {
          const field = key.replace('_like', '');
          where[field] = { [Op.like]: `%${value}%` };
        } else if (key.endsWith('_in')) {
          const field = key.replace('_in', '');
          where[field] = { [Op.in]: Array.isArray(value) ? value : [value] };
        } else if (key.endsWith('_between')) {
          const field = key.replace('_between', '');
          if (Array.isArray(value) && value.length === 2) {
            where[field] = { [Op.between]: value };
          }
        } else {
          where[key] = value;
        }
      }
    });

    return where;
  }

  // Optimize SELECT queries
  static selectFields(fields = []) {
    if (fields.length === 0) return undefined;
    return { attributes: fields };
  }
}

module.exports = QueryOptimizer;
```

#### Transaction Management
```javascript
// src/utils/transaction.js
const sequelize = require('../database/connection');

class TransactionManager {
  static async executeInTransaction(callback) {
    const transaction = await sequelize.transaction();

    try {
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  static async executeWithRetry(callback, maxRetries = 3) {
    let lastError;

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.executeInTransaction(callback);
      } catch (error) {
        lastError = error;

        // Retry only for specific errors
        if (this.shouldRetry(error) && i < maxRetries - 1) {
          await this.delay(Math.pow(2, i) * 1000); // Exponential backoff
          continue;
        }

        throw error;
      }
    }

    throw lastError;
  }

  static shouldRetry(error) {
    // Retry for deadlocks, connection errors, etc.
    const retryableErrors = [
      'ER_LOCK_DEADLOCK',
      'ER_LOCK_WAIT_TIMEOUT',
      'ECONNRESET',
      'ENOTFOUND'
    ];

    return retryableErrors.some(code =>
      error.code === code || error.message.includes(code)
    );
  }

  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = TransactionManager;
```

### 3. Security Best Practices

#### Authentication Middleware
```javascript
// src/middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../modules/users/model');
const { errorResponse } = require('../utils/response');

const authenticate = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return errorResponse(res, 'Access token required', 401, 'MISSING_TOKEN');
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user || !user.isActive) {
      return errorResponse(res, 'Invalid or inactive user', 401, 'INVALID_USER');
    }

    req.user = user;
    next();
  } catch (error) {
    return errorResponse(res, 'Invalid token', 401, 'INVALID_TOKEN');
  }
};

const authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return errorResponse(res, 'Authentication required', 401, 'AUTH_REQUIRED');
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return errorResponse(res, 'Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    next();
  };
};

module.exports = { authenticate, authorize };
```

#### Input Validation
```javascript
// src/middleware/validation.js
const { body, param, query, validationResult } = require('express-validator');
const { errorResponse } = require('../utils/response');

const validateRequest = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    return errorResponse(res, 'Validation failed', 400, 'VALIDATION_ERROR', formattedErrors);
  }

  next();
};

// Validation rules for booking
const bookingValidationRules = {
  create: [
    body('customerId').isInt({ min: 1 }).withMessage('Valid customer ID required'),
    body('serviceId').isInt({ min: 1 }).withMessage('Valid service ID required'),
    body('employeeId').isInt({ min: 1 }).withMessage('Valid employee ID required'),
    body('branchId').isInt({ min: 1 }).withMessage('Valid branch ID required'),
    body('bookingDate').isISO8601().withMessage('Valid date required (YYYY-MM-DD)'),
    body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid time required (HH:MM)'),
    body('customerNote').optional().isLength({ max: 500 }).withMessage('Note too long')
  ],

  update: [
    param('id').isInt({ min: 1 }).withMessage('Valid booking ID required'),
    body('status').optional().isIn(['pending', 'confirmed', 'cancelled', 'completed', 'no_show']).withMessage('Invalid status'),
    body('staffNote').optional().isLength({ max: 500 }).withMessage('Note too long')
  ]
};

module.exports = {
  validateRequest,
  bookingValidationRules
};
```

## Deployment và Production

### 1. Environment Configuration
```bash
# .env.production
NODE_ENV=production
PORT=3000
API_VERSION=1.0.0

# Database
DB_HOST=your-production-db-host
DB_PORT=3306
DB_USERNAME=your-db-user
DB_PASSWORD=your-secure-password
DB_DATABASE=spa_booking_production
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Disable alter table in production
DB_ALTER_ENABLED=false
DB_ALTER_DROP=false
DB_ALTER_FORCE=false
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false

# Security
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRES_IN=7d

# CORS
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Redis (if using)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### 2. Docker Configuration
```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY scripts/ ./scripts/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node scripts/health-check.js

# Start application
CMD ["node", "src/app.js"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 3. Monitoring và Logging
```javascript
// src/utils/logger.js
const winston = require('winston');
const path = require('path');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'spa-booking-api' },
  transports: [
    // Write all logs with level `error` and below to `error.log`
    new winston.transports.File({
      filename: path.join(process.env.LOG_FILE_PATH || './logs', 'error.log'),
      level: 'error'
    }),
    // Write all logs with level `info` and below to `combined.log`
    new winston.transports.File({
      filename: path.join(process.env.LOG_FILE_PATH || './logs', 'combined.log')
    })
  ]
});

// If we're not in production then log to the `console`
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

## Testing Strategy

### 1. Unit Testing
```javascript
// tests/unit/services/booking.test.js
const BookingService = require('../../../src/modules/bookings/service');
const Booking = require('../../../src/modules/bookings/model');

jest.mock('../../../src/modules/bookings/model');

describe('BookingService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createBooking', () => {
    it('should create a booking successfully', async () => {
      const bookingData = {
        customerId: 1,
        serviceId: 1,
        employeeId: 1,
        branchId: 1,
        bookingDate: '2025-06-15',
        startTime: '14:00',
        totalAmount: 500000
      };

      const mockBooking = {
        id: 1,
        ...bookingData,
        bookingCode: 'BK202506150001',
        status: 'pending'
      };

      Booking.findOne.mockResolvedValue(null); // No conflicts
      Booking.create.mockResolvedValue(mockBooking);

      const result = await BookingService.createBooking(bookingData);

      expect(result).toEqual(mockBooking);
      expect(Booking.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...bookingData,
          bookingCode: expect.stringMatching(/^BK\d{8}\d{4}$/)
        })
      );
    });

    it('should throw error when time slot is not available', async () => {
      const bookingData = {
        customerId: 1,
        serviceId: 1,
        employeeId: 1,
        branchId: 1,
        bookingDate: '2025-06-15',
        startTime: '14:00',
        totalAmount: 500000
      };

      Booking.findOne.mockResolvedValue({ id: 1 }); // Conflict exists

      await expect(BookingService.createBooking(bookingData))
        .rejects.toThrow('Time slot is not available');
    });
  });
});
```

### 2. Integration Testing
```javascript
// tests/integration/bookings.test.js
const request = require('supertest');
const app = require('../../src/app');
const sequelize = require('../../src/database/connection');

describe('Bookings API', () => {
  let authToken;
  let testUser;

  beforeAll(async () => {
    await sequelize.sync({ force: true });

    // Create test user and get auth token
    const response = await request(app)
      .post('/api/auth/register')
      .send({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        phone: '0123456789'
      });

    testUser = response.body.data.user;
    authToken = response.body.data.token;
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/bookings', () => {
    it('should create a new booking', async () => {
      const bookingData = {
        customerId: 1,
        serviceId: 1,
        employeeId: 1,
        branchId: 1,
        bookingDate: '2025-06-15',
        startTime: '14:00'
      };

      const response = await request(app)
        .post('/api/bookings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(bookingData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('bookingCode');
      expect(response.body.data.status).toBe('pending');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/bookings')
        .send({})
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('MISSING_TOKEN');
    });
  });
});
```

## Kết Luận

Kế hoạch này cung cấp một framework hoàn chỉnh để phát triển hệ thống booking spa backend với:

### Ưu Điểm Chính:
1. **Cấu trúc module rõ ràng**: Mỗi module độc lập với controller, service, model, route và swagger
2. **Cơ chế alter table linh hoạt**: Có thể bật/tắt và kiểm soát việc đồng bộ database
3. **Best practices**: Tuân thủ các best practices của Express.js và Sequelize
4. **Security**: Implement đầy đủ authentication, authorization và validation
5. **Scalability**: Cấu trúc dễ mở rộng và maintain
6. **Testing**: Strategy testing hoàn chỉnh với unit và integration tests
7. **Production ready**: Cấu hình đầy đủ cho deployment và monitoring

### Các Tính Năng Nổi Bật:
- **Alter Table Control**: Kiểm soát việc alter table thông qua environment variables
- **Database Sync Service**: Service chuyên dụng để quản lý database synchronization
- **CLI Commands**: Commands để thực hiện các thao tác database từ command line
- **Error Handling**: Xử lý lỗi toàn diện và standardized
- **API Documentation**: Swagger documentation tự động
- **Security**: JWT authentication, role-based authorization, input validation
- **Performance**: Query optimization, caching, rate limiting

Kế hoạch này đảm bảo việc phát triển hệ thống booking spa backend sẽ được thực hiện một cách có tổ chức, an toàn và hiệu quả.
```
```
```
```
```