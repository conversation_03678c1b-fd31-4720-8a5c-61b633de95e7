# 📋 FormBooker - SAAS Booking Form Generator Platform

**Tagline:** "Create booking forms in minutes, share anywhere"

FormBooker is a comprehensive SAAS platform that allows businesses to generate embeddable booking forms. Businesses can either embed these forms into their websites OR get standalone page links to share on social media for customers to book their services.

## 🎯 **Core Features**

### **For Business Owners:**
- ✅ **Simple Registration & Login** - Quick account setup
- ✅ **Intuitive Dashboard** - Overview of forms, bookings, and stats
- ✅ **Form Builder** - Create custom booking forms in minutes
- ✅ **Booking Management** - View and manage all incoming bookings
- ✅ **Service Management** - Add/edit services offered
- ✅ **Branch Management** - Manage multiple business locations
- ✅ **Dual Sharing Options** - Embeddable code AND standalone links

### **For End Customers:**
- ✅ **Easy Booking** - Simple, mobile-friendly booking forms
- ✅ **Instant Confirmation** - Immediate booking confirmation
- ✅ **Professional Experience** - Clean, branded booking process

## 🏗️ **Technology Stack**

- **Framework:** Next.js 15 with App Router
- **Styling:** Tailwind CSS with custom design system
- **Language:** JavaScript (JSX)
- **Icons:** Lucide React
- **Fonts:** Inter (Google Fonts)
- **Development:** Turbopack for fast development

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+ 
- npm or yarn

### **Installation**

```bash
# Clone the repository
git clone <repository-url>
cd formBooker-frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Or with Turbopack (faster)
npm run dev:turbo
```

### **Development Server**
- **URL:** http://localhost:3001
- **Hot Reload:** Enabled
- **Turbopack:** Available for faster builds

## 📱 **Page Structure**

### **Public Pages**
- `/` - Landing page with features and demo
- `/login` - User authentication
- `/register` - New user registration

### **Dashboard Pages** (Protected)
- `/dashboard` - Main overview with stats and quick actions
- `/forms` - Manage booking forms
- `/forms/new` - Create new booking form
- `/forms/edit/[id]` - Edit existing form
- `/bookings` - View and manage all bookings
- `/services` - Manage services offered
- `/branches` - Manage business locations
- `/settings` - Account and notification settings

### **Public Booking Pages**
- `/book/[formId]` - Customer booking form (public)

## 🎨 **Design System**

### **Color Palette**
- **Primary:** #3b82f6 (Blue) - Main brand color
- **Secondary:** #10b981 (Green) - Success states
- **Accent:** #f59e0b (Orange) - Call-to-action
- **Neutral:** #6b7280 (Gray) - Text and borders
- **Background:** #f9fafb (Light Gray) - Page backgrounds

### **Typography**
- **Font Family:** Inter (Google Fonts)
- **Headings:** Bold weights for hierarchy
- **Body:** Regular weight for readability
- **UI Elements:** Medium weight for emphasis

### **Components**
- **Buttons:** Multiple variants (primary, secondary, outline, ghost)
- **Cards:** Clean with subtle shadows
- **Forms:** Consistent styling with validation states
- **Badges:** Status indicators with color coding
- **Navigation:** Active states and hover effects

## 📋 **User Flow**

### **Business Owner Journey**
1. **Landing Page** → View features and benefits
2. **Registration** → Create business account
3. **Dashboard** → See overview and quick actions
4. **Service Setup** → Add services offered
5. **Branch Setup** → Add business locations
6. **Form Creation** → Build custom booking forms
7. **Form Sharing** → Get embed code or standalone links
8. **Booking Management** → Handle incoming bookings

### **Customer Journey**
1. **Form Discovery** → Find form via website embed or social link
2. **Service Selection** → View service details and pricing
3. **Information Entry** → Fill out booking details
4. **Booking Submission** → Submit appointment request
5. **Confirmation** → Receive booking confirmation

## 🔧 **Development Scripts**

```bash
# Development
npm run dev              # Start development server
npm run dev:turbo        # Start with Turbopack (faster)

# Building
npm run build            # Build for production
npm run start            # Start production server

# Code Quality
npm run lint             # Check for linting errors
npm run lint:fix         # Fix linting errors
npm run format           # Format code with Prettier
npm run format:check     # Check code formatting

# Utilities
npm run clean            # Clean build artifacts
npm run type-check       # TypeScript type checking
npm run analyze          # Bundle analyzer
```

## 📁 **Project Structure**

```
formBooker-frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/            # Authentication pages
│   │   ├── book/              # Public booking forms
│   │   ├── dashboard/         # Main dashboard
│   │   ├── forms/             # Form management
│   │   ├── bookings/          # Booking management
│   │   ├── services/          # Service management
│   │   ├── branches/          # Branch management
│   │   ├── settings/          # Account settings
│   │   ├── globals.css        # Global styles
│   │   └── layout.js          # Root layout
│   └── components/
│       └── layout/
│           └── DashboardLayout.js  # Dashboard layout
├── public/                    # Static assets
├── tailwind.config.js         # Tailwind configuration
├── next.config.js            # Next.js configuration
└── package.json              # Dependencies and scripts
```

## 🎯 **Key Features Implementation**

### **Form Builder**
- Drag-and-drop interface (planned)
- Field customization
- Live preview
- Service and branch selection
- Form validation

### **Booking Management**
- Real-time booking list
- Status management
- Customer contact integration
- Filtering and search
- Booking details modal

### **Sharing System**
- Embeddable iframe code generation
- Standalone page links
- Copy-to-clipboard functionality
- Social media ready URLs

### **Mobile Responsive**
- Mobile-first design approach
- Touch-friendly interfaces
- Responsive navigation
- Optimized form layouts

## 🔒 **Security Features**

- Form validation on client and server
- Input sanitization
- Protected routes
- Secure form submissions
- Data validation

## 🚀 **Deployment**

### **Build for Production**
```bash
npm run build
npm run start
```

### **Environment Variables**
```bash
NEXT_PUBLIC_APP_NAME="FormBooker"
NEXT_PUBLIC_APP_TAGLINE="Create booking forms in minutes, share anywhere"
NEXT_PUBLIC_API_URL="http://localhost:3000"
NEXT_PUBLIC_FRONTEND_URL="http://localhost:3001"
```

## 📈 **Performance**

- **Next.js 15** - Latest performance optimizations
- **Turbopack** - Fast development builds
- **Tailwind CSS** - Optimized CSS bundle
- **Code Splitting** - Automatic route-based splitting
- **Image Optimization** - Next.js built-in optimization

## 🎉 **Getting Started**

1. **Start the development server:** `npm run dev:turbo`
2. **Visit:** http://localhost:3001
3. **Explore the landing page** and features
4. **Register a new account** or use demo credentials
5. **Create your first booking form**
6. **Share the form** and start receiving bookings!

## 📞 **Support**

For questions, issues, or feature requests:
- Check the documentation
- Review the code comments
- Test with the demo data provided

---

**FormBooker** - Simplifying booking management for businesses everywhere! 🎯
