/**
 * Simple server starter for debugging
 */

console.log('🚀 Starting Spa Booking System Backend...');

try {
  // Load environment first
  require('dotenv').config();
  console.log('✅ Environment loaded');

  console.log('1. Loading app...');
  const app = require('./src/app');
  console.log('✅ App loaded');

  console.log('2. Starting server...');
  const port = process.env.APP_PORT || 3000;

  const server = app.listen(port, '0.0.0.0', () => {
    console.log('✅ Server started successfully');
    console.log(`🔗 API URL: http://localhost:${port}`);
    console.log(`📖 API Documentation: http://localhost:${port}/api-docs`);
    console.log('🎉 Ready to accept connections!');
  });

  server.on('error', (error) => {
    console.error('❌ Server error:', error.message);
    if (error.code === 'EADDRINUSE') {
      console.error(`Port ${port} is already in use. Please use a different port.`);
    }
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down gracefully...');
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });

} catch (error) {
  console.error('❌ Failed to start server:', error.message);
  console.error(error.stack);
  process.exit(1);
}
