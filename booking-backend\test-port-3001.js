const axios = require('axios');

async function testBackendPort() {
  console.log('🔍 Testing backend at port 3001...\n');
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ Health:', healthResponse.data);
    
    // Test API endpoint
    console.log('\n2. Testing API endpoint...');
    const apiResponse = await axios.get('http://localhost:3001/api/health');
    console.log('✅ API Health:', apiResponse.data);
    
    // Test public booking endpoint with same data as frontend would send
    console.log('\n3. Testing public booking endpoint...');
    const bookingData = {
      formSlug: 'test-public-form',
      customerName: 'Test Frontend User',
      phoneNumber: '**********',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-20',
      preferredTime: '14:00',
      specialRequests: 'Testing from frontend integration'
    };
    
    const bookingResponse = await axios.post('http://localhost:3001/api/public/bookings', bookingData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Booking created:', bookingResponse.data);
    
    console.log('\n🎉 Backend at port 3001 is working correctly!');
    
  } catch (error) {
    console.error('❌ Backend test failed:', error.response?.status || 'No status');
    console.error('📄 Error:', JSON.stringify(error.response?.data || error.message, null, 2));
    console.error('📍 URL:', error.config?.url);
  }
}

testBackendPort();
