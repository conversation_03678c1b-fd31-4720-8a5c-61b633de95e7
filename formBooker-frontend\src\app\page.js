'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  CheckCircle,
  ArrowRight,
  Calendar,
  Share2,
  Smartphone,
  Zap,
  Users,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

/**
 * FormBooker Landing Page
 * Implements the exact wireframe from plan-ui-new.md
 */
export default function LandingPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      router.replace('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex flex-col justify-center items-center">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary-500 rounded-xl flex items-center justify-center">
                <Calendar className="h-7 w-7 text-white" />
              </div>
              <span className="ml-3 text-2xl font-bold text-text">FormBooker</span>
            </div>
          </div>
          <div className="flex justify-center mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-neutral-50">
      {/* Header */}
      <header className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <span className="ml-2 text-xl font-bold text-text">FormBooker</span>
            </div>
            
            {/* Navigation */}
            <div className="flex items-center space-x-4">
              <Link 
                href="/login" 
                className="btn-ghost"
              >
                Sign In
              </Link>
              <Link 
                href="/register" 
                className="btn-primary"
              >
                Get Started Free
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Heading */}
          <h1 className="text-5xl md:text-6xl font-bold text-text mb-6">
            FormBooker
          </h1>
          <p className="text-xl md:text-2xl text-neutral-600 mb-8 text-balance">
            Create Booking Forms in Minutes
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link 
              href="/register" 
              className="btn-primary btn-lg inline-flex items-center"
            >
              Get Started Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link 
              href="/login" 
              className="btn-outline btn-lg"
            >
              Sign In
            </Link>
          </div>

          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            <div className="flex items-center justify-center md:justify-start">
              <CheckCircle className="h-5 w-5 text-secondary-500 mr-2 flex-shrink-0" />
              <span className="text-neutral-700">Embeddable forms</span>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <CheckCircle className="h-5 w-5 text-secondary-500 mr-2 flex-shrink-0" />
              <span className="text-neutral-700">Standalone booking pages</span>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <CheckCircle className="h-5 w-5 text-secondary-500 mr-2 flex-shrink-0" />
              <span className="text-neutral-700">Social media sharing</span>
            </div>
          </div>

          {/* Demo Form Preview */}
          <div className="card max-w-md mx-auto">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-text">Demo Form Preview</h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div>
                  <label className="form-label">Service</label>
                  <div className="form-input bg-neutral-50">Hair Cut Booking</div>
                </div>
                <div>
                  <label className="form-label">Your Name</label>
                  <div className="form-input bg-neutral-50">John Doe</div>
                </div>
                <div>
                  <label className="form-label">Preferred Date</label>
                  <div className="form-input bg-neutral-50">Dec 15, 2024</div>
                </div>
                <button className="btn-primary w-full" disabled>
                  Book Appointment
                </button>
              </div>
            </div>
            <div className="card-footer text-center">
              <p className="text-xs text-neutral-500">Powered by FormBooker</p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-text mb-4">
              Everything you need to manage bookings
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Simple, powerful tools to create forms, manage bookings, and grow your business
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-text mb-2">Quick Setup</h3>
              <p className="text-neutral-600">Forms ready in minutes with our intuitive builder</p>
            </div>

            {/* Feature 2 */}
            <div className="text-center">
              <div className="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Share2 className="h-6 w-6 text-secondary-600" />
              </div>
              <h3 className="text-lg font-semibold text-text mb-2">Easy Sharing</h3>
              <p className="text-neutral-600">Embed anywhere or share standalone links</p>
            </div>

            {/* Feature 3 */}
            <div className="text-center">
              <div className="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Smartphone className="h-6 w-6 text-accent-600" />
              </div>
              <h3 className="text-lg font-semibold text-text mb-2">Mobile Ready</h3>
              <p className="text-neutral-600">Perfect experience on all devices</p>
            </div>

            {/* Feature 4 */}
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-text mb-2">Analytics</h3>
              <p className="text-neutral-600">Track bookings and conversion rates</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-500">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to streamline your bookings?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Join thousands of businesses using FormBooker to manage their appointments
          </p>
          <Link 
            href="/register" 
            className="btn-accent btn-lg inline-flex items-center"
          >
            Start Free Trial
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Logo & Description */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
                <span className="ml-2 text-xl font-bold">FormBooker</span>
              </div>
              <p className="text-neutral-400 max-w-md">
                Create booking forms in minutes, share anywhere. 
                Simple booking form creation with embeddable widgets and standalone pages.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><Link href="/features" className="text-neutral-400 hover:text-white transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="text-neutral-400 hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/templates" className="text-neutral-400 hover:text-white transition-colors">Templates</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Support</h3>
              <ul className="space-y-2">
                <li><Link href="/help" className="text-neutral-400 hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/contact" className="text-neutral-400 hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/privacy" className="text-neutral-400 hover:text-white transition-colors">Privacy</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-neutral-800 mt-8 pt-8 text-center">
            <p className="text-neutral-400">
              © 2024 FormBooker. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
