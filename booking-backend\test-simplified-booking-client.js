const axios = require('axios');

const BASE_URL = 'http://localhost:3003';

async function testSimplifiedBooking() {
  console.log('🧪 Testing simplified booking flow...\n');
  
  try {
    // 1. Test form lookup
    console.log('1. 📋 Testing form lookup...');
    const formResponse = await axios.get(`${BASE_URL}/api/public/forms/test-public-form`);
    console.log('✅ Form found:', formResponse.data);
    
    console.log('\n2. 📝 Testing booking creation...');
    
    // 2. Test booking creation
    const bookingData = {
      formSlug: 'test-public-form',
      customerName: '<PERSON>',
      phoneNumber: '0123456789',
      emailAddress: '<EMAIL>',
      preferredDate: '2025-06-20',
      preferredTime: '10:00',
      specialRequests: 'Test booking from simplified script'
    };
    
    console.log('📤 Sending booking data:', JSON.stringify(bookingData, null, 2));
    
    const bookingResponse = await axios.post(`${BASE_URL}/api/public/bookings`, bookingData);
    console.log('✅ Booking created:', bookingResponse.data);
    
    console.log('\n3. 📊 Testing bookings list...');
    
    // 3. Test bookings list
    const listResponse = await axios.get(`${BASE_URL}/api/test/bookings`);
    console.log('✅ Bookings list:', listResponse.data);
    
    console.log('\n🎉 All tests passed! Simplified booking flow works correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.status || 'No status');
    console.error('📄 Error details:', JSON.stringify(error.response?.data || error.message, null, 2));
  }
}

testSimplifiedBooking();
