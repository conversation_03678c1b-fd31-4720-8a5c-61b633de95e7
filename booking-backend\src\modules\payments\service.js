/**
 * Payment Service
 * Business logic for payment management
 */

const { Op, sequelize } = require('sequelize');
const Payment = require('./model');
const Booking = require('../bookings/model');
const Customer = require('../customers/model');
const User = require('../users/model');
const Service = require('../services/model');
const Branch = require('../branches/model');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class PaymentService {
  /**
   * Create a new payment
   */
  static async createPayment(paymentData, userId) {
    try {
      // Validate booking exists
      const booking = await Booking.findByPk(paymentData.bookingId);
      if (!booking) {
        throw new AppError('Booking not found', 404, 'BOOKING_NOT_FOUND');
      }

      // Validate customer exists
      const customer = await Customer.findByPk(paymentData.customerId);
      if (!customer) {
        throw new AppError('Customer not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Generate payment code
      const paymentCode = Payment.generatePaymentCode();

      // Calculate net amount (amount - fees)
      const fees = paymentData.fees || 0;
      const netAmount = paymentData.amount - fees;

      // Create payment
      const payment = await Payment.create({
        paymentCode,
        bookingId: paymentData.bookingId,
        customerId: paymentData.customerId,
        amount: paymentData.amount,
        method: paymentData.method,
        status: paymentData.status || 'pending',
        transactionId: paymentData.transactionId,
        gatewayResponse: paymentData.gatewayResponse,
        paymentDate: paymentData.paymentDate || new Date(),
        dueDate: paymentData.dueDate,
        description: paymentData.description,
        notes: paymentData.notes,
        currency: paymentData.currency || 'VND',
        fees,
        netAmount,
        isPartialPayment: paymentData.isPartialPayment || false,
        metadata: paymentData.metadata,
        processedBy: userId
      });

      // Update booking payment status if payment is completed
      if (payment.status === 'completed') {
        await this.updateBookingPaymentStatus(booking, payment);
      }

      logger.info('Payment created successfully', {
        paymentId: payment.id,
        paymentCode: payment.paymentCode,
        bookingId: paymentData.bookingId,
        amount: paymentData.amount,
        createdBy: userId
      });

      return await this.getPaymentById(payment.id);
    } catch (error) {
      logger.error('Create payment failed', {
        error: error.message,
        paymentData,
        userId
      });
      throw error;
    }
  }

  /**
   * Get all payments with pagination and filtering
   */
  static async getAllPayments(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        method,
        customerId,
        bookingId,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        search
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = {};

      // Apply filters
      if (status) {
        whereClause.status = status;
      }

      if (method) {
        whereClause.method = method;
      }

      if (customerId) {
        whereClause.customerId = customerId;
      }

      if (bookingId) {
        whereClause.bookingId = bookingId;
      }

      if (startDate && endDate) {
        whereClause.paymentDate = {
          [Op.between]: [startDate, endDate]
        };
      }

      if (minAmount || maxAmount) {
        whereClause.amount = {};
        if (minAmount) whereClause.amount[Op.gte] = minAmount;
        if (maxAmount) whereClause.amount[Op.lte] = maxAmount;
      }

      if (search) {
        whereClause[Op.or] = [
          {
            paymentCode: {
              [Op.iLike]: `%${search}%`
            }
          },
          {
            transactionId: {
              [Op.iLike]: `%${search}%`
            }
          },
          {
            description: {
              [Op.iLike]: `%${search}%`
            }
          },
          {
            notes: {
              [Op.iLike]: `%${search}%`
            }
          }
        ];
      }

      const { count, rows } = await Payment.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Booking,
            as: 'booking',
            attributes: ['id', 'bookingCode', 'bookingDate', 'startTime', 'status']
          },
          {
            model: Customer,
            as: 'customer',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
              }
            ]
          },
          {
            model: User,
            as: 'processedByUser',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'refundedByUser',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        payments: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          nextPage: page < totalPages ? parseInt(page) + 1 : null,
          prevPage: page > 1 ? parseInt(page) - 1 : null
        }
      };
    } catch (error) {
      logger.error('Get all payments failed', {
        error: error.message,
        options
      });
      throw error;
    }
  }

  /**
   * Get payment by ID
   */
  static async getPaymentById(id) {
    try {
      const payment = await Payment.findByPk(id, {
        include: [
          {
            model: Booking,
            as: 'booking',
            include: [
              {
                model: Service,
                as: 'service',
                attributes: ['id', 'name', 'category', 'duration', 'price']
              },
              {
                model: Branch,
                as: 'branch',
                attributes: ['id', 'name', 'address', 'phone']
              }
            ]
          },
          {
            model: Customer,
            as: 'customer',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
              }
            ]
          },
          {
            model: User,
            as: 'processedByUser',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'refundedByUser',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!payment) {
        throw new AppError('Payment not found', 404, 'PAYMENT_NOT_FOUND');
      }

      return payment;
    } catch (error) {
      logger.error('Get payment by ID failed', {
        error: error.message,
        paymentId: id
      });
      throw error;
    }
  }

  /**
   * Get payment by payment code
   */
  static async getPaymentByCode(paymentCode) {
    try {
      const payment = await Payment.findOne({
        where: { paymentCode },
        include: [
          {
            model: Booking,
            as: 'booking'
          },
          {
            model: Customer,
            as: 'customer',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
              }
            ]
          }
        ]
      });

      if (!payment) {
        throw new AppError('Payment not found', 404, 'PAYMENT_NOT_FOUND');
      }

      return payment;
    } catch (error) {
      logger.error('Get payment by code failed', {
        error: error.message,
        paymentCode
      });
      throw error;
    }
  }

  /**
   * Update payment
   */
  static async updatePayment(id, updateData, userId) {
    try {
      const payment = await this.getPaymentById(id);

      // Check if payment can be updated
      if (['completed', 'refunded'].includes(payment.status)) {
        throw new AppError('Completed or refunded payments cannot be updated', 400, 'PAYMENT_NOT_UPDATABLE');
      }

      // Update payment
      await payment.update({
        ...updateData,
        processedBy: userId,
        processedAt: new Date()
      });

      // Update booking payment status if payment status changed to completed
      if (updateData.status === 'completed' && payment.status !== 'completed') {
        const booking = await Booking.findByPk(payment.bookingId);
        await this.updateBookingPaymentStatus(booking, payment);
      }

      logger.info('Payment updated successfully', {
        paymentId: id,
        updateData,
        updatedBy: userId
      });

      return await this.getPaymentById(id);
    } catch (error) {
      logger.error('Update payment failed', {
        error: error.message,
        paymentId: id,
        updateData,
        userId
      });
      throw error;
    }
  }

  /**
   * Process payment (mark as completed)
   */
  static async processPayment(id, processingData, userId) {
    try {
      const payment = await this.getPaymentById(id);

      if (payment.status !== 'pending') {
        throw new AppError('Only pending payments can be processed', 400, 'PAYMENT_NOT_PROCESSABLE');
      }

      await payment.update({
        status: 'completed',
        transactionId: processingData.transactionId,
        gatewayResponse: processingData.gatewayResponse,
        paymentDate: new Date(),
        processedBy: userId,
        processedAt: new Date(),
        notes: processingData.notes
      });

      // Update booking payment status
      const booking = await Booking.findByPk(payment.bookingId);
      await this.updateBookingPaymentStatus(booking, payment);

      logger.info('Payment processed successfully', {
        paymentId: id,
        processedBy: userId
      });

      return await this.getPaymentById(id);
    } catch (error) {
      logger.error('Process payment failed', {
        error: error.message,
        paymentId: id,
        processingData,
        userId
      });
      throw error;
    }
  }

  /**
   * Update booking payment status based on payments
   */
  static async updateBookingPaymentStatus(booking, payment) {
    try {
      // Get all payments for this booking
      const allPayments = await Payment.findAll({
        where: {
          bookingId: booking.id,
          status: 'completed'
        }
      });

      const totalPaid = allPayments.reduce((sum, p) => sum + parseFloat(p.amount), 0);
      const bookingAmount = parseFloat(booking.finalAmount);

      let paymentStatus = 'pending';
      if (totalPaid >= bookingAmount) {
        paymentStatus = 'paid';
      } else if (totalPaid > 0) {
        paymentStatus = 'partial';
      }

      await booking.update({ paymentStatus });

      logger.info('Booking payment status updated', {
        bookingId: booking.id,
        paymentStatus,
        totalPaid,
        bookingAmount
      });
    } catch (error) {
      logger.error('Update booking payment status failed', {
        error: error.message,
        bookingId: booking.id
      });
      throw error;
    }
  }

  /**
   * Refund payment
   */
  static async refundPayment(id, refundData, userId) {
    try {
      const payment = await this.getPaymentById(id);

      if (!payment.canBeRefunded()) {
        throw new AppError('Payment cannot be refunded', 400, 'PAYMENT_NOT_REFUNDABLE');
      }

      const refundAmount = refundData.amount || payment.getRemainingAmount();

      if (refundAmount > payment.getRemainingAmount()) {
        throw new AppError('Refund amount exceeds remaining amount', 400, 'INVALID_REFUND_AMOUNT');
      }

      const newRefundAmount = parseFloat(payment.refundAmount) + parseFloat(refundAmount);
      const newStatus = newRefundAmount >= parseFloat(payment.amount) ? 'refunded' : 'completed';

      await payment.update({
        refundAmount: newRefundAmount,
        refundReason: refundData.reason,
        refundedAt: new Date(),
        refundedBy: userId,
        status: newStatus
      });

      // Update booking payment status
      const booking = await Booking.findByPk(payment.bookingId);
      await this.updateBookingPaymentStatus(booking, payment);

      logger.info('Payment refunded successfully', {
        paymentId: id,
        refundAmount,
        refundedBy: userId
      });

      return await this.getPaymentById(id);
    } catch (error) {
      logger.error('Refund payment failed', {
        error: error.message,
        paymentId: id,
        refundData,
        userId
      });
      throw error;
    }
  }

  /**
   * Cancel payment
   */
  static async cancelPayment(id, cancellationData, userId) {
    try {
      const payment = await this.getPaymentById(id);

      if (!payment.canBeCancelled()) {
        throw new AppError('Payment cannot be cancelled', 400, 'PAYMENT_NOT_CANCELLABLE');
      }

      await payment.update({
        status: 'cancelled',
        notes: cancellationData.reason,
        processedBy: userId,
        processedAt: new Date()
      });

      logger.info('Payment cancelled successfully', {
        paymentId: id,
        cancelledBy: userId
      });

      return await this.getPaymentById(id);
    } catch (error) {
      logger.error('Cancel payment failed', {
        error: error.message,
        paymentId: id,
        cancellationData,
        userId
      });
      throw error;
    }
  }

  /**
   * Get customer payment history
   */
  static async getCustomerPaymentHistory(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        method,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        search
      } = options;

      const offset = (page - 1) * limit;

      // First, get the customer record for this user
      const customer = await Customer.findOne({
        where: { userId },
        attributes: ['id']
      });

      if (!customer) {
        throw new AppError('Customer profile not found', 404, 'CUSTOMER_NOT_FOUND');
      }

      // Build where clause for customer's payments
      const whereClause = {
        customerId: customer.id,
        isActive: true
      };

      // Add filters
      if (status) {
        whereClause.status = status;
      }

      if (method) {
        whereClause.method = method;
      }

      if (startDate && endDate) {
        whereClause.paymentDate = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        };
      } else if (startDate) {
        whereClause.paymentDate = {
          [Op.gte]: new Date(startDate)
        };
      } else if (endDate) {
        whereClause.paymentDate = {
          [Op.lte]: new Date(endDate)
        };
      }

      if (minAmount || maxAmount) {
        whereClause.amount = {};
        if (minAmount) whereClause.amount[Op.gte] = parseFloat(minAmount);
        if (maxAmount) whereClause.amount[Op.lte] = parseFloat(maxAmount);
      }

      // Search functionality
      if (search) {
        whereClause[Op.or] = [
          { paymentCode: { [Op.iLike]: `%${search}%` } },
          { transactionId: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      const { count, rows } = await Payment.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Booking,
            as: 'booking',
            attributes: ['id', 'bookingCode', 'bookingDate', 'startTime', 'status'],
            include: [
              {
                model: Service,
                as: 'service',
                attributes: ['id', 'name', 'description']
              },
              {
                model: Branch,
                as: 'branch',
                attributes: ['id', 'name', 'address']
              }
            ]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      const totalPages = Math.ceil(count / limit);

      return {
        payments: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      logger.error('Get customer payment history failed', {
        error: error.message,
        userId,
        options
      });
      throw error;
    }
  }

  /**
   * Get payment statistics
   */
  static async getPaymentStats(options = {}) {
    try {
      const { startDate, endDate, method, customerId } = options;
      const whereClause = {};

      if (startDate && endDate) {
        whereClause.paymentDate = {
          [Op.between]: [startDate, endDate]
        };
      }

      if (method) {
        whereClause.method = method;
      }

      if (customerId) {
        whereClause.customerId = customerId;
      }

      const [
        totalPayments,
        pendingPayments,
        completedPayments,
        failedPayments,
        refundedPayments,
        totalRevenue,
        totalRefunds,
        totalFees
      ] = await Promise.all([
        Payment.count({ where: whereClause }),
        Payment.count({ where: { ...whereClause, status: 'pending' } }),
        Payment.count({ where: { ...whereClause, status: 'completed' } }),
        Payment.count({ where: { ...whereClause, status: 'failed' } }),
        Payment.count({ where: { ...whereClause, status: 'refunded' } }),
        Payment.sum('amount', { where: { ...whereClause, status: 'completed' } }),
        Payment.sum('refundAmount', { where: whereClause }),
        Payment.sum('fees', { where: { ...whereClause, status: 'completed' } })
      ]);

      // Get payment method breakdown
      const methodBreakdown = await Payment.findAll({
        where: { ...whereClause, status: 'completed' },
        attributes: [
          'method',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('SUM', sequelize.col('amount')), 'total']
        ],
        group: ['method']
      });

      return {
        totalPayments,
        pendingPayments,
        completedPayments,
        failedPayments,
        refundedPayments,
        totalRevenue: totalRevenue || 0,
        totalRefunds: totalRefunds || 0,
        totalFees: totalFees || 0,
        netRevenue: (totalRevenue || 0) - (totalRefunds || 0) - (totalFees || 0),
        successRate: totalPayments > 0 ? (completedPayments / totalPayments * 100).toFixed(2) : 0,
        methodBreakdown: methodBreakdown.map(item => ({
          method: item.method,
          count: parseInt(item.dataValues.count),
          total: parseFloat(item.dataValues.total || 0)
        }))
      };
    } catch (error) {
      logger.error('Get payment stats failed', {
        error: error.message,
        options
      });
      throw error;
    }
  }

  /**
   * Get payments by booking ID
   */
  static async getPaymentsByBookingId(bookingId) {
    try {
      const payments = await Payment.findAll({
        where: { bookingId },
        include: [
          {
            model: User,
            as: 'processedByUser',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'refundedByUser',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [['createdAt', 'DESC']]
      });

      return payments;
    } catch (error) {
      logger.error('Get payments by booking ID failed', {
        error: error.message,
        bookingId
      });
      throw error;
    }
  }

  /**
   * Delete payment (soft delete)
   */
  static async deletePayment(id, userId) {
    try {
      const payment = await this.getPaymentById(id);

      if (['completed', 'refunded'].includes(payment.status)) {
        throw new AppError('Cannot delete completed or refunded payments', 400, 'PAYMENT_NOT_DELETABLE');
      }

      await payment.destroy();

      logger.info('Payment deleted successfully', {
        paymentId: id,
        deletedBy: userId
      });

      return { message: 'Payment deleted successfully' };
    } catch (error) {
      logger.error('Delete payment failed', {
        error: error.message,
        paymentId: id,
        userId
      });
      throw error;
    }
  }
}

module.exports = PaymentService;
