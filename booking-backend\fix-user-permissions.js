/**
 * Fix User Permissions Script
 * Updates the admin user to be manager of a branch so they can create forms
 */

const { sequelize } = require('./src/database/connection');
const { users, branches, services } = require('./src/database/models');

async function fixUserPermissions() {
  try {
    console.log('🔧 Fixing user permissions for forms creation...\n');

    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Find our admin user
    const adminUser = await users.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }

    console.log('✅ Admin user found:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Name: ${adminUser.name}`);

    // Find a branch to assign or create one
    let targetBranch = await branches.findOne({
      where: { is_active: true }
    });

    if (!targetBranch) {
      // Create a new branch
      targetBranch = await branches.create({
        name: 'Admin Test Branch',
        address: '123 Test Street',
        phone: '0123456789',
        city: 'Test City',
        manager_id: adminUser.id,
        is_active: true
      });
      console.log('✅ Created new branch:', targetBranch.name);
    } else {
      // Update existing branch to have our admin as manager
      await targetBranch.update({ manager_id: adminUser.id });
      console.log('✅ Updated branch manager:', targetBranch.name);
    }

    // Check if there's a service for this branch
    let targetService = await services.findOne({
      where: { branch_id: targetBranch.id, is_active: true }
    });

    if (!targetService) {      // Create a service for this branch
      targetService = await services.create({
        name: 'Admin Test Service',
        description: 'Test service for admin user',
        category: 'massage',
        price: 100000,
        duration: 60,
        branch_id: targetBranch.id,
        is_active: true
      });
      console.log('✅ Created new service:', targetService.name);
    }

    console.log('\n🎯 Permissions fixed! You can now use:');
    console.log(`   serviceId: ${targetService.id}`);
    console.log(`   branchId: ${targetBranch.id}`);
    console.log(`   User: ${adminUser.name} (ID: ${adminUser.id})`);

    // Test form creation
    console.log('\n🧪 Testing form creation...');
    
    // We'll need to import the FormsService and simulate the creation
    const FormsService = require('./src/modules/forms/service');
    
    const formData = {
      name: 'Auto-Generated Test Form',
      serviceId: targetService.id,
      branchId: targetBranch.id,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };

    const createdForm = await FormsService.createForm(formData, adminUser.id);
    
    console.log('🎉 SUCCESS! Form created via service:');
    console.log(`   Form ID: ${createdForm.id}`);
    console.log(`   Form Name: ${createdForm.name}`);
    console.log(`   Form Slug: ${createdForm.slug}`);
    
    // Test URL generation
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4000';
    const publicUrl = `${frontendUrl}/book/${createdForm.slug}`;
    const embedCode = `<iframe src="${frontendUrl}/book/${createdForm.slug}" width="100%" height="600" frameborder="0" style="border: none; border-radius: 8px;" allowtransparency="true" loading="lazy"></iframe>`;
    
    console.log('\n🔗 Generated URLs:');
    console.log(`   Public URL: ${publicUrl}`);
    console.log(`   Embed Code: ${embedCode.substring(0, 100)}...`);

  } catch (error) {
    console.error('❌ Error fixing permissions:', error.message);
    console.error(error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// Run the fix
fixUserPermissions();
