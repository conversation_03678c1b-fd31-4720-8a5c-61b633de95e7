const axios = require('axios');

// Test forms API endpoint
async function testFormsAPI() {
  const BASE_URL = 'http://localhost:3000';
  
  console.log('Testing Forms API...\n');
  
  try {
    // 1. First login to get a token    console.log('1. Attempting login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: '11111111'
    });
    
    if (!loginResponse.data.success) {
      console.error('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful, token received');
    console.log('Token:', token.substring(0, 50) + '...\n');
    
    // 2. Test forms creation
    console.log('2. Testing form creation...');    const formData = {
      name: 'Test Booking Form - ' + Date.now(),
      serviceId: 9,
      branchId: 5,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: null
      }
    };
    
    const createResponse = await axios.post(`${BASE_URL}/api/forms`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Form creation response status:', createResponse.status);
    console.log('Response data:', JSON.stringify(createResponse.data, null, 2));
    
    // Check if publicUrl and embedCode are present
    const formResponse = createResponse.data.data;
    if (formResponse.publicUrl) {
      console.log('✅ publicUrl is present:', formResponse.publicUrl);
    } else {
      console.log('❌ publicUrl is missing');
    }
    
    if (formResponse.embedCode) {
      console.log('✅ embedCode is present:', formResponse.embedCode.substring(0, 100) + '...');
    } else {
      console.log('❌ embedCode is missing');  
    }
    
    // Check environment variable
    console.log('\n3. Environment check:');
    console.log('FRONTEND_URL from process.env:', process.env.FRONTEND_URL);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    }
  }
}

testFormsAPI();
