/**
 * Test Token Validation
 * Test if the token validation logic works correctly
 */

const axios = require('axios');

async function testTokenValidation() {
  console.log('🧪 Testing Token Validation...\n');
  
  try {
    // Step 1: Login to get real tokens
    console.log('1️⃣ Logging in to get real tokens...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      console.log('📦 Full response:', JSON.stringify(loginResponse.data, null, 2));

      // Backend returns 'token' not 'accessToken'
      const { token: accessToken, refreshToken, user } = loginResponse.data.data;
      
      console.log('📦 Received tokens:');
      console.log('- Access Token Length:', accessToken.length);
      console.log('- Refresh Token Length:', refreshToken.length);
      console.log('- User:', user.email);
      
      // Step 2: Test token validation logic
      console.log('\n2️⃣ Testing token validation logic...');
      
      // Simulate tokenManager.isTokenValid()
      function testTokenValid(token) {
        if (!token) {
          console.log('❌ No token provided');
          return false;
        }
        
        try {
          // Basic JWT format check (header.payload.signature)
          const parts = token.split('.');
          if (parts.length !== 3) {
            console.log('❌ Invalid JWT format, parts:', parts.length);
            return false;
          }
          
          console.log('✅ JWT format valid');
          
          // Decode payload to check expiry
          const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
          const currentTime = Math.floor(Date.now() / 1000);
          
          console.log('📋 Token payload:', {
            id: payload.id,
            email: payload.email,
            role: payload.role,
            iat: payload.iat,
            exp: payload.exp,
            issuer: payload.iss,
            audience: payload.aud
          });
          
          console.log('⏰ Time check:', {
            currentTime: currentTime,
            tokenExpiry: payload.exp,
            timeUntilExpiry: payload.exp ? (payload.exp - currentTime) : 'N/A',
            isExpired: payload.exp ? (payload.exp <= currentTime) : 'N/A',
            isValidWithBuffer: payload.exp ? (payload.exp > (currentTime + 30)) : false
          });
          
          // Check if token is expired (with 30 second buffer)
          const isValid = payload.exp && payload.exp > (currentTime + 30);
          console.log('🎯 Token validation result:', isValid);
          return isValid;
        } catch (error) {
          console.error('❌ Token validation error:', error.message);
          return false;
        }
      }
      
      const isTokenValid = testTokenValid(accessToken);
      
      // Step 3: Test authService.isAuthenticated() logic
      console.log('\n3️⃣ Testing authService.isAuthenticated() logic...');
      
      function testIsAuthenticated(token, user) {
        const hasToken = !!token;
        const hasUser = !!user;
        const isTokenValid = testTokenValid(token);
        
        console.log('🔍 Authentication check:', {
          hasToken,
          hasUser,
          isTokenValid
        });
        
        const result = hasToken && hasUser && isTokenValid;
        console.log('🎯 isAuthenticated() result:', result);
        return result;
      }
      
      const isAuthenticated = testIsAuthenticated(accessToken, user);
      
      // Step 4: Test with expired token
      console.log('\n4️⃣ Testing with manually expired token...');
      
      // Create a token with past expiry
      const expiredTokenParts = accessToken.split('.');
      const expiredPayload = JSON.parse(Buffer.from(expiredTokenParts[1], 'base64').toString());
      expiredPayload.exp = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
      
      const expiredPayloadEncoded = Buffer.from(JSON.stringify(expiredPayload)).toString('base64');
      const expiredToken = `${expiredTokenParts[0]}.${expiredPayloadEncoded}.${expiredTokenParts[2]}`;
      
      const isExpiredTokenValid = testTokenValid(expiredToken);
      const isExpiredAuthenticated = testIsAuthenticated(expiredToken, user);
      
      // Summary
      console.log('\n📊 Summary:');
      console.log('- Valid token validation:', isTokenValid ? '✅' : '❌');
      console.log('- Valid authentication:', isAuthenticated ? '✅' : '❌');
      console.log('- Expired token validation:', isExpiredTokenValid ? '❌ (should be false)' : '✅');
      console.log('- Expired authentication:', isExpiredAuthenticated ? '❌ (should be false)' : '✅');
      
      if (isAuthenticated) {
        console.log('\n🎉 Token validation is working correctly!');
        console.log('The issue might be elsewhere in the authentication flow.');
      } else {
        console.log('\n⚠️ Token validation failed!');
        console.log('This explains why authentication persistence is not working.');
      }
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Cannot connect to backend server at http://localhost:3000');
      console.log('Please make sure the backend server is running.');
    } else {
      console.log('❌ Error:', error.response?.data?.message || error.message);
    }
  }
}

// Run the test
testTokenValidation().catch(console.error);
