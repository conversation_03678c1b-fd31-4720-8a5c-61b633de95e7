'use client';

import { useState } from 'react';
import { 
  Copy, 
  Check, 
  LinkIcon, 
  Code, 
  Share2,
  Download,
  ExternalLink
} from 'lucide-react';

/**
 * Enhanced Form Sharing Component
 * Provides comprehensive sharing options for booking forms
 */
export default function FormSharingModal({ form, isOpen, onClose }) {
  const [copiedItem, setCopiedItem] = useState(null);
  const [activeTab, setActiveTab] = useState('links');

  const handleCopy = async (text, itemId) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(itemId);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const openSocialShare = (url) => {
    window.open(url, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
  };

  if (!isOpen || !form) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-neutral-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-text">Share Your Booking Form</h2>
              <p className="text-sm text-neutral-600 mt-1">{form.name}</p>
            </div>
            <button
              onClick={onClose}
              className="text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-neutral-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('links')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'links'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-neutral-500 hover:text-neutral-700'
              }`}
            >
              🔗 Direct Links
            </button>
            <button
              onClick={() => setActiveTab('embed')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'embed'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-neutral-500 hover:text-neutral-700'
              }`}
            >
              📋 Embed Codes
            </button>
            <button
              onClick={() => setActiveTab('social')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'social'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-neutral-500 hover:text-neutral-700'
              }`}
            >
              📱 Social & Mobile
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Direct Links Tab */}
          {activeTab === 'links' && (
            <div className="space-y-6">
              {/* Primary Link */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-2">
                  Primary Booking Link
                </label>
                <div className="flex">
                  <input
                    type="text"
                    value={form.publicUrl}
                    readOnly
                    className="flex-1 form-input rounded-r-none"
                  />
                  <button
                    onClick={() => handleCopy(form.publicUrl, 'primary-link')}
                    className="px-4 py-2 bg-primary-600 text-white rounded-r-md hover:bg-primary-700 transition-colors"
                  >
                    {copiedItem === 'primary-link' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </button>
                </div>
                <p className="text-xs text-neutral-500 mt-1">Perfect for email, SMS, and general sharing</p>
              </div>

              {/* Tracking Link */}
              {form.trackingUrl && (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Tracking Link (with analytics)
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={form.trackingUrl}
                      readOnly
                      className="flex-1 form-input rounded-r-none"
                    />
                    <button
                      onClick={() => handleCopy(form.trackingUrl, 'tracking-link')}
                      className="px-4 py-2 bg-secondary-600 text-white rounded-r-md hover:bg-secondary-700 transition-colors"
                    >
                      {copiedItem === 'tracking-link' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">Includes tracking parameters for analytics</p>
                </div>
              )}

              {/* UTM Link */}
              {form.utmUrl && (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    UTM Campaign Link
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={form.utmUrl}
                      readOnly
                      className="flex-1 form-input rounded-r-none"
                    />
                    <button
                      onClick={() => handleCopy(form.utmUrl, 'utm-link')}
                      className="px-4 py-2 bg-accent-600 text-white rounded-r-md hover:bg-accent-700 transition-colors"
                    >
                      {copiedItem === 'utm-link' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </button>
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">For Google Analytics campaign tracking</p>
                </div>
              )}
            </div>
          )}

          {/* Embed Codes Tab */}
          {activeTab === 'embed' && form.embedCodes && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Standard Embed */}
                <div className="border border-neutral-200 rounded-lg p-4">
                  <h3 className="font-medium text-neutral-800 mb-2">Standard Embed</h3>
                  <p className="text-sm text-neutral-600 mb-3">600px height, perfect for most websites</p>
                  <textarea
                    value={form.embedCodes.standard}
                    readOnly
                    className="w-full h-24 text-xs font-mono border border-neutral-300 rounded p-2 resize-none"
                  />
                  <button
                    onClick={() => handleCopy(form.embedCodes.standard, 'standard-embed')}
                    className="btn-outline btn-sm mt-2 w-full"
                  >
                    {copiedItem === 'standard-embed' ? 'Copied!' : 'Copy Code'}
                  </button>
                </div>

                {/* Compact Embed */}
                <div className="border border-neutral-200 rounded-lg p-4">
                  <h3 className="font-medium text-neutral-800 mb-2">Compact Embed</h3>
                  <p className="text-sm text-neutral-600 mb-3">400px height, great for sidebars</p>
                  <textarea
                    value={form.embedCodes.compact}
                    readOnly
                    className="w-full h-24 text-xs font-mono border border-neutral-300 rounded p-2 resize-none"
                  />
                  <button
                    onClick={() => handleCopy(form.embedCodes.compact, 'compact-embed')}
                    className="btn-outline btn-sm mt-2 w-full"
                  >
                    {copiedItem === 'compact-embed' ? 'Copied!' : 'Copy Code'}
                  </button>
                </div>

                {/* Mobile Optimized */}
                <div className="border border-neutral-200 rounded-lg p-4">
                  <h3 className="font-medium text-neutral-800 mb-2">Mobile Optimized</h3>
                  <p className="text-sm text-neutral-600 mb-3">Responsive design for mobile devices</p>
                  <textarea
                    value={form.embedCodes.mobile}
                    readOnly
                    className="w-full h-24 text-xs font-mono border border-neutral-300 rounded p-2 resize-none"
                  />
                  <button
                    onClick={() => handleCopy(form.embedCodes.mobile, 'mobile-embed')}
                    className="btn-outline btn-sm mt-2 w-full"
                  >
                    {copiedItem === 'mobile-embed' ? 'Copied!' : 'Copy Code'}
                  </button>
                </div>

                {/* Popup Button */}
                <div className="border border-neutral-200 rounded-lg p-4">
                  <h3 className="font-medium text-neutral-800 mb-2">Popup Button</h3>
                  <p className="text-sm text-neutral-600 mb-3">Opens form in popup window</p>
                  <textarea
                    value={form.embedCodes.button}
                    readOnly
                    className="w-full h-24 text-xs font-mono border border-neutral-300 rounded p-2 resize-none"
                  />
                  <button
                    onClick={() => handleCopy(form.embedCodes.button, 'button-embed')}
                    className="btn-outline btn-sm mt-2 w-full"
                  >
                    {copiedItem === 'button-embed' ? 'Copied!' : 'Copy Code'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Social & Mobile Tab */}
          {activeTab === 'social' && (
            <div className="space-y-6">
              {/* QR Code */}
              {form.qrCodeUrl && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-medium text-purple-800">QR Code</h3>
                      <p className="text-sm text-purple-600">Let customers scan to book instantly</p>
                    </div>
                    <button
                      onClick={() => window.open(form.qrCodeUrl, '_blank')}
                      className="btn-outline text-purple-600 border-purple-300"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View QR Code
                    </button>
                  </div>
                </div>
              )}

              {/* Social Media Sharing */}
              {form.socialUrls && (
                <div>
                  <h3 className="font-medium text-neutral-800 mb-4">Social Media Sharing</h3>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                    <button
                      onClick={() => openSocialShare(form.socialUrls.facebook)}
                      className="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white mb-2">
                        f
                      </div>
                      <span className="text-sm font-medium text-blue-800">Facebook</span>
                    </button>

                    <button
                      onClick={() => openSocialShare(form.socialUrls.twitter)}
                      className="flex flex-col items-center p-4 bg-sky-50 border border-sky-200 rounded-lg hover:bg-sky-100 transition-colors"
                    >
                      <div className="w-10 h-10 bg-sky-500 rounded-lg flex items-center justify-center text-white mb-2">
                        🐦
                      </div>
                      <span className="text-sm font-medium text-sky-800">Twitter</span>
                    </button>

                    <button
                      onClick={() => openSocialShare(form.socialUrls.linkedin)}
                      className="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      <div className="w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center text-white mb-2">
                        in
                      </div>
                      <span className="text-sm font-medium text-blue-900">LinkedIn</span>
                    </button>

                    <button
                      onClick={() => openSocialShare(form.socialUrls.whatsapp)}
                      className="flex flex-col items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                    >
                      <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center text-white mb-2">
                        💬
                      </div>
                      <span className="text-sm font-medium text-green-800">WhatsApp</span>
                    </button>

                    <button
                      onClick={() => openSocialShare(form.socialUrls.telegram)}
                      className="flex flex-col items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white mb-2">
                        ✈️
                      </div>
                      <span className="text-sm font-medium text-blue-700">Telegram</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Social Content Preview */}
              {form.socialContent && (
                <div className="bg-neutral-50 border border-neutral-200 rounded-lg p-4">
                  <h3 className="font-medium text-neutral-800 mb-3">Social Media Preview</h3>
                  <div className="space-y-2">
                    <div>
                      <strong className="text-sm text-neutral-700">Title:</strong>
                      <p className="text-sm text-neutral-600">{form.socialContent.title}</p>
                    </div>
                    <div>
                      <strong className="text-sm text-neutral-700">Description:</strong>
                      <p className="text-sm text-neutral-600">{form.socialContent.description}</p>
                    </div>
                    <div>
                      <strong className="text-sm text-neutral-700">Hashtags:</strong>
                      <p className="text-sm text-neutral-600">#{form.socialContent.hashtags?.join(' #')}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-neutral-200 bg-neutral-50">
          <div className="flex justify-between items-center">
            <p className="text-sm text-neutral-600">
              📊 Track your form's performance in the analytics dashboard
            </p>
            <button
              onClick={onClose}
              className="btn-primary"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
