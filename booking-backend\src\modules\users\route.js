/**
 * Users Routes
 * API routes for user management
 */

const express = require('express');
const router = express.Router();
const UsersController = require('./controller');
const { authenticate, authorize } = require('../../middleware/auth');
const { validateRequest, validatePagination, validateIdParam } = require('../../middleware/validation');
const { adminLimiter } = require('../../middleware/rateLimiter');
const { body, query, param } = require('express-validator');

// Validation rules
const createUserValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('Password must be between 6 and 128 characters'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('phone')
    .optional()
    .matches(/^(0|\+84)[0-9]{9,10}$/)
    .withMessage('Valid Vietnamese phone number required'),
  body('role')
    .isIn(['admin', 'staff', 'customer'])
    .withMessage('Role must be admin, staff, or customer'),
  body('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Valid date of birth required'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other')
];

const updateUserValidation = [
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email required'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('phone')
    .optional()
    .matches(/^(0|\+84)[0-9]{9,10}$/)
    .withMessage('Valid Vietnamese phone number required'),
  body('role')
    .optional()
    .isIn(['admin', 'staff', 'customer'])
    .withMessage('Role must be admin, staff, or customer'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Valid date of birth required'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other')
];

const toggleStatusValidation = [
  body('isActive')
    .isBoolean()
    .withMessage('isActive is required and must be a boolean')
];

const resetPasswordValidation = [
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('New password must be between 6 and 128 characters')
];

const searchValidation = [
  query('q')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

const getUsersValidation = [
  query('role')
    .optional()
    .isIn(['admin', 'staff', 'customer'])
    .withMessage('Invalid role filter'),
  query('isActive')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isActive must be true or false'),
  query('branchId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid branch ID required'),
  query('search')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters')
];

// All routes require authentication
router.use(authenticate);

/**
 * @route   GET /api/users
 * @desc    Get all users with pagination and filters
 * @access  Private (Admin, Staff)
 */
router.get('/', 
  authorize(['admin', 'staff']),
  getUsersValidation,
  validatePagination,
  validateRequest,
  UsersController.getUsers
);

/**
 * @route   GET /api/users/stats
 * @desc    Get user statistics
 * @access  Private (Admin)
 */
router.get('/stats', 
  authorize(['admin']),
  UsersController.getUserStats
);

/**
 * @route   GET /api/users/search
 * @desc    Search users
 * @access  Private (Admin, Staff)
 */
router.get('/search', 
  authorize(['admin', 'staff']),
  searchValidation,
  validateRequest,
  UsersController.searchUsers
);

/**
 * @route   GET /api/users/role/:role
 * @desc    Get users by role
 * @access  Private (Admin, Staff)
 */
router.get('/role/:role', 
  authorize(['admin', 'staff']),
  param('role')
    .isIn(['admin', 'staff', 'customer'])
    .withMessage('Invalid role'),
  validateRequest,
  UsersController.getUsersByRole
);

/**
 * @route   GET /api/users/profile
 * @desc    Get current user profile (same as /auth/profile but under users)
 * @access  Private
 */
router.get('/profile',
  authenticate,
  UsersController.getCurrentUserProfile
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private (Admin, Staff)
 */
router.get('/:id',
  authorize(['admin', 'staff']),
  validateIdParam(),
  validateRequest,
  UsersController.getUserById
);

/**
 * @route   POST /api/users
 * @desc    Create new user
 * @access  Private (Admin)
 */
router.post('/', 
  authorize(['admin']),
  adminLimiter,
  createUserValidation,
  validateRequest,
  UsersController.createUser
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private (Admin)
 */
router.put('/:id', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  updateUserValidation,
  validateRequest,
  UsersController.updateUser
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user (soft delete)
 * @access  Private (Admin)
 */
router.delete('/:id', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  UsersController.deleteUser
);

/**
 * @route   PATCH /api/users/:id/status
 * @desc    Toggle user status (activate/deactivate)
 * @access  Private (Admin)
 */
router.patch('/:id/status', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  toggleStatusValidation,
  validateRequest,
  UsersController.toggleUserStatus
);

/**
 * @route   POST /api/users/:id/reset-password
 * @desc    Reset user password
 * @access  Private (Admin)
 */
router.post('/:id/reset-password', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  resetPasswordValidation,
  validateRequest,
  UsersController.resetUserPassword
);

/**
 * @route   POST /api/users/:id/unlock
 * @desc    Unlock user account
 * @access  Private (Admin)
 */
router.post('/:id/unlock', 
  authorize(['admin']),
  adminLimiter,
  validateIdParam(),
  validateRequest,
  UsersController.unlockUser
);

module.exports = router;
