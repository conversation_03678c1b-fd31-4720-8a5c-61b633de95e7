/**
 * Check database table structures
 */

const { sequelize } = require('./src/database/connection');

async function checkTables() {
  console.log('🔍 Checking database table structures...');
  
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected');

    // Check bookings table structure
    const [bookingColumns] = await sequelize.query('DESCRIBE bookings');
    console.log('\n📅 Bookings table structure:');
    bookingColumns.forEach(col => {
      console.log(`  ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check branches table structure
    const [branchColumns] = await sequelize.query('DESCRIBE branches');
    console.log('\n📋 Branches table structure:');
    branchColumns.forEach(col => {
      console.log(`  ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check services table structure
    const [serviceColumns] = await sequelize.query('DESCRIBE services');
    console.log('\n🔧 Services table structure:');
    serviceColumns.forEach(col => {
      console.log(`  ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

    // Check forms table structure
    const [formColumns] = await sequelize.query('DESCRIBE forms');
    console.log('\n📝 Forms table structure:');
    formColumns.forEach(col => {
      console.log(`  ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

// Run check
checkTables();
