# Technical Documentation - Spa Booking System

## Technology Stack Overview

### Core Technologies
- **Runtime**: Node.js 20.x LTS
- **Framework**: NestJS 10.x (Latest stable)
- **Language**: TypeScript 5.x
- **Database**: MySQL 8.0
- **ORM**: TypeORM 0.3.x
- **Cache**: Redis 7.x
- **Queue**: Bull (Redis-based)
- **API Style**: RESTful + WebSocket

### Key Dependencies
```json
{
  "@nestjs/common": "^10.0.0",
  "@nestjs/core": "^10.0.0",
  "@nestjs/platform-express": "^10.0.0",
  "@nestjs/typeorm": "^10.0.0",
  "@nestjs/jwt": "^10.0.0",
  "@nestjs/passport": "^10.0.0",
  "@nestjs/swagger": "^7.0.0",
  "@nestjs/bull": "^10.0.0",
  "@nestjs/schedule": "^4.0.0",
  "@nestjs/platform-socket.io": "^10.0.0",
  "typeorm": "^0.3.17",
  "mysql2": "^3.6.0",
  "bcrypt": "^5.1.0",
  "class-validator": "^0.14.0",
  "class-transformer": "^0.5.1",
  "passport": "^0.6.0",
  "passport-jwt": "^4.0.1",
  "passport-local": "^1.0.0",
  "bull": "^4.11.0",
  "ioredis": "^5.3.0",
  "nodemailer": "^6.9.0",
  "twilio": "^4.19.0",
  "stripe": "^14.0.0",
  "moment-timezone": "^0.5.43"
}
Project Structure
javascript


spa-booking-system/
├── src/
│   ├── main.ts                      # Application entry point
│   ├── app.module.ts                # Root module
│   ├── common/                      # Shared resources
│   │   ├── constants/
│   │   │   └── app.constants.ts
│   │   ├── decorators/
│   │   │   ├── roles.decorator.ts
│   │   │   └── user.decorator.ts
│   │   ├── dto/
│   │   │   └── pagination.dto.ts
│   │   ├── entities/
│   │   │   └── base.entity.ts
│   │   ├── enums/
│   │   │   ├── booking-status.enum.ts
│   │   │   ├── payment-status.enum.ts
│   │   │   └── user-role.enum.ts
│   │   ├── exceptions/
│   │   │   └── business.exception.ts
│   │   ├── filters/
│   │   │   └── http-exception.filter.ts
│   │   ├── guards/
│   │   │   ├── jwt-auth.guard.ts
│   │   │   └── roles.guard.ts
│   │   ├── interceptors/
│   │   │   ├── logging.interceptor.ts
│   │   │   └── transform.interceptor.ts
│   │   ├── pipes/
│   │   │   └── validation.pipe.ts
│   │   └── utils/
│   │       ├── date.utils.ts
│   │       └── string.utils.ts
│   ├── config/                      # Configuration files
│   │   ├── app.config.ts
│   │   ├── database.config.ts
│   │   ├── jwt.config.ts
│   │   ├── redis.config.ts
│   │   └── mail.config.ts
│   ├── database/                    # Database related
│   │   ├── migrations/
│   │   └── seeds/
│   └── modules/                     # Feature modules
│       ├── auth/
│       │   ├── auth.module.ts
│       │   ├── auth.controller.ts
│       │   ├── auth.service.ts
│       │   ├── dto/
│       │   │   ├── login.dto.ts
│       │   │   ├── register.dto.ts
│       │   │   └── refresh-token.dto.ts
│       │   ├── entities/
│       │   │   └── refresh-token.entity.ts
│       │   ├── guards/
│       │   │   └── local-auth.guard.ts
│       │   └── strategies/
│       │       ├── jwt.strategy.ts
│       │       └── local.strategy.ts
│       ├── users/
│       │   ├── users.module.ts
│       │   ├── users.controller.ts
│       │   ├── users.service.ts
│       │   ├── dto/
│       │   │   ├── create-user.dto.ts
│       │   │   └── update-user.dto.ts
│       │   └── entities/
│       │       └── user.entity.ts
│       ├── branches/
│       ├── services/
│       ├── employees/
│       ├── customers/
│       ├── bookings/
│       ├── payments/
│       ├── notifications/
│       ├── settings/
│       └── reports/
├── test/                            # Test files
├── dist/                            # Compiled output
├── node_modules/
├── .env.example
├── .env.development
├── .env.production
├── .gitignore
├── nest-cli.json
├── package.json
├── tsconfig.json
├── README.md
└── docker-compose.yml
Module Implementation Details
1. Auth Module
Purpose
Handle authentication, authorization, and session management.
Key Components
typescript


// auth.module.ts
@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
API Endpoints
typescript


// auth.controller.ts
@Controller('api/auth')
export class AuthController {
  @Post('login')
  @UseGuards(LocalAuthGuard)
  async login(@Request() req, @Body() loginDto: LoginDto);
@Post('register') async register(@Body() registerDto: RegisterDto);
@Post('refresh') @UseGuards(JwtAuthGuard) async refreshToken(@Body() refreshTokenDto: RefreshTokenDto);
@Post('logout') @UseGuards(JwtAuthGuard) async logout(@Request() req);
@Get('profile') @UseGuards(JwtAuthGuard) async getProfile(@Request() req);
@Post('forgot-password') async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto);
@Post('reset-password') async resetPassword(@Body() resetPasswordDto: ResetPasswordDto);
@Post('change-password') @UseGuards(JwtAuthGuard) async changePassword(@Request() req, @Body() changePasswordDto: ChangePasswordDto); }
javascript



#### DTOs
```typescript
// login.dto.ts
export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;
@IsString() @IsNotEmpty() @MinLength(6) password: string; }
// register.dto.ts export class RegisterDto { @IsString() @IsNotEmpty() name: string;
@IsEmail() @IsNotEmpty() email: string;
@IsString() @IsNotEmpty() @MinLength(6) password: string;
@IsString() @IsNotEmpty() @Matches(/^[0-9]{10,11}$/) phone: string; }
javascript



### 2. Bookings Module

#### Purpose
Core booking functionality including availability checking, scheduling, and management.

#### Key Components
```typescript
// bookings.module.ts
@Module({
  imports: [
    TypeOrmModule.forFeature([Booking, Service, Employee, Customer]),
    EmployeesModule,
    ServicesModule,
    CustomersModule,
    NotificationsModule,
    BullModule.registerQueue({
      name: 'booking-notifications',
    }),
  ],
  controllers: [BookingsController],
  providers: [
    BookingsService,
    BookingAvailabilityService,
    BookingValidationService,
    BookingNotificationProcessor,
  ],
  exports: [BookingsService],
})
export class BookingsModule {}
Service Implementation
typescript


// bookings.service.ts
@Injectable()
export class BookingsService {
  constructor(
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    private availabilityService: BookingAvailabilityService,
    private validationService: BookingValidationService,
    @InjectQueue('booking-notifications')
    private notificationQueue: Queue,
  ) {}
async createBooking(createBookingDto: CreateBookingDto): Promise<Booking> { // 1. Validate booking data await this.validationService.validateBooking(createBookingDto);
javascript


// 2. Check availability
const isAvailable = await this.availabilityService.checkAvailability(
  createBookingDto.employeeId,
  createBookingDto.bookingDate,
  createBookingDto.startTime,
  createBookingDto.serviceId,
);

if (!isAvailable) {
  throw new ConflictException('Time slot is not available');
}

// 3. Create booking
const booking = this.bookingRepository.create({
  ...createBookingDto,
  bookingCode: this.generateBookingCode(),
  status: BookingStatus.PENDING,
});

const savedBooking = await this.bookingRepository.save(booking);

// 4. Queue notifications
await this.notificationQueue.add('confirmation', {
  bookingId: savedBooking.id,
  type: 'booking_confirmation',
});

// 5. Schedule reminder
const reminderTime = moment(booking.bookingDate)
  .subtract(24, 'hours')
  .toDate();
  
await this.notificationQueue.add(
  'reminder',
  { bookingId: savedBooking.id },
  { delay: reminderTime.getTime() - Date.now() }
);

return savedBooking;
}
async getAvailableSlots( employeeId: number, date: string, serviceId: number, ): Promise<TimeSlot[]> { return this.availabilityService.getAvailableSlots( employeeId, date, serviceId, ); }
private generateBookingCode(): string { const date = moment().format('YYYYMMDD'); const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0'); return BK${date}${random}; } }
javascript



### 3. Employees Module

#### Schedule Management
```typescript
// employee-schedule.service.ts
@Injectable()
export class EmployeeScheduleService {
  async getEmployeeAvailability(
    employeeId: number,
    date: string,
  ): Promise<AvailabilitySlot[]> {
    // 1. Get employee schedule for the day
    const dayOfWeek = moment(date).day();
    const schedule = await this.getScheduleForDay(employeeId, dayOfWeek);
    
    if (!schedule || !schedule.isActive) {
      return [];
    }
    
    // 2. Get time offs for the date
    const timeOffs = await this.getTimeOffs(employeeId, date);
    
    // 3. Get existing bookings
    const bookings = await this.getBookings(employeeId, date);
    
    // 4. Generate available slots
    const slots = this.generateTimeSlots(
      schedule.startTime,
      schedule.endTime,
      15, // 15-minute intervals
    );
    
    // 5. Filter out unavailable slots
    return slots.filter(slot => {
      const isInTimeOff = this.isSlotInTimeOff(slot, timeOffs);
      const isBooked = this.isSlotBooked(slot, bookings);
      const isInBreak = this.isSlotInBreak(slot, schedule);
      
      return !isInTimeOff && !isBooked && !isInBreak;
    });
  }
}
4. Payments Module
Payment Processing
typescript


// payments.service.ts
@Injectable()
export class PaymentsService {
  constructor(
    private stripeService: StripeService,
    private momoService: MomoService,
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
  ) {}
async processPayment( bookingId: number, paymentDto: ProcessPaymentDto, ): Promise<Payment> { const booking = await this.validateBooking(bookingId);
javascript


let paymentResult;
switch (paymentDto.method) {
  case PaymentMethod.STRIPE:
    paymentResult = await this.stripeService.processPayment({
      amount: paymentDto.amount,
      currency: 'VND',
      description: `Booking ${booking.bookingCode}`,
      metadata: { bookingId: booking.id },
    });
    break;
    
  case PaymentMethod.MOMO:
    paymentResult = await this.momoService.processPayment({
      amount: paymentDto.amount,
      orderId: booking.bookingCode,
      orderInfo: `Thanh toán booking ${booking.bookingCode}`,
      returnUrl: `${process.env.APP_URL}/payment/callback`,
    });
    break;
    
  default:
    throw new BadRequestException('Unsupported payment method');
}

const payment = this.paymentRepository.create({
  bookingId,
  amount: paymentDto.amount,
  method: paymentDto.method,
  type: paymentDto.type,
  transactionId: paymentResult.transactionId,
  status: PaymentStatus.PROCESSING,
  gatewayResponse: paymentResult.rawResponse,
});

return this.paymentRepository.save(payment);
} }
javascript



### 5. Notifications Module

#### Queue Processing
```typescript
// notification.processor.ts
@Processor('notifications')
export class NotificationProcessor {
  constructor(
    private emailService: EmailService,
    private smsService: SmsService,
    private notificationRepository: Repository<Notification>,
  ) {}
@Process('send-email') async handleEmailNotification(job: Job<EmailJobData>) { const { to, subject, template, data } = job.data;
javascript


try {
  await this.emailService.sendMail({
    to,
    subject,
    template,
    context: data,
  });
  
  await this.notificationRepository.update(job.data.notificationId, {
    status: NotificationStatus.SENT,
    sentAt: new Date(),
  });
} catch (error) {
  await this.notificationRepository.update(job.data.notificationId, {
    status: NotificationStatus.FAILED,
    errorMessage: error.message,
  });
  
  throw error; // Retry
}
}
@Process('send-sms') async handleSmsNotification(job: Job<SmsJobData>) { const { to, message } = job.data;
javascript


try {
  await this.smsService.sendSms(to, message);
  
  await this.notificationRepository.update(job.data.notificationId, {
    status: NotificationStatus.SENT,
    sentAt: new Date(),
  });
} catch (error) {
  await this.notificationRepository.update(job.data.notificationId, {
    status: NotificationStatus.FAILED,
    errorMessage: error.message,
  });
  
  throw error;
}
} }
javascript



## API Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2025-06-10T10:00:00.000Z",
    "version": "1.0.0"
  }
}
Error Response
json


{
  "success": false,
  "error": {
    "code": "BOOKING_NOT_AVAILABLE",
    "message": "The selected time slot is not available",
    "details": {
      "field": "startTime",
      "value": "14:00"
    }
  },
  "meta": {
    "timestamp": "2025-06-10T10:00:00.000Z",
    "version": "1.0.0"
  }
}
Pagination Response
json


{
  "success": true,
  "data": [
    // Array of items
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  },
  "meta": {
    "timestamp": "2025-06-10T10:00:00.000Z",
    "version": "1.0.0"
  }
}
Environment Configuration
env


# Application
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000
APP_NAME="Spa Booking System"
APP_VERSION=1.0.0
Database
DB_TYPE=mysql DB_HOST=localhost DB_PORT=3306 DB_USERNAME=spa_user DB_PASSWORD=secure_password DB_DATABASE=spa_booking_system DB_SYNCHRONIZE=false DB_LOGGING=true
Redis
REDIS_HOST=localhost REDIS_PORT=6379 REDIS_PASSWORD= REDIS_DB=0
JWT
JWT_SECRET=your-super-secret-jwt-key JWT_EXPIRES_IN=24h JWT_REFRESH_SECRET=your-super-secret-refresh-key JWT_REFRESH_EXPIRES_IN=7d
Bcrypt
BCRYPT_ROUNDS=10
Email (Gmail example)
MAIL_HOST=smtp.gmail.com MAIL_PORT=587 MAIL_USER=<EMAIL> MAIL_PASS=your-app-password MAIL_FROM='"Spa Booking" <EMAIL>' MAIL_SECURE=false
SMS (Twilio)
TWILIO_ACCOUNT_SID=your-account-sid TWILIO_AUTH_TOKEN=your-auth-token TWILIO_PHONE_NUMBER=+**********
Payment - Stripe
STRIPE_SECRET_KEY=sk_test_... STRIPE_WEBHOOK_SECRET=whsec_... STRIPE_CURRENCY=VND
Payment - MoMo
MOMO_PARTNER_CODE=your-partner-code MOMO_ACCESS_KEY=your-access-key MOMO_SECRET_KEY=your-secret-key MOMO_ENDPOINT=https://test-payment.momo.vn
Payment - PayPal
PAYPAL_CLIENT_ID=your-client-id PAYPAL_CLIENT_SECRET=your-client-secret PAYPAL_MODE=sandbox
Google Calendar
GOOGLE_CLIENT_ID=your-client-id GOOGLE_CLIENT_SECRET=your-client-secret GOOGLE_REDIRECT_URI=http://localhost:3000/api/integrations/google/callback
Microsoft Graph
MICROSOFT_CLIENT_ID=your-client-id MICROSOFT_CLIENT_SECRET=your-client-secret MICROSOFT_TENANT_ID=your-tenant-id MICROSOFT_REDIRECT_URI=http://localhost:3000/api/integrations/microsoft/callback
Rate Limiting
RATE_LIMIT_WINDOW=15 RATE_LIMIT_MAX=100
CORS
CORS_ORIGIN=http://localhost:3001,http://localhost:3002
Logging
LOG_LEVEL=debug LOG_FILE_PATH=./logs
javascript



## Development Commands

```bash
# Installation
npm install
Development
npm run start:dev
Build
npm run build
Production
npm run start:prod
Database
npm run migration:generate -- CreateBookingTable npm run migration:run npm run migration:revert
Testing
npm run test npm run test:watch npm run test:cov npm run test:e2e
Linting
npm run lint npm run format
Documentation
npm run docs:generate
javascript



## Testing Strategy

### Unit Testing
```typescript
// bookings.service.spec.ts
describe('BookingsService', () => {
  let service: BookingsService;
  let repository: Repository<Booking>;
beforeEach(async () => { const module: TestingModule = await Test.createTestingModule({ providers: [ BookingsService, { provide: getRepositoryToken(Booking), useClass: Repository, }, ], }).compile();
javascript


service = module.get<BookingsService>(BookingsService);
repository = module.get<Repository<Booking>>(getRepositoryToken(Booking));
});
describe('createBooking', () => { it('should create a booking successfully', async () => { const createBookingDto: CreateBookingDto = { customerId: 1, serviceId: 1, employeeId: 1, bookingDate: '2025-06-15', startTime: '14:00', };
javascript


  const mockBooking = {
    id: 1,
    ...createBookingDto,
    bookingCode: 'BK202506150001',
  };
javascript


  jest.spyOn(repository, 'create').mockReturnValue(mockBooking as any);
  jest.spyOn(repository, 'save').mockResolvedValue(mockBooking as any);
javascript


  const result = await service.createBooking(createBookingDto);
javascript


  expect(result).toEqual(mockBooking);
  expect(repository.create).toHaveBeenCalledWith(expect.objectContaining({
    ...createBookingDto,
    status: BookingStatus.PENDING,
  }));
});
}); });
javascript



### E2E Testing
```typescript
// bookings.e2e-spec.ts
describe('Bookings (e2e)', () => {
  let app: INestApplication;
beforeAll(async () => { const moduleFixture: TestingModule = await Test.createTestingModule({ imports: [AppModule], }).compile();
javascript


app = moduleFixture.createNestApplication();
await app.init();
});
describe('/api/bookings (POST)', () => { it('should create a booking', () => { return request(app.getHttpServer()) .post('/api/bookings') .set('Authorization', 'Bearer valid-jwt-token') .send({ customerId: 1, serviceId: 1, employeeId: 1, bookingDate: '2025-06-15', startTime: '14:00', }) .expect(201) .expect((res) => { expect(res.body.success).toBe(true); expect(res.body.data).toHaveProperty('bookingCode'); }); }); });
afterAll(async () => { await app.close(); }); });
javascript



## Security Best Practices

1. **Authentication & Authorization**
   - JWT tokens with short expiration
   - Refresh token rotation
   - Role-based access control
   - API key for third-party integrations

2. **Data Validation**
   - Input validation using class-validator
   - SQL injection prevention via TypeORM
   - XSS prevention
   - Request size limits

3. **Rate Limiting**
   - Global rate limiting
   - Per-endpoint rate limiting
   - IP-based blocking for abuse

4. **Encryption**
   - Passwords hashed with bcrypt
   - Sensitive data encrypted at rest
   - HTTPS enforcement
   - Secure cookie settings

5. **Logging & Monitoring**
   - Audit logs for critical actions
   - Error tracking with Sentry
   - Performance monitoring
   - Security event logging

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] SSL certificates installed
- [ ] Rate limiting configured
- [ ] Monitoring setup
- [ ] Backup strategy implemented
- [ ] Load balancer configured
- [ ] CDN for static assets
- [ ] Error tracking enabled
- [ ] Security headers configured
Hai file này đã được viết chi tiết với đầy đủ thông tin để Augment Code AI có thể hiểu và thực hiện từng bước trong quá trình phát triển hệ thống booking spa của bạn.