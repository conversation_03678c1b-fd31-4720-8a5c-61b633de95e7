/**
 * Database Connection
 * Sequelize instance configuration and connection management
 */

const { Sequelize } = require('sequelize');
const databaseConfig = require('../config/database');
const logger = require('../utils/logger');

// Get current environment configuration
const config = databaseConfig.current;

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: config.logging,
    pool: config.pool,
    timezone: config.timezone,
    define: config.define,
    dialectOptions: config.dialectOptions || {},
    retry: config.retry,
    
    // Additional options
    benchmark: process.env.NODE_ENV === 'development',
    transactionType: 'IMMEDIATE',
    isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    
    // Hooks for connection events
    hooks: {
      beforeConnect: async (config) => {
        logger.info('Attempting to connect to database...', {
          host: config.host,
          port: config.port,
          database: config.database
        });
      },
      
      afterConnect: async (connection, config) => {
        logger.info('Successfully connected to database', {
          host: config.host,
          port: config.port,
          database: config.database
        });
      },
      
      beforeDisconnect: async (connection) => {
        logger.info('Disconnecting from database...');
      },
      
      afterDisconnect: async (connection) => {
        logger.info('Successfully disconnected from database');
      }
    }
  }
);

// Connection test function
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully');
    return true;
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    return false;
  }
};

// Graceful shutdown function
const closeConnection = async () => {
  try {
    await sequelize.close();
    logger.info('Database connection closed successfully');
  } catch (error) {
    logger.error('Error closing database connection:', error);
  }
};

// Connection retry function
const connectWithRetry = async (maxRetries = 5, delay = 5000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await sequelize.authenticate();
      logger.info(`Database connected successfully on attempt ${i + 1}`);
      return true;
    } catch (error) {
      logger.warn(`Database connection attempt ${i + 1} failed:`, error.message);
      
      if (i === maxRetries - 1) {
        logger.error('Max database connection retries reached');
        throw error;
      }
      
      logger.info(`Retrying database connection in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

// Health check function
const healthCheck = async () => {
  try {
    await sequelize.query('SELECT 1');
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: config.database,
      host: config.host
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: config.database,
      host: config.host
    };
  }
};

// Get connection info
const getConnectionInfo = () => {
  return {
    database: config.database,
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    pool: {
      max: config.pool.max,
      min: config.pool.min,
      acquire: config.pool.acquire,
      idle: config.pool.idle
    },
    timezone: config.timezone
  };
};

// Export sequelize instance and utility functions
module.exports = {
  sequelize,
  testConnection,
  closeConnection,
  connectWithRetry,
  healthCheck,
  getConnectionInfo,
  
  // Sequelize classes for models
  Sequelize,
  DataTypes: Sequelize.DataTypes,
  Op: Sequelize.Op,
  Transaction: Sequelize.Transaction,
  
  // Current config
  config
};
