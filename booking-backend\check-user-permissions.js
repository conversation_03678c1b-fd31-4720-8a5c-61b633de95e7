const axios = require('axios');

async function checkUserPermissions() {
  try {
    console.log('🔍 Checking User Permissions...\n');
    
    // Login first
    const loginRes = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>', 
      password: 'admin123456'
    });
    
    const token = loginRes.data.data.token;
    const userInfo = loginRes.data.data.user;
    
    console.log('✅ Login successful');
    console.log('User Info:', {
      id: userInfo.id,
      email: userInfo.email,
      name: userInfo.name,
      role: userInfo.role
    });
    
    // Check services
    const servicesRes = await axios.get('http://localhost:3000/api/services', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    // Check branches  
    const branchesRes = await axios.get('http://localhost:3000/api/branches', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const services = servicesRes.data.data || [];
    const branches = branchesRes.data.data || [];
    
    console.log('\n📋 Permission Analysis:');
    console.log(`User ID: ${userInfo.id}`);
    
    console.log('\nBranches where user is manager:');
    const managedBranches = branches.filter(b => b.manager_id === userInfo.id);
    managedBranches.forEach(branch => {
      console.log(`  - ${branch.name} (ID: ${branch.id})`);
    });
    
    console.log('\nServices in managed branches:');
    const validServices = services.filter(s => 
      managedBranches.some(b => b.id === s.branch_id)
    );
    validServices.forEach(service => {
      console.log(`  - ${service.name} (ID: ${service.id}) in Branch ${service.branch_id}`);
    });
    
    if (validServices.length > 0 && managedBranches.length > 0) {
      const testService = validServices[0];
      const testBranch = managedBranches.find(b => b.id === testService.branch_id);
      
      console.log('\n✅ Recommended test data:');
      console.log(`  serviceId: ${testService.id}`);
      console.log(`  branchId: ${testBranch.id}`);
      
      // Test form creation with correct IDs
      console.log('\n🧪 Testing form creation with correct permissions...');
      
      const formData = {
        name: 'Permission Test Form - ' + Date.now(),
        serviceId: testService.id,
        branchId: testBranch.id,
        status: 'active',
        fieldsConfig: {
          customerName: true,
          phoneNumber: true,
          emailAddress: true,
          preferredDate: true,
          preferredTime: true,
          specialRequests: true
        },
        brandingConfig: {
          primaryColor: '#3b82f6',
          logo: null,
          customMessage: null
        }
      };
      
      try {
        const createResponse = await axios.post('http://localhost:3000/api/forms', formData, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
          console.log('🎉 SUCCESS! Form created successfully');
        console.log('Full API Response:', JSON.stringify(createResponse.data, null, 2));
        const form = createResponse.data.data;
        console.log('Form details:');
        console.log(`  - ID: ${form?.id || 'undefined'}`);
        console.log(`  - Name: ${form?.name || 'undefined'}`);
        console.log(`  - Slug: ${form?.slug || 'undefined'}`);
        console.log(`  - Public URL: ${form?.publicUrl || '❌ MISSING'}`);
        console.log(`  - Embed Code: ${form?.embedCode ? '✅ Present' : '❌ MISSING'}`);
        
        if (form.publicUrl && form.embedCode) {
          console.log('\n🎯 THE ISSUE IS FIXED! Both publicUrl and embedCode are present');
          console.log(`Public URL: ${form.publicUrl}`);
          console.log(`Embed Code: ${form.embedCode.substring(0, 100)}...`);
        } else {
          console.log('\n❌ Issue persists: Missing publicUrl or embedCode');
        }
        
      } catch (createError) {
        console.log('❌ Form creation still failed:', createError.response?.data?.message || createError.message);
      }
      
    } else {
      console.log('\n❌ No valid service-branch combinations found');
      console.log('💡 You need to assign this user as manager of a branch that has services');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

checkUserPermissions();
