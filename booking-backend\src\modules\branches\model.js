/**
 * Branch Model
 * Spa branch/location management
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../../database/connection');

const Branch = sequelize.define('Branch', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [2, 100],
      notEmpty: true
    },
    comment: 'Branch name'
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      len: [10, 500],
      notEmpty: true
    },
    comment: 'Full address'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      is: /^(0|\+84)[0-9]{9,10}$/
    },
    comment: 'Branch phone number'
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    validate: {
      isEmail: true,
      len: [5, 100]
    },
    comment: 'Branch email address'
  },
  city: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      len: [2, 50],
      notEmpty: true
    },
    comment: 'City name'
  },
  district: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [2, 50]
    },
    comment: 'District name'
  },
  ward: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [2, 50]
    },
    comment: 'Ward name'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true,
    validate: {
      min: -90,
      max: 90
    },
    comment: 'GPS latitude'
  },
  longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true,
    validate: {
      min: -180,
      max: 180
    },
    comment: 'GPS longitude'
  },
  openTime: {
    type: DataTypes.TIME,
    allowNull: false,
    defaultValue: '08:00:00',
    field: 'open_time',
    comment: 'Opening time'
  },
  closeTime: {
    type: DataTypes.TIME,
    allowNull: false,
    defaultValue: '20:00:00',
    field: 'close_time',
    comment: 'Closing time'
  },
  workingDays: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    field: 'working_days',
    comment: 'Working days of the week'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'is_active',
    comment: 'Branch status'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Branch description'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Branch images URLs'
  },
  managerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'manager_id',
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Branch manager user ID'
  }
}, {
  tableName: 'branches',
  timestamps: true,
  underscored: true,
  paranoid: true, // Soft delete
  
  // Indexes
  indexes: [
    { fields: ['name'] },
    { fields: ['city'] },
    { fields: ['is_active'] },
    { fields: ['manager_id'] },
    { fields: ['created_at'] }
  ],
  
  // Scopes
  scopes: {
    active: {
      where: { isActive: true }
    },
    withLocation: {
      where: {
        latitude: { [sequelize.Sequelize.Op.ne]: null },
        longitude: { [sequelize.Sequelize.Op.ne]: null }
      }
    }
  }
});

// Instance methods
Branch.prototype.isOpen = function(time = new Date()) {
  const currentTime = time.toTimeString().slice(0, 5); // HH:MM format
  const currentDay = time.toLocaleDateString('en-US', { weekday: 'lowercase' });
  
  return this.workingDays.includes(currentDay) && 
         currentTime >= this.openTime && 
         currentTime <= this.closeTime;
};

Branch.prototype.getWorkingHours = function() {
  return {
    openTime: this.openTime,
    closeTime: this.closeTime,
    workingDays: this.workingDays
  };
};

// Class methods
Branch.getActiveBranches = function(options = {}) {
  return this.scope('active').findAll(options);
};

Branch.findByCity = function(city, options = {}) {
  return this.scope('active').findAll({
    where: { city },
    ...options
  });
};

Branch.findNearby = function(latitude, longitude, radiusKm = 10, options = {}) {
  const { Op } = require('sequelize');
  
  // Simple bounding box calculation (not precise but fast)
  const latDelta = radiusKm / 111; // Roughly 111 km per degree of latitude
  const lonDelta = radiusKm / (111 * Math.cos(latitude * Math.PI / 180));
  
  return this.scope(['active', 'withLocation']).findAll({
    where: {
      latitude: {
        [Op.between]: [latitude - latDelta, latitude + latDelta]
      },
      longitude: {
        [Op.between]: [longitude - lonDelta, longitude + lonDelta]
      }
    },
    ...options
  });
};

// Define associations
Branch.associate = (models) => {
  // Branch belongs to User (manager)
  Branch.belongsTo(models.users, {
    foreignKey: 'manager_id',
    as: 'manager',
    allowNull: true
  });

  // Branch has many Users (employees)
  Branch.hasMany(models.users, {
    foreignKey: 'branch_id',
    as: 'employees'
  });

  // Branch has many Bookings
  Branch.hasMany(models.bookings, {
    foreignKey: 'branch_id',
    as: 'bookings'
  });

  // Branch has many Services
  Branch.hasMany(models.services, {
    foreignKey: 'branch_id',
    as: 'services'
  });
};

module.exports = Branch;
