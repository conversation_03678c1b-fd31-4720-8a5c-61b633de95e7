/**
 * Authentication Context
 * Provides global authentication state and methods
 */

'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import authService from '@/services/authService';
import { tokenManager } from '@/lib/api';

// Create context
const AuthContext = createContext({});

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// AuthProvider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      console.log('🔄 AuthContext: Starting initializeAuth...');
      setIsLoading(true);

      // Check if user is authenticated from localStorage
      const isAuth = authService.isAuthenticated();
      const currentUser = authService.getCurrentUser();
      const token = tokenManager.getToken();

      console.log('🔍 AuthContext: Authentication check results:', {
        isAuth,
        hasCurrentUser: !!currentUser,
        hasToken: !!token,
        currentUser: currentUser ? { id: currentUser.id, email: currentUser.email } : null
      });

      if (isAuth && currentUser && token) {
        console.log('✅ AuthContext: User authenticated, setting state...');
        // Set user immediately from localStorage for faster UI response
        setUser(currentUser);
        setIsAuthenticated(true);

        // Background refresh of user profile (non-blocking)
        refreshUserProfileInBackground();
      } else {
        console.log('❌ AuthContext: User not authenticated, clearing state...');
        console.log('  - isAuth:', isAuth);
        console.log('  - currentUser:', !!currentUser);
        console.log('  - token:', !!token);

        // Clear any stale data
        await handleLogout();
      }
    } catch (error) {
      console.error('❌ AuthContext: Auth initialization error:', error);
      await handleLogout();
    } finally {
      console.log('🏁 AuthContext: initializeAuth completed');
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  // Background profile refresh (non-blocking)
  const refreshUserProfileInBackground = async () => {
    try {
      const profileResponse = await authService.getProfile();
      if (profileResponse.success) {
        setUser(profileResponse.user);
        tokenManager.setUser(profileResponse.user);
      }
    } catch (error) {
      console.error('Background profile refresh failed:', error);
      // Only logout if it's a 401 error (unauthorized)
      if (error.status === 401) {
        await handleLogout();
      }
      // For other errors (network, etc.), keep user logged in with cached data
    }
  };

  // Login function
  const login = async (credentials) => {
    try {
      setIsLoading(true);
      const response = await authService.login(credentials);
      
      if (response.success) {
        setUser(response.user);
        setIsAuthenticated(true);
        return response;
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setIsLoading(true);
      const response = await authService.register(userData);
      
      if (response.success) {
        setUser(response.user);
        setIsAuthenticated(true);
        return response;
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      await handleLogout();
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      await handleLogout();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle logout (clear state and redirect)
  const handleLogout = async () => {
    console.log('🔄 AuthContext: handleLogout called');
    setUser(null);
    setIsAuthenticated(false);
    tokenManager.removeTokens();

    // Only redirect if we're not already on login/register pages
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      console.log('🔍 AuthContext: Current path for logout redirect:', currentPath);
      if (currentPath !== '/login' && currentPath !== '/register' && currentPath !== '/') {
        console.log('🔄 AuthContext: Redirecting to login...');
        router.push('/login');
      } else {
        console.log('🔍 AuthContext: Already on public page, no redirect needed');
      }
    }
  };

  // Update user profile
  const updateUser = (updatedUser) => {
    setUser(updatedUser);
    tokenManager.setUser(updatedUser);
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return user?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  // Refresh user profile
  const refreshProfile = async () => {
    try {
      const response = await authService.getProfile();
      if (response.success) {
        setUser(response.user);
        return response.user;
      }
    } catch (error) {
      console.error('Failed to refresh profile:', error);
      if (error.status === 401) {
        await handleLogout();
      }
      throw error;
    }
  };

  // Context value
  const value = {
    // State
    user,
    isLoading,
    isAuthenticated,
    isInitialized,

    // Methods
    login,
    register,
    logout,
    updateUser,
    refreshProfile,

    // Utility methods
    hasRole,
    hasAnyRole,

    // Auth service methods (for direct access if needed)
    authService,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
