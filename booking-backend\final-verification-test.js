const axios = require('axios');

// Final verification test for the forms functionality
async function finalVerification() {
  console.log('🏁 FINAL VERIFICATION TEST - Forms Functionality\n');
  console.log('=' .repeat(60));
  
  const BASE_URL = 'http://localhost:3000';
  const FRONTEND_URL = 'http://localhost:4000';
  
  try {
    // 1. Test Backend Connection
    console.log('\n1️⃣ Testing Backend Connection...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`).catch(() => null);
    if (healthResponse && healthResponse.status === 200) {
      console.log('✅ Backend is running on ' + BASE_URL);
    } else {
      console.log('❌ Backend is not responding');
      return;
    }
    
    // 2. Test Authentication
    console.log('\n2️⃣ Testing Authentication...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Authentication successful');
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
    } else {
      console.log('❌ Authentication failed');
      return;
    }
    
    const token = loginResponse.data.data.token;
    
    // 3. Test Form Creation
    console.log('\n3️⃣ Testing Form Creation...');
    const timestamp = Date.now();
    const formData = {
      name: `Verification Test Form - ${timestamp}`,
      serviceId: 9,
      branchId: 5,
      status: 'active',
      fieldsConfig: {
        customerName: true,
        phoneNumber: true,
        emailAddress: true,
        preferredDate: true,
        preferredTime: true,
        specialRequests: true
      },
      brandingConfig: {
        primaryColor: '#3b82f6',
        logo: null,
        customMessage: 'Welcome to our booking system!'
      }
    };
    
    const createResponse = await axios.post(`${BASE_URL}/api/forms`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (createResponse.status === 201 && createResponse.data.success) {
      console.log('✅ Form created successfully');
      const form = createResponse.data.data;
      
      // 4. Verify Required Fields
      console.log('\n4️⃣ Verifying Form Data...');
      const checks = [
        { field: 'id', value: form.id, expected: 'number' },
        { field: 'name', value: form.name, expected: 'string' },
        { field: 'slug', value: form.slug, expected: 'string' },
        { field: 'publicUrl', value: form.publicUrl, expected: 'string' },
        { field: 'embedCode', value: form.embedCode, expected: 'string' },
        { field: 'service', value: form.service, expected: 'object' },
        { field: 'branch', value: form.branch, expected: 'object' }
      ];
      
      let allChecksPass = true;
      checks.forEach(check => {
        const actualType = typeof check.value;
        const exists = check.value !== null && check.value !== undefined;
        
        if (exists && (actualType === check.expected || (check.expected === 'object' && check.value !== null))) {
          console.log(`   ✅ ${check.field}: ${exists ? '✓' : '✗'} (${actualType})`);
        } else {
          console.log(`   ❌ ${check.field}: ${exists ? '✗' : '✗'} (${actualType})`);
          allChecksPass = false;
        }
      });
      
      // 5. Verify URL Structure
      console.log('\n5️⃣ Verifying URL Structure...');
      if (form.publicUrl && form.publicUrl.includes(form.slug)) {
        console.log('✅ Public URL contains correct slug');
        console.log(`   URL: ${form.publicUrl}`);
      } else {
        console.log('❌ Public URL structure incorrect');
        allChecksPass = false;
      }
      
      // 6. Verify Embed Code
      console.log('\n6️⃣ Verifying Embed Code...');
      if (form.embedCode && form.embedCode.includes('<iframe') && form.embedCode.includes(form.slug)) {
        console.log('✅ Embed code is properly formatted iframe');
        console.log(`   Preview: ${form.embedCode.substring(0, 80)}...`);
      } else {
        console.log('❌ Embed code format incorrect');
        allChecksPass = false;
      }
      
      // 7. Test Environment Variable
      console.log('\n7️⃣ Checking Environment Configuration...');
      // Since we can't access the backend's env from here, we'll check the URLs
      if (form.publicUrl.includes('localhost:4000')) {
        console.log('✅ FRONTEND_URL environment variable is working');
        console.log('   Frontend URL base: localhost:4000');
      } else {
        console.log('❌ FRONTEND_URL environment variable may not be set correctly');
      }
      
      // 8. Final Results
      console.log('\n' + '=' .repeat(60));
      console.log('🏁 FINAL VERIFICATION RESULTS');
      console.log('=' .repeat(60));
      
      if (allChecksPass) {
        console.log('🎉 ALL TESTS PASSED! Forms functionality is WORKING CORRECTLY!');
        console.log('\n✅ Backend API: Working');
        console.log('✅ Authentication: Working'); 
        console.log('✅ Form Creation: Working');
        console.log('✅ Public URL Generation: Working');
        console.log('✅ Embed Code Generation: Working');
        console.log('✅ Environment Configuration: Working');
        
        console.log('\n📋 SAMPLE DATA:');
        console.log(`   Form ID: ${form.id}`);
        console.log(`   Form Name: ${form.name}`);
        console.log(`   Slug: ${form.slug}`);
        console.log(`   Public URL: ${form.publicUrl}`);
        console.log(`   Service: ${form.service.name} (${form.service.duration} mins)`);
        console.log(`   Branch: ${form.branch.name}`);
        
        console.log('\n🚀 Ready for Production Use!');
      } else {
        console.log('❌ Some tests failed. Please review the issues above.');
      }
      
    } else {
      console.log('❌ Form creation failed');
      console.log('Response:', createResponse.data);
    }
    
  } catch (error) {
    console.error('\n❌ Verification failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the verification
finalVerification();
